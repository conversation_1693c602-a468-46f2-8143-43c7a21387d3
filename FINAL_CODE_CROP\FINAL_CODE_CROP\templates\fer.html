{% extends 'layout.html' %} {% block content %}

<div class="container mt-5">
    <div class="row">
        <!-- Left side - Fertilizer Image and Information -->
        <div class="col-lg-6">
            <div class="info-section">
                <img src="/static/images/ferti.jpeg" alt="Fertilizer" class="fertilizer-image img-fluid rounded shadow-lg mb-4">
                
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">How Fertilizers Help Your Crops</h5>
                    </div>
                    <div class="card-body">
                        <ol class="fertilizer-benefits">
                            <li><strong>Boosts Plant Growth:</strong> Provides essential nutrients for faster development</li>
                            <li><strong>Increases Crop Yield:</strong> Can improve harvest by up to 50-70%</li>
                            <li><strong>Enhances Root Development:</strong> Strengthens root systems for better nutrient absorption</li>
                            <li><strong>Improves Soil Fertility:</strong> Replenishes depleted soil nutrients over time</li>
                            <li><strong>Better Disease Resistance:</strong> Well-fed plants are more resistant to diseases</li>
                            <li><strong>Faster Maturation:</strong> Reduces growing time by 15-20%</li>
                            <li><strong>Quality Enhancement:</strong> Improves fruit size, color, and nutritional value</li>
                            <li><strong>Economic Benefits:</strong> Higher yields mean increased farmer income</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right side - Form -->
        <div class="col-lg-6">
            <div class="card shadow-lg">
                <div class="card-header bg-warning text-dark text-center">
                    <h3 class="mb-0">Fertilizer Advisor</h3>
                    <small>Get personalized fertilizer recommendations</small>
                </div>
                <div class="card-body p-4">
                    <form action="{{ url_for('fer_predict')}}" method="post">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><b>1. Temperature (°C):</b></label>
                                <input type="number" name="temp" class="form-control" placeholder="Enter temperature (25-38)" min="25" max="38" step="0.1" value="{{temp}}" required />
                                <small class="text-muted">Range: 25-38°C</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><b>2. Humidity (%):</b></label>
                                <input type="number" name="humid" class="form-control" placeholder="Enter humidity (50-72)" value="{{hum}}" min="50" max="72" step="0.1" required />
                                <small class="text-muted">Range: 50-72%</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><b>3. Moisture (%):</b></label>
                                <input type="number" name="mois" class="form-control" placeholder="Enter moisture (25-65)" value="{{moi}}" min="25" max="65" step="0.1" required />
                                <small class="text-muted"> Range: 25-65%</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><b>4. Soil Type:</b></label>
                                <select name="soil" class="form-select">
                                    <option value=0> Black Soil</option>
                                    <option value=1>Clayey Soil</option>
                                    <option value=2>Loamy Soil</option>
                                    <option value=3>Red Soil</option>
                                    <option value=4>Sandy Soil</option>
                                </select>
                                <small class="text-muted"> Values: 0, 1, 2, 3, 4</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><b>5. Crop Type:</b></label>
                                <select name="crop" class="form-select">
                                    <option value=0> Barley</option>
                                    <option value=1>Cotton</option>
                                    <option value=2>Ground Nuts</option>
                                    <option value=3>Maize</option>
                                    <option value=4>Millets</option>
                                    <option value=5>Oil Seeds</option>
                                    <option value=6>Paddy</option>
                                    <option value=7>7 - Pulses</option>
                                    <option value=8>Sugarcane</option>
                                    <option value=9>Tobacco</option>
                                    <option value=10>Wheat</option>
                                </select>
                                <small class="text-muted"> Values: 0-10</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><b>6. Nitrogen (N):</b></label>
                                <input type="number" name="nitro" class="form-control" placeholder="Enter nitrogen (4-42)" min="4" max="42" step="1" value="{{n}}" required />
                                <small class="text-muted"> Range: 4-42 kg/ha</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><b>7. Potassium (K):</b></label>
                                <input type="number" name="pota" class="form-control" placeholder="Enter potassium (0-19)" min="0" max="19" step="1" value="{{k}}" required />
                                <small class="text-muted">Range: 0-19 kg/ha</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><b>8. Phosphorous (P):</b></label>
                                <input type="number" name="phos" class="form-control" placeholder="Enter phosphorous (0-42)" min="0" max="42" step="1" value="{{p}}" required />
                                <small class="text-muted"> Range: 0-42 kg/ha</small>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-warning btn-lg w-100">
                                Submit
                            </button>
                        </div>
                        
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 15px;
}
.card-header {
    border-radius: 15px 15px 0 0 !important;
}
.fertilizer-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
}
.fertilizer-benefits {
    padding-left: 1.2rem;
    margin-bottom: 0;
}
.fertilizer-benefits li {
    margin-bottom: 0.8rem;
    font-size: 0.95rem;
    line-height: 1.4;
}
.fertilizer-benefits li strong {
    color: #28a745;
}
.info-section {
    padding: 1rem;
}
@media (max-width: 991px) {
    .info-section {
        margin-bottom: 2rem;
    }
    .fertilizer-image {
        height: 180px;
    }
}
@media (max-width: 576px) {
    .fertilizer-image {
        height: 150px;
    }
}
</style>

{% endblock %}