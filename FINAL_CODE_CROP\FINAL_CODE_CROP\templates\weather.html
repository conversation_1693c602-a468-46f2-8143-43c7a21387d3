{% extends 'layout.html' %}

{% block title %}Weather - AgroPro{% endblock %}

{% block content %}
<div class='container mt-5 pt-5'>
    <div class='row'>
        <div class='col-12'>
            <h1 class='text-center mb-4'>Weather Information</h1>
        </div>
    </div>
    
    <div class='row justify-content-center'>
        <div class='col-md-6'>
            <div class='card'>
                <div class='card-header bg-primary text-white'>
                    <h4 class='mb-0'>Get Weather Data</h4>
                </div>
                <div class='card-body'>
                    <form method='POST' action='/weather'>
                        <div class='mb-3'>
                            <label for='city_select' class='form-label'>Choose City:</label>
                            <select class='form-select' id='city_select' name='city_select' required>
                                <option value=''>-- Select a City --</option>
                                <option value='Delhi'>Delhi</option>
                                <option value='Mumbai'>Mumbai</option>
                                <option value='Bangalore'>Bangalore</option>
                                <option value='Chennai'>Chennai</option>
                                <option value='Kolkata'>Kolkata</option>
                                <option value='Hyderabad'>Hyderabad</option>
                                <option value='Pune'>Pune</option>
                                <option value='Ahmedabad'>Ahmedabad</option>
                                <option value='Jaipur'>Jaipur</option>
                                <option value='Lucknow'>Lucknow</option>
                            </select>
                        </div>
                        <div class='d-grid'>
                            <button type='submit' class='btn btn-primary'>Get Weather Report</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if city %}
    <div class='row justify-content-center mt-4'>
        <div class='col-md-8'>
            <div class='card'>
                <div class='card-header bg-success text-white'>
                    <h4 class='mb-0'>Weather Report for {{ city }}</h4>
                </div>
                <div class='card-body'>
                    <div class='row'>
                        <div class='col-md-6'>
                            <p><strong>Temperature:</strong> {{ temp if temp else 'N/A' }}</p>
                            <p><strong>Weather:</strong> {{ sky if sky else 'N/A' }}</p>
                        </div>
                        <div class='col-md-6'>
                            <p><strong>Humidity:</strong> {{ humidity if humidity else 'N/A' }}</p>
                            <p><strong>Wind Speed:</strong> {{ wind_speed if wind_speed else 'N/A' }}</p>
                        </div>
                    </div>
                    {% if fallback_notice %}
                    <div class='alert alert-info mt-3'>
                        <strong>Note:</strong> Using estimated weather data.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if msg %}
    <div class='row justify-content-center mt-4'>
        <div class='col-md-8'>
            <div class='alert alert-warning'>
                {{ msg }}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
