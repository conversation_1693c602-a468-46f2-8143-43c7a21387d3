<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Expense - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .category-icon {
            font-size: 1.2em;
            margin-right: 8px;
        }
        .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-seedling me-2"></i>AgroPro - Add Expense
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/expenses"><i class="fas fa-dashboard me-1"></i>Dashboard</a>
                <a class="nav-link" href="/view_expenses"><i class="fas fa-list me-1"></i>View All</a>
                <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Home</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-5 fw-bold text-success">
                        <i class="fas fa-plus-circle me-3"></i>Add New Expense
                    </h1>
                    <p class="lead text-muted">Track your agricultural expenses efficiently</p>
                </div>

                <!-- Success/Error Messages -->
                {% if error %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}

                <!-- Add Expense Form -->
                <div class="card form-card">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-receipt me-2"></i>Expense Details</h4>
                    </div>
                    <div class="card-body p-4">
                        <form method="POST" action="/add_expense">
                            <div class="row">
                                <!-- Date -->
                                <div class="col-md-6 mb-3">
                                    <label for="date" class="form-label">
                                        <i class="fas fa-calendar text-primary me-1"></i>Date
                                    </label>
                                    <input type="date" class="form-control" id="date" name="date" 
                                           value="{{ request.form.get('date', '') }}" required>
                                </div>

                                <!-- Amount -->
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">
                                        <i class="fas fa-rupee-sign text-success me-1"></i>Amount (₹)
                                    </label>
                                    <input type="number" step="0.01" class="form-control" id="amount" name="amount" 
                                           placeholder="0.00" value="{{ request.form.get('amount', '') }}" required>
                                </div>
                            </div>

                            <!-- Category -->
                            <div class="mb-3">
                                <label for="category" class="form-label">
                                    <i class="fas fa-tags text-info me-1"></i>Category
                                </label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="Seeds" {{ 'selected' if request.form.get('category') == 'Seeds' }}>
                                        <i class="fas fa-seedling category-icon"></i>Seeds
                                    </option>
                                    <option value="Fertilizers" {{ 'selected' if request.form.get('category') == 'Fertilizers' }}>
                                        <i class="fas fa-leaf category-icon"></i>Fertilizers
                                    </option>
                                    <option value="Labor" {{ 'selected' if request.form.get('category') == 'Labor' }}>
                                        <i class="fas fa-users category-icon"></i>Labor
                                    </option>
                                    <option value="Pesticides" {{ 'selected' if request.form.get('category') == 'Pesticides' }}>
                                        <i class="fas fa-spray-can category-icon"></i>Pesticides
                                    </option>
                                    <option value="Irrigation" {{ 'selected' if request.form.get('category') == 'Irrigation' }}>
                                        <i class="fas fa-tint category-icon"></i>Irrigation
                                    </option>
                                    <option value="Equipment" {{ 'selected' if request.form.get('category') == 'Equipment' }}>
                                        <i class="fas fa-tools category-icon"></i>Equipment
                                    </option>
                                    <option value="Transport" {{ 'selected' if request.form.get('category') == 'Transport' }}>
                                        <i class="fas fa-truck category-icon"></i>Transport
                                    </option>
                                    <option value="Packaging" {{ 'selected' if request.form.get('category') == 'Packaging' }}>
                                        <i class="fas fa-box category-icon"></i>Packaging
                                    </option>
                                    <option value="Fuel" {{ 'selected' if request.form.get('category') == 'Fuel' }}>
                                        <i class="fas fa-gas-pump category-icon"></i>Fuel
                                    </option>
                                    <option value="Maintenance" {{ 'selected' if request.form.get('category') == 'Maintenance' }}>
                                        <i class="fas fa-wrench category-icon"></i>Maintenance
                                    </option>
                                    <option value="Insurance" {{ 'selected' if request.form.get('category') == 'Insurance' }}>
                                        <i class="fas fa-shield-alt category-icon"></i>Insurance
                                    </option>
                                    <option value="Others" {{ 'selected' if request.form.get('category') == 'Others' }}>
                                        <i class="fas fa-ellipsis-h category-icon"></i>Others
                                    </option>
                                </select>
                            </div>

                            <!-- Description -->
                            <div class="mb-4">
                                <label for="description" class="form-label">
                                    <i class="fas fa-comment text-warning me-1"></i>Description
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="Enter details about this expense (optional)">{{ request.form.get('description', '') }}</textarea>
                                <div class="form-text">Provide additional details to help track your expenses better</div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/expenses" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-2"></i>Save Expense
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Tips -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Expense Tracking Tips</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Record expenses immediately to avoid forgetting
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Keep receipts for verification and tax purposes
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Use specific descriptions for better tracking
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Categorize expenses properly for analysis
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Review monthly expenses to identify patterns
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Set budgets for different categories
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        // Initialize Socket.IO for real-time submission
        const socket = io();
        let isSubmitting = false;

        // Connection status
        socket.on('connect', function() {
            console.log('Connected to real-time expense system');
            showNotification('Connected to real-time system', 'success');
        });

        socket.on('disconnect', function() {
            showNotification('Connection lost - form will use standard submission', 'warning');
        });

        // Real-time responses
        socket.on('success', function(data) {
            showNotification(data.message, 'success');
            resetForm();
            isSubmitting = false;
            updateSubmitButton(false);
        });

        socket.on('error', function(data) {
            showNotification(data.message, 'danger');
            isSubmitting = false;
            updateSubmitButton(false);
        });

        // Set today's date as default
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.getElementById('date');
            if (!dateInput.value) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.value = today;
            }
        });

        // Real-time form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();

            if (isSubmitting) return;

            const formData = {
                date: document.getElementById('date').value,
                category: document.getElementById('category').value,
                amount: document.getElementById('amount').value,
                description: document.getElementById('description').value
            };

            // Validate form
            if (!validateForm(formData)) {
                return false;
            }

            // Submit via Socket.IO for real-time updates
            if (socket.connected) {
                isSubmitting = true;
                updateSubmitButton(true);
                socket.emit('add_expense_realtime', formData);
                showNotification('Adding expense...', 'info');
            } else {
                // Fallback to regular form submission
                this.submit();
            }
        });

        function validateForm(data) {
            if (!data.date) {
                showNotification('Please select a date', 'danger');
                return false;
            }

            if (!data.category) {
                showNotification('Please select a category', 'danger');
                return false;
            }

            if (!data.amount || parseFloat(data.amount) <= 0) {
                showNotification('Please enter a valid amount greater than 0', 'danger');
                return false;
            }

            return true;
        }

        function resetForm() {
            document.getElementById('amount').value = '';
            document.getElementById('description').value = '';
            // Keep date and category for convenience
        }

        function updateSubmitButton(loading) {
            const button = document.querySelector('button[type="submit"]');
            if (loading) {
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding...';
                button.disabled = true;
            } else {
                button.innerHTML = '<i class="fas fa-save me-2"></i>Save Expense';
                button.disabled = false;
            }
        }

        function showNotification(message, type) {
            // Remove existing notifications
            const existing = document.querySelectorAll('.notification-alert');
            existing.forEach(el => el.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed notification-alert`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Auto-focus on amount field after category selection
        document.getElementById('category').addEventListener('change', function() {
            if (this.value) {
                document.getElementById('amount').focus();
            }
        });

        // Real-time amount formatting
        document.getElementById('amount').addEventListener('input', function() {
            const value = parseFloat(this.value);
            if (value && value > 0) {
                this.style.borderColor = '#28a745';
            } else {
                this.style.borderColor = '';
            }
        });

        // Quick amount buttons
        function addQuickAmountButtons() {
            const amountInput = document.getElementById('amount');
            const quickAmounts = [100, 500, 1000, 2000, 5000];

            const buttonContainer = document.createElement('div');
            buttonContainer.className = 'mt-2';
            buttonContainer.innerHTML = '<small class="text-muted">Quick amounts: </small>';

            quickAmounts.forEach(amount => {
                const btn = document.createElement('button');
                btn.type = 'button';
                btn.className = 'btn btn-outline-secondary btn-sm me-1';
                btn.textContent = '₹' + amount;
                btn.onclick = () => {
                    amountInput.value = amount;
                    amountInput.style.borderColor = '#28a745';
                    amountInput.focus();
                };
                buttonContainer.appendChild(btn);
            });

            amountInput.parentNode.appendChild(buttonContainer);
        }

        // Add quick amount buttons on page load
        document.addEventListener('DOMContentLoaded', addQuickAmountButtons);
    </script>
</body>
</html>
