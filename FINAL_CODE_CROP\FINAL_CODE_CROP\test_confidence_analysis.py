import requests
import numpy as np

def test_confidence_scenarios():
    """Test different input scenarios to understand confidence patterns"""
    
    test_cases = [
        {
            'name': 'Ideal Rice Conditions',
            'data': {
                'nitrogen': 80, 'phosphorous': 40, 'pottasium': 40,
                'temp': 25.0, 'hum': 80.0, 'ph': 6.5, 'rainfall': 200.0
            }
        },
        {
            'name': 'Ideal Wheat Conditions', 
            'data': {
                'nitrogen': 50, 'phosphorous': 30, 'pottasium': 30,
                'temp': 15.0, 'hum': 60.0, 'ph': 7.0, 'rainfall': 50.0
            }
        },
        {
            'name': 'Borderline Conditions (Mixed)',
            'data': {
                'nitrogen': 70, 'phosphorous': 35, 'pottasium': 35,
                'temp': 20.0, 'hum': 70.0, 'ph': 6.8, 'rainfall': 100.0
            }
        },
        {
            'name': 'Extreme Values',
            'data': {
                'nitrogen': 140, 'phosphorous': 145, 'pottasium': 205,
                'temp': 44.0, 'hum': 100.0, 'ph': 10.0, 'rainfall': 300.0
            }
        },
        {
            'name': 'Your Current Input (if causing low confidence)',
            'data': {
                'nitrogen': 90, 'phosphorous': 42, 'pottasium': 43,
                'temp': 20.9, 'hum': 82.0, 'ph': 6.5, 'rainfall': 202.9
            }
        }
    ]
    
    print("🔍 Testing confidence levels for different input scenarios...")
    print("=" * 80)
    
    for case in test_cases:
        try:
            response = requests.post('http://127.0.0.1:5000/crop_predict', 
                                   data=case['data'], 
                                   timeout=10)
            
            if response.status_code == 200:
                # Extract confidence and crop
                import re
                confidence_match = re.search(r'(\d+\.?\d*)%', response.text)
                crop_match = re.search(r'class="crop-name"[^>]*>([^<]+)', response.text)
                
                if confidence_match and crop_match:
                    confidence = float(confidence_match.group(1))
                    crop = crop_match.group(1).strip()
                    
                    # Determine confidence level
                    if confidence >= 75:
                        level = "🟢 HIGH"
                        status = "Highly Recommended"
                    elif confidence >= 50:
                        level = "🟡 MEDIUM" 
                        status = "Moderate Match"
                    else:
                        level = "🔴 LOW"
                        status = "Consider Alternatives"
                    
                    print(f"\n📊 {case['name']}:")
                    print(f"   Input: N={case['data']['nitrogen']}, P={case['data']['phosphorous']}, K={case['data']['pottasium']}")
                    print(f"          T={case['data']['temp']}°C, H={case['data']['hum']}%, pH={case['data']['ph']}, R={case['data']['rainfall']}mm")
                    print(f"   Result: {crop} ({confidence}%) {level} - {status}")
                else:
                    print(f"\n❌ {case['name']}: Could not extract confidence")
            else:
                print(f"\n❌ {case['name']}: Request failed ({response.status_code})")
                
        except Exception as e:
            print(f"\n❌ Error testing {case['name']}: {e}")
    
    print("\n" + "=" * 80)
    print("📋 Analysis Guide:")
    print("🟢 HIGH (≥75%): Model is very confident in the recommendation")
    print("🟡 MEDIUM (50-74%): Model is moderately confident, good match")
    print("🔴 LOW (<50%): Model is uncertain, input conditions may be:")
    print("   • Borderline between multiple crops")
    print("   • Outside typical training data ranges")
    print("   • Suitable for multiple crop types")
    print("\n💡 Recommendation: For low confidence, consider:")
    print("   • Check if input values are realistic for your region")
    print("   • Try slightly different parameter values")
    print("   • Consider growing multiple recommended crops")

if __name__ == "__main__":
    test_confidence_scenarios()
