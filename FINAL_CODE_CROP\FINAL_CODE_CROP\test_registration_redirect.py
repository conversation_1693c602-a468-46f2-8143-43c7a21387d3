import requests
import sys

def test_registration_redirect():
    """Test that registration redirects to login page with success message"""
    
    # Test data
    test_user = {
        'name': 'testuser123',
        'email': '<EMAIL>', 
        'phone': '1234567890',
        'password': 'testpass123'
    }
    
    try:
        # Send POST request to registration endpoint
        response = requests.post(
            'http://localhost:5000/userreg',
            data=test_user,
            allow_redirects=False  # Don't follow redirects automatically
        )
        
        print(f"Registration response status: {response.status_code}")
        
        if response.status_code == 302:  # Redirect status
            location = response.headers.get('Location', '')
            print(f"Redirect location: {location}")
            
            if '/userlog' in location and 'msg=' in location:
                print("✅ SUCCESS: Registration properly redirects to login page with message")
                return True
            else:
                print("❌ FAILED: Redirect location is not correct")
                return False
        else:
            print(f"❌ FAILED: Expected redirect (302) but got {response.status_code}")
            print(f"Response content: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ FAILED: Could not connect to Flask server. Make sure it's running on http://localhost:5000")
        return False
    except Exception as e:
        print(f"❌ FAILED: Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_registration_redirect()
    sys.exit(0 if success else 1)
