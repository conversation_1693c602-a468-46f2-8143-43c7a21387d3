"""
Enhanced Irrigation API with Machine Learning Integration
Provides intelligent irrigation recommendations using trained ML models
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sqlite3
from pathlib import Path
import joblib
from irrigation_preprocessing import IrrigationDataPreprocessor
from irrigation_model import IrrigationPredictor
import requests
import json

class EnhancedIrrigationAPI:
    def __init__(self):
        self.preprocessor = IrrigationDataPreprocessor()
        self.predictor = IrrigationPredictor()
        self.models_loaded = False
        self.init_database()
        self.load_models()
    
    def init_database(self):
        """Initialize enhanced irrigation database"""
        conn = sqlite3.connect('irrigation_data.db')
        cursor = conn.cursor()
        
        # Enhanced irrigation schedules table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS irrigation_schedules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                crop_type TEXT NOT NULL,
                soil_type TEXT NOT NULL,
                climate_zone TEXT,
                location TEXT,
                field_size REAL,
                planting_date DATE,
                current_growth_stage INTEGER,
                soil_moisture REAL,
                temperature REAL,
                humidity REAL,
                rainfall REAL,
                wind_speed REAL,
                solar_radiation REAL,
                soil_ph REAL,
                soil_ec REAL,
                irrigation_needed INTEGER,
                irrigation_amount_mm REAL,
                irrigation_probability REAL,
                recommendation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                applied BOOLEAN DEFAULT FALSE,
                notes TEXT
            )
        ''')
        
        # Soil moisture monitoring table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS soil_moisture_readings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location TEXT NOT NULL,
                sensor_id TEXT,
                moisture_level REAL NOT NULL,
                temperature REAL,
                ph_level REAL,
                ec_level REAL,
                reading_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Weather data cache table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS weather_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location TEXT NOT NULL,
                temperature REAL,
                humidity REAL,
                rainfall REAL,
                wind_speed REAL,
                solar_radiation REAL,
                weather_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(location, weather_timestamp)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Enhanced irrigation database initialized")
    
    def load_models(self):
        """Load trained ML models and preprocessor"""
        try:
            # Load preprocessor
            if self.preprocessor.load_preprocessor("models/irrigation_preprocessor.pkl"):
                # Load ML models
                if self.predictor.load_models("models/irrigation_model"):
                    self.models_loaded = True
                    print("✅ ML models loaded successfully")
                    return True
            
            print("⚠️ Could not load ML models, using fallback recommendations")
            return False
        except Exception as e:
            print(f"❌ Error loading models: {e}")
            return False
    
    def get_weather_data(self, location):
        """Get current weather data for location with enhanced integration"""
        try:
            # Try to use weather integration system
            from weather_irrigation_integration import weather_integrator
            weather_analysis = weather_integrator.analyze_irrigation_weather_conditions(location)

            if weather_analysis and weather_analysis['current_weather']:
                weather = weather_analysis['current_weather']
                return {
                    'temperature': weather['temperature'],
                    'humidity': weather['humidity'],
                    'rainfall': weather['rainfall'],
                    'wind_speed': weather['wind_speed'],
                    'solar_radiation': weather['solar_radiation'],
                    'weather_analysis': weather_analysis  # Include full analysis
                }
        except Exception as e:
            print(f"⚠️ Weather integration failed: {e}")

        # Fallback to sample data
        return {
            'temperature': np.random.normal(25, 5),
            'humidity': np.random.normal(65, 15),
            'rainfall': np.random.exponential(5),
            'wind_speed': np.random.gamma(2, 2),
            'solar_radiation': np.random.normal(20, 3)
        }
    
    def get_soil_data(self, location, sensor_id=None):
        """Get current soil data from sensors or database"""
        conn = sqlite3.connect('irrigation_data.db')
        
        query = '''
            SELECT moisture_level, temperature, ph_level, ec_level, reading_timestamp
            FROM soil_moisture_readings 
            WHERE location = ?
        '''
        params = [location]
        
        if sensor_id:
            query += ' AND sensor_id = ?'
            params.append(sensor_id)
        
        query += ' ORDER BY reading_timestamp DESC LIMIT 1'
        
        result = conn.execute(query, params).fetchone()
        conn.close()
        
        if result:
            return {
                'soil_moisture': result[0],
                'soil_temperature': result[1],
                'soil_ph': result[2] or 6.5,
                'soil_ec': result[3] or 1.5,
                'last_reading': result[4]
            }
        else:
            # Return default values if no sensor data
            return {
                'soil_moisture': 35.0,
                'soil_temperature': 22.0,
                'soil_ph': 6.5,
                'soil_ec': 1.5,
                'last_reading': None
            }
    
    def get_soil_properties(self, soil_type):
        """Get soil properties based on soil type"""
        soil_properties = {
            'clay': {'field_capacity': 45, 'wilting_point': 25},
            'loam': {'field_capacity': 35, 'wilting_point': 15},
            'sandy_loam': {'field_capacity': 25, 'wilting_point': 10},
            'sandy': {'field_capacity': 15, 'wilting_point': 5},
            'silt': {'field_capacity': 40, 'wilting_point': 20}
        }

        return soil_properties.get(soil_type.lower(), {'field_capacity': 35, 'wilting_point': 15})

    def calculate_crop_stage(self, crop_type, planting_date):
        """Calculate current crop growth stage based on planting date"""
        if not planting_date:
            return 1  # Default to mid-stage

        # Crop growth periods (days)
        crop_periods = {
            'rice': 120, 'wheat': 90, 'corn': 100, 'cotton': 150,
            'sugarcane': 365, 'tomato': 80, 'potato': 70, 'onion': 120,
            'soybean': 95, 'barley': 85
        }

        total_days = crop_periods.get(crop_type.lower(), 100)

        if isinstance(planting_date, str):
            planting_date = datetime.strptime(planting_date, '%Y-%m-%d').date()

        days_since_planting = (datetime.now().date() - planting_date).days

        # Calculate growth stage (0: early, 1: mid, 2: late, 3: harvest)
        if days_since_planting < total_days * 0.25:
            return 0
        elif days_since_planting < total_days * 0.75:
            return 1
        elif days_since_planting < total_days:
            return 2
        else:
            return 3
    
    def get_ml_irrigation_recommendation(self, input_data):
        """Get irrigation recommendation using ML models"""
        if not self.models_loaded:
            return self.get_fallback_recommendation(input_data)
        
        try:
            # Prepare input DataFrame
            df_input = pd.DataFrame([input_data])
            
            # Preprocess the input
            X_processed = self.preprocessor.transform(df_input)
            
            # Make predictions
            predictions = self.predictor.predict_irrigation(X_processed)
            
            # Extract results
            irrigation_needed = bool(predictions['irrigation_needed'][0])
            irrigation_probability = float(predictions['irrigation_probability'][0])
            irrigation_amount = float(predictions['irrigation_amount_mm'][0])
            
            # Generate detailed recommendation
            recommendation = self.generate_detailed_recommendation(
                input_data, irrigation_needed, irrigation_probability, irrigation_amount
            )
            
            return recommendation
            
        except Exception as e:
            print(f"❌ Error in ML prediction: {e}")
            return self.get_fallback_recommendation(input_data)
    
    def generate_detailed_recommendation(self, input_data, irrigation_needed, probability, amount):
        """Generate detailed irrigation recommendation with explanations"""
        
        # Base recommendation
        recommendation = {
            'irrigation_needed': irrigation_needed,
            'irrigation_probability': probability,
            'irrigation_amount_mm': amount,
            'confidence': 'high' if probability > 0.8 or probability < 0.2 else 'medium',
            'timestamp': datetime.now().isoformat()
        }
        
        # Calculate irrigation schedule
        if irrigation_needed and amount > 0:
            # Determine irrigation frequency based on crop and soil type
            frequency_days = self.get_irrigation_frequency(input_data['crop_type'], input_data['soil_type'])
            
            recommendation.update({
                'schedule': f"Irrigate every {frequency_days} days",
                'amount_per_session': round(amount, 1),
                'daily_water_mm': round(amount / frequency_days, 1),
                'best_time': "Early morning (6-8 AM) or evening (6-8 PM)",
                'method': self.get_irrigation_method(input_data['crop_type']),
                'duration_minutes': self.calculate_irrigation_duration(amount, input_data.get('field_size', 1))
            })
        else:
            recommendation.update({
                'schedule': "No irrigation needed",
                'amount_per_session': 0,
                'daily_water_mm': 0,
                'best_time': "N/A",
                'method': "Monitor soil moisture",
                'duration_minutes': 0
            })
        
        # Add explanations
        recommendation['explanations'] = self.generate_explanations(input_data, irrigation_needed, probability)

        # Add warnings and tips
        recommendation['warnings'] = self.generate_warnings(input_data)
        recommendation['tips'] = self.generate_tips(input_data)

        # Add weather-based recommendations if available
        weather_data = input_data.get('weather_data', {})
        if 'weather_analysis' in weather_data:
            weather_analysis = weather_data['weather_analysis']
            recommendation['weather_score'] = weather_analysis.get('current_weather_score', 50)
            recommendation['weather_urgency'] = weather_analysis.get('irrigation_urgency', 'MEDIUM')
            recommendation['optimal_window'] = weather_analysis.get('optimal_irrigation_window', 'Current conditions suitable')

            if weather_analysis.get('weather_warnings'):
                recommendation['warnings'].extend(weather_analysis['weather_warnings'].split('; '))

            # Adjust irrigation timing based on weather
            if weather_analysis.get('rainfall_expected_24h', 0) > 10:
                recommendation['schedule'] = "Delay irrigation - rain expected within 24 hours"
                recommendation['irrigation_needed'] = False
                recommendation['amount_per_session'] = 0
        
        return recommendation
    
    def get_irrigation_frequency(self, crop_type, soil_type):
        """Determine irrigation frequency based on crop and soil"""
        crop_frequencies = {
            'rice': 1,  # Daily during flooding
            'wheat': 7,
            'corn': 3,
            'cotton': 5,
            'sugarcane': 2,
            'tomato': 2,
            'potato': 3,
            'onion': 4,
            'soybean': 4,
            'barley': 6
        }
        
        soil_adjustments = {
            'sandy': -1,  # More frequent
            'clay': +2,   # Less frequent
            'loam': 0,
            'sandy_loam': -1,
            'silt': +1
        }
        
        base_frequency = crop_frequencies.get(crop_type.lower(), 3)
        adjustment = soil_adjustments.get(soil_type.lower(), 0)
        
        return max(1, base_frequency + adjustment)
    
    def get_irrigation_method(self, crop_type):
        """Recommend irrigation method based on crop"""
        methods = {
            'rice': 'Flood irrigation',
            'wheat': 'Sprinkler or drip irrigation',
            'corn': 'Drip or furrow irrigation',
            'cotton': 'Drip irrigation',
            'sugarcane': 'Furrow or drip irrigation',
            'tomato': 'Drip irrigation',
            'potato': 'Sprinkler irrigation',
            'onion': 'Drip irrigation',
            'soybean': 'Sprinkler irrigation',
            'barley': 'Sprinkler irrigation'
        }
        
        return methods.get(crop_type.lower(), 'Drip irrigation (recommended)')
    
    def calculate_irrigation_duration(self, amount_mm, field_size_hectares):
        """Calculate irrigation duration in minutes"""
        # Assume drip irrigation rate of 4 mm/hour
        irrigation_rate = 4.0  # mm/hour
        duration_hours = amount_mm / irrigation_rate
        return round(duration_hours * 60)  # Convert to minutes
    
    def generate_explanations(self, input_data, irrigation_needed, probability):
        """Generate explanations for the recommendation"""
        explanations = []
        
        if irrigation_needed:
            if input_data.get('soil_moisture', 0) < 25:
                explanations.append("Soil moisture is below optimal levels")
            if input_data.get('rainfall', 0) < 5:
                explanations.append("Recent rainfall is insufficient")
            if input_data.get('temperature', 0) > 30:
                explanations.append("High temperature increases water demand")
        else:
            if input_data.get('rainfall', 0) > 10:
                explanations.append("Recent rainfall provides adequate moisture")
            if input_data.get('soil_moisture', 0) > 40:
                explanations.append("Soil moisture levels are adequate")
        
        return explanations
    
    def generate_warnings(self, input_data):
        """Generate warnings based on conditions"""
        warnings = []
        
        if input_data.get('temperature', 0) > 40:
            warnings.append("Extreme heat - avoid midday irrigation")
        if input_data.get('wind_speed', 0) > 15:
            warnings.append("High wind conditions - adjust irrigation timing")
        if input_data.get('soil_ph', 7) < 5.5 or input_data.get('soil_ph', 7) > 8.5:
            warnings.append("Soil pH is outside optimal range")
        
        return warnings
    
    def generate_tips(self, input_data):
        """Generate helpful tips"""
        tips = [
            "Monitor soil moisture regularly with sensors",
            "Adjust irrigation based on weather forecasts",
            "Use mulching to reduce water evaporation",
            "Consider drip irrigation for water efficiency"
        ]
        
        return tips
    
    def get_fallback_recommendation(self, input_data):
        """Fallback recommendation when ML models are not available"""
        crop_type = input_data.get('crop_type', 'wheat').lower()
        soil_moisture = input_data.get('soil_moisture', 35)
        rainfall = input_data.get('rainfall', 5)
        
        # Simple rule-based logic
        irrigation_needed = soil_moisture < 30 and rainfall < 5
        
        base_amounts = {
            'rice': 50, 'wheat': 25, 'corn': 30, 'cotton': 35,
            'sugarcane': 60, 'tomato': 28, 'potato': 22, 'onion': 18
        }
        
        amount = base_amounts.get(crop_type, 25) if irrigation_needed else 0
        
        return {
            'irrigation_needed': irrigation_needed,
            'irrigation_probability': 0.8 if irrigation_needed else 0.2,
            'irrigation_amount_mm': amount,
            'confidence': 'medium',
            'schedule': f"Water every 3-5 days" if irrigation_needed else "No irrigation needed",
            'method': 'Drip irrigation recommended',
            'explanations': ['Based on basic soil moisture and rainfall thresholds'],
            'warnings': [],
            'tips': ['Consider upgrading to ML-based recommendations'],
            'timestamp': datetime.now().isoformat()
        }
    
    def get_comprehensive_irrigation_recommendation(self, crop_type, soil_type, location, 
                                                  planting_date=None, field_size=1.0, user_id=None):
        """Get comprehensive irrigation recommendation with all data sources"""
        
        # Get current weather data
        weather_data = self.get_weather_data(location)
        
        # Get soil sensor data
        soil_data = self.get_soil_data(location)
        
        # Calculate crop stage
        growth_stage = self.calculate_crop_stage(crop_type, planting_date)
        crop_days = 30 if not planting_date else (datetime.now().date() - 
                   datetime.strptime(planting_date, '%Y-%m-%d').date()).days
        
        # Determine climate zone (simplified)
        climate_zone = 'temperate'  # Could be enhanced with location-based lookup
        
        # Get soil properties based on soil type
        soil_properties = self.get_soil_properties(soil_type)

        # Calculate crop coefficient based on crop type and growth stage
        crop_coefficients = {
            'rice': [0.6, 1.2, 0.8], 'wheat': [0.5, 1.1, 0.7], 'corn': [0.6, 1.3, 0.8],
            'cotton': [0.7, 1.2, 0.6], 'sugarcane': [0.8, 1.4, 0.9], 'tomato': [0.6, 1.1, 0.8],
            'potato': [0.5, 1.0, 0.7], 'onion': [0.5, 0.9, 0.6], 'soybean': [0.6, 1.1, 0.7],
            'barley': [0.5, 1.0, 0.6]
        }

        kc_values = crop_coefficients.get(crop_type.lower(), [0.6, 1.1, 0.7])
        crop_coefficient = kc_values[min(growth_stage, 2)]

        # Calculate ET0 and ETC
        temp = weather_data['temperature']
        humidity = weather_data['humidity']
        solar_rad = weather_data['solar_radiation']

        # Simplified ET0 calculation
        et0 = 0.0023 * (temp + 17.8) * np.sqrt(abs(temp - humidity)) * (solar_rad * 0.408)
        etc = et0 * crop_coefficient

        # Calculate effective rainfall
        rainfall = weather_data['rainfall']
        effective_rainfall = rainfall * 0.8 if rainfall < 10 else rainfall * 0.6

        # Calculate moisture stress
        available_water = soil_data['soil_moisture'] - soil_properties['wilting_point']
        max_available_water = soil_properties['field_capacity'] - soil_properties['wilting_point']
        moisture_stress = max(0, 1 - (available_water / max_available_water)) if max_available_water > 0 else 0

        # Prepare input data for ML model
        input_data = {
            'crop_type': crop_type,
            'soil_type': soil_type,
            'climate_zone': climate_zone,
            'temperature': weather_data['temperature'],
            'humidity': weather_data['humidity'],
            'rainfall': weather_data['rainfall'],
            'wind_speed': weather_data['wind_speed'],
            'solar_radiation': weather_data['solar_radiation'],
            'soil_moisture': soil_data['soil_moisture'],
            'soil_ph': soil_data['soil_ph'],
            'soil_ec': soil_data['soil_ec'],
            'crop_days': max(1, crop_days),
            'growth_stage': growth_stage,
            'crop_coefficient': crop_coefficient,
            'et0': et0,
            'etc': etc,
            'effective_rainfall': effective_rainfall,
            'moisture_stress': moisture_stress,
            'field_capacity': soil_properties['field_capacity'],
            'wilting_point': soil_properties['wilting_point'],
            'field_size': field_size,
            'location': location,
            'weather_data': weather_data  # Include weather data for analysis
        }
        
        # Get ML recommendation
        recommendation = self.get_ml_irrigation_recommendation(input_data)
        
        # Store recommendation in database
        self.store_irrigation_recommendation(user_id, input_data, recommendation)
        
        return recommendation
    
    def store_irrigation_recommendation(self, user_id, input_data, recommendation):
        """Store irrigation recommendation in database"""
        conn = sqlite3.connect('irrigation_data.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO irrigation_schedules (
                user_id, crop_type, soil_type, climate_zone, location,
                field_size, current_growth_stage, soil_moisture, temperature,
                humidity, rainfall, wind_speed, solar_radiation, soil_ph, soil_ec,
                irrigation_needed, irrigation_amount_mm, irrigation_probability
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            user_id,
            input_data['crop_type'],
            input_data['soil_type'],
            input_data['climate_zone'],
            input_data.get('location', 'Unknown'),
            input_data['field_size'],
            input_data['growth_stage'],
            input_data['soil_moisture'],
            input_data['temperature'],
            input_data['humidity'],
            input_data['rainfall'],
            input_data['wind_speed'],
            input_data['solar_radiation'],
            input_data['soil_ph'],
            input_data['soil_ec'],
            recommendation['irrigation_needed'],
            recommendation['irrigation_amount_mm'],
            recommendation['irrigation_probability']
        ))
        
        conn.commit()
        conn.close()

# Initialize the enhanced API
enhanced_irrigation_api = EnhancedIrrigationAPI()
