"""
Test script to verify the irrigation API is working correctly
"""

import requests
import json

def test_irrigation_api():
    """Test the irrigation prediction API"""
    
    # Test data
    test_data = {
        "crop_type": "tomato",
        "soil_type": "sandy", 
        "location": "Delhi",
        "field_size": 2.0,
        "planting_date": "2024-01-01"
    }
    
    try:
        print("🧪 Testing Irrigation API...")
        print(f"Test data: {test_data}")
        
        # Test the API endpoint
        response = requests.post(
            'http://127.0.0.1:5000/irrigation_predict',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ API Response:")
            print(f"Irrigation Needed: {result.get('irrigation_needed')}")
            print(f"Amount: {result.get('irrigation_amount_mm')} mm")
            print(f"Confidence: {result.get('confidence')}")
            print(f"Schedule: {result.get('schedule')}")
            print(f"Method: {result.get('method')}")
            
            if result.get('explanations'):
                print("\n📝 Explanations:")
                for exp in result['explanations']:
                    print(f"  - {exp}")
            
            if result.get('warnings'):
                print("\n⚠️ Warnings:")
                for warn in result['warnings']:
                    print(f"  - {warn}")
            
            if result.get('tips'):
                print("\n💡 Tips:")
                for tip in result['tips'][:3]:  # Show first 3 tips
                    print(f"  - {tip}")
            
            print("\n🎉 API test successful!")
            return True
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure Flask app is running on http://127.0.0.1:5000")
        return False
    except Exception as e:
        print(f"❌ Test Error: {e}")
        return False

def test_direct_api():
    """Test the enhanced irrigation API directly"""
    try:
        print("\n🔬 Testing Enhanced Irrigation API directly...")
        
        from enhanced_irrigation_api import enhanced_irrigation_api
        
        # Test with different conditions
        test_cases = [
            {
                'name': 'Normal Conditions',
                'crop_type': 'wheat',
                'soil_type': 'loam',
                'location': 'Delhi',
                'field_size': 1.0
            },
            {
                'name': 'Dry Conditions (Sandy Soil)',
                'crop_type': 'tomato',
                'soil_type': 'sandy',
                'location': 'Rajasthan',
                'field_size': 2.0
            },
            {
                'name': 'High Water Crop (Rice)',
                'crop_type': 'rice',
                'soil_type': 'clay',
                'location': 'Punjab',
                'field_size': 3.0
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- Test Case {i}: {test_case['name']} ---")
            
            recommendation = enhanced_irrigation_api.get_comprehensive_irrigation_recommendation(
                crop_type=test_case['crop_type'],
                soil_type=test_case['soil_type'],
                location=test_case['location'],
                field_size=test_case['field_size']
            )
            
            print(f"Crop: {test_case['crop_type']} | Soil: {test_case['soil_type']}")
            print(f"Irrigation Needed: {recommendation['irrigation_needed']}")
            print(f"Amount: {recommendation['irrigation_amount_mm']:.1f} mm")
            print(f"Confidence: {recommendation['confidence']}")
            
            if recommendation.get('explanations'):
                print(f"Main Reason: {recommendation['explanations'][0]}")
        
        print("\n✅ Direct API tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Direct API test failed: {e}")
        return False

if __name__ == "__main__":
    print("🌾 Irrigation System API Tests")
    print("=" * 50)
    
    # Test direct API first
    direct_success = test_direct_api()
    
    # Test Flask API
    flask_success = test_irrigation_api()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Direct API: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"Flask API: {'✅ PASS' if flask_success else '❌ FAIL'}")
    
    if direct_success and flask_success:
        print("\n🎉 All tests passed! The irrigation system is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the error messages above.")
