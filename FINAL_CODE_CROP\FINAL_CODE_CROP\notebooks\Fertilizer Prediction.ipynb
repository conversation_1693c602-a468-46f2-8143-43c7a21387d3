{"cells": [{"cell_type": "code", "execution_count": 1, "id": "hazardous-juice", "metadata": {}, "outputs": [], "source": ["#importing libraries\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": 2, "id": "signed-float", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Temparature</th>\n", "      <th>Hu<PERSON><PERSON><PERSON></th>\n", "      <th>Moisture</th>\n", "      <th>Soil Type</th>\n", "      <th>Crop Type</th>\n", "      <th>Nitrogen</th>\n", "      <th>Potassium</th>\n", "      <th>Phosphorous</th>\n", "      <th>Fertilizer Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>26</td>\n", "      <td>52</td>\n", "      <td>38</td>\n", "      <td>Sandy</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Urea</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>29</td>\n", "      <td>52</td>\n", "      <td>45</td>\n", "      <td>Loamy</td>\n", "      <td>Sugarcane</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>36</td>\n", "      <td>DAP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>34</td>\n", "      <td>65</td>\n", "      <td>62</td>\n", "      <td>Black</td>\n", "      <td>Cotton</td>\n", "      <td>7</td>\n", "      <td>9</td>\n", "      <td>30</td>\n", "      <td>14-35-14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>32</td>\n", "      <td>62</td>\n", "      <td>34</td>\n", "      <td>Red</td>\n", "      <td>Tobacco</td>\n", "      <td>22</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>28-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>28</td>\n", "      <td>54</td>\n", "      <td>46</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Paddy</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Urea</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Temparature  Humidity   Moisture Soil Type  Crop Type  Nitrogen  Potassium  \\\n", "0           26         52        38     <PERSON>        37          0   \n", "1           29         52        45     Loamy  <PERSON>cane        12          0   \n", "2           34         65        62     <PERSON>     Cotton         7          9   \n", "3           32         62        34       Red    Tobacco        22          0   \n", "4           28         54        46    <PERSON><PERSON>        35          0   \n", "\n", "   Phosphorous Fertilizer Name  \n", "0            0            Urea  \n", "1           36             DAP  \n", "2           30        14-35-14  \n", "3           20           28-28  \n", "4            0            Urea  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["#importing the dataset\n", "data = pd.read_csv('Fertilizer Prediction.csv')\n", "data.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "durable-photographer", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 99 entries, 0 to 98\n", "Data columns (total 9 columns):\n", " #   Column           Non-Null Count  Dtype \n", "---  ------           --------------  ----- \n", " 0   Temparature      99 non-null     int64 \n", " 1   Humidity         99 non-null     int64 \n", " 2   Moisture         99 non-null     int64 \n", " 3   Soil Type        99 non-null     object\n", " 4   Crop Type        99 non-null     object\n", " 5   Nitrogen         99 non-null     int64 \n", " 6   Potassium        99 non-null     int64 \n", " 7   Phosphorous      99 non-null     int64 \n", " 8   Fertilizer Name  99 non-null     object\n", "dtypes: int64(6), object(3)\n", "memory usage: 7.1+ KB\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 4, "id": "sweet-daisy", "metadata": {}, "outputs": [], "source": ["#changing the column names\n", "data.rename(columns={'Humidity ':'Humidity','Soil Type':'Soil_Type','Crop Type':'Crop_Type','Fertilizer Name':'Fertilizer'},inplace=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "golden-european", "metadata": {}, "outputs": [{"data": {"text/plain": ["Temparature    14\n", "Humidity       13\n", "Moisture       41\n", "Soil_Type       5\n", "Crop_Type      11\n", "Nitrogen       24\n", "Potassium      13\n", "Phosphorous    32\n", "Fertilizer      7\n", "dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#checking unique values\n", "data.nunique()"]}, {"cell_type": "code", "execution_count": 6, "id": "suited-corner", "metadata": {}, "outputs": [{"data": {"text/plain": ["Temparature    0\n", "Humidity       0\n", "Moisture       0\n", "Soil_Type      0\n", "Crop_Type      0\n", "Nitrogen       0\n", "Potassium      0\n", "Phosphorous    0\n", "Fertilizer     0\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#checking for null values\n", "data.isna().sum()"]}, {"cell_type": "code", "execution_count": 7, "id": "published-walnut", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Temparature</th>\n", "      <th>Hu<PERSON><PERSON><PERSON></th>\n", "      <th>Moisture</th>\n", "      <th>Soil_Type</th>\n", "      <th>Crop_Type</th>\n", "      <th>Nitrogen</th>\n", "      <th>Potassium</th>\n", "      <th>Phosphorous</th>\n", "      <th>Fertilizer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>99.000000</td>\n", "      <td>99.000000</td>\n", "      <td>99.000000</td>\n", "      <td>99</td>\n", "      <td>99</td>\n", "      <td>99.000000</td>\n", "      <td>99.000000</td>\n", "      <td>99.000000</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>11</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Loamy</td>\n", "      <td>Sugarcane</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Urea</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>21</td>\n", "      <td>13</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>30.282828</td>\n", "      <td>59.151515</td>\n", "      <td>43.181818</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>18.909091</td>\n", "      <td>3.383838</td>\n", "      <td>18.606061</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.502304</td>\n", "      <td>5.840331</td>\n", "      <td>11.271568</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>11.599693</td>\n", "      <td>5.814667</td>\n", "      <td>13.476978</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>25.000000</td>\n", "      <td>50.000000</td>\n", "      <td>25.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>28.000000</td>\n", "      <td>54.000000</td>\n", "      <td>34.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>10.000000</td>\n", "      <td>0.000000</td>\n", "      <td>9.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>30.000000</td>\n", "      <td>60.000000</td>\n", "      <td>41.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>13.000000</td>\n", "      <td>0.000000</td>\n", "      <td>19.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>33.000000</td>\n", "      <td>64.000000</td>\n", "      <td>50.500000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>24.000000</td>\n", "      <td>7.500000</td>\n", "      <td>30.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>38.000000</td>\n", "      <td>72.000000</td>\n", "      <td>65.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>42.000000</td>\n", "      <td>19.000000</td>\n", "      <td>42.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Temparature   Humidity   Moisture Soil_Type  Crop_Type   Nitrogen  \\\n", "count     99.000000  99.000000  99.000000        99         99  99.000000   \n", "unique          NaN        NaN        NaN         5         11        NaN   \n", "top             NaN        NaN        NaN     Loamy  Sugarcane        NaN   \n", "freq            NaN        NaN        NaN        21         13        NaN   \n", "mean      30.282828  59.151515  43.181818       NaN        NaN  18.909091   \n", "std        3.502304   5.840331  11.271568       NaN        NaN  11.599693   \n", "min       25.000000  50.000000  25.000000       NaN        NaN   4.000000   \n", "25%       28.000000  54.000000  34.000000       NaN        NaN  10.000000   \n", "50%       30.000000  60.000000  41.000000       NaN        NaN  13.000000   \n", "75%       33.000000  64.000000  50.500000       NaN        NaN  24.000000   \n", "max       38.000000  72.000000  65.000000       NaN        NaN  42.000000   \n", "\n", "        Potassium  Phosphorous Fertilizer  \n", "count   99.000000    99.000000         99  \n", "unique        NaN          NaN          7  \n", "top           NaN          NaN       Urea  \n", "freq          NaN          NaN         22  \n", "mean     3.383838    18.606061        NaN  \n", "std      5.814667    13.476978        NaN  \n", "min      0.000000     0.000000        NaN  \n", "25%      0.000000     9.000000        NaN  \n", "50%      0.000000    19.000000        NaN  \n", "75%      7.500000    30.000000        NaN  \n", "max     19.000000    42.000000        NaN  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["#statistical parameters\n", "data.describe(include='all')"]}, {"cell_type": "code", "execution_count": 8, "id": "treated-liechtenstein", "metadata": {}, "outputs": [], "source": ["#Defining function for Continuous and catogorical variable\n", "def plot_conti(x):\n", "    fig, axes = plt.subplots(nrows=1,ncols=3,figsize=(15,5),tight_layout=True)\n", "    axes[0].set_title('Histogram')\n", "    sns.histplot(x,ax=axes[0])\n", "    axes[1].set_title('Checking Outliers')\n", "    sns.boxplot(x,ax=axes[1])\n", "    axes[2].set_title('Relation with output variable')\n", "    sns.boxplot(y = x,x = data.Fertilizer)\n", "    \n", "def plot_cato(x):\n", "    fig, axes = plt.subplots(nrows=1,ncols=2,figsize=(15,5),tight_layout=True)\n", "    axes[0].set_title('Count Plot')\n", "    sns.countplot(x,ax=axes[0])\n", "    axes[1].set_title('Relation with output variable')\n", "    sns.countplot(x = x,hue = data.Fertilizer, ax=axes[1])"]}, {"cell_type": "code", "execution_count": 22, "id": "rotary-november", "metadata": {}, "outputs": [], "source": ["#encoding the labels for categorical variables\n", "from sklearn.preprocessing import LabelEncoder"]}, {"cell_type": "code", "execution_count": 23, "id": "chief-junior", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Encoded</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Original</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Black</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Loamy</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Red</th>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON></th>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Encoded\n", "Original         \n", "Black           0\n", "Clayey          1\n", "Loamy           2\n", "Red             3\n", "Sandy           4"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["#encoding Soil Type variable\n", "encode_soil = LabelEncoder()\n", "\n", "#fitting the label encoder\n", "data.Soil_Type = encode_soil.fit_transform(data.Soil_Type)\n", "\n", "#creating the DataFrame\n", "Soil_Type = pd.DataFrame(zip(encode_soil.classes_,encode_soil.transform(encode_soil.classes_)),columns=['Original','Encoded'])\n", "Soil_Type = Soil_Type.set_index('Original')\n", "Soil_Type"]}, {"cell_type": "code", "execution_count": 24, "id": "parental-offset", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Encoded</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Original</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Cotton</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ground Nuts</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Millets</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Oil seeds</th>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Paddy</th>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pulses</th>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sugarcane</th>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Tobacco</th>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Wheat</th>\n", "      <td>10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Encoded\n", "Original            \n", "Barley             0\n", "Cotton             1\n", "Ground Nuts        2\n", "Maize              3\n", "Millets            4\n", "Oil seeds          5\n", "Paddy              6\n", "Pulses             7\n", "Sugarcane          8\n", "Tobacco            9\n", "Wheat             10"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["#encoding Crop Type variable\n", "encode_crop = LabelEncoder()\n", "\n", "#fitting the label encoder\n", "data.Crop_Type = encode_crop.fit_transform(data.Crop_Type)\n", "\n", "#creating the DataFrame\n", "Crop_Type = pd.DataFrame(zip(encode_crop.classes_,encode_crop.transform(encode_crop.classes_)),columns=['Original','Encoded'])\n", "Crop_Type = Crop_Type.set_index('Original')\n", "Crop_Type"]}, {"cell_type": "code", "execution_count": 25, "id": "organic-oakland", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Encoded</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Original</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10-26-26</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14-35-14</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17-17-17</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20-20</th>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28-28</th>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DAP</th>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Urea</th>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Encoded\n", "Original         \n", "10-26-26        0\n", "14-35-14        1\n", "17-17-17        2\n", "20-20           3\n", "28-28           4\n", "DAP             5\n", "Urea            6"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["#encoding Fertilizer variable\n", "encode_ferti = LabelEncoder()\n", "\n", "#fitting the label encoder\n", "data.Fertilizer = encode_ferti.fit_transform(data.Fertilizer)\n", "\n", "#creating the DataFrame\n", "Fertilizer = pd.DataFrame(zip(encode_ferti.classes_,encode_ferti.transform(encode_ferti.classes_)),columns=['Original','Encoded'])\n", "Fertilizer = Fertilizer.set_index('Original')\n", "Fertilizer"]}, {"cell_type": "code", "execution_count": 26, "id": "forced-activation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of Splitting :\n", "x_train = (79, 8), y_train = (79,), x_test = (20, 8), y_test = (20,)\n"]}], "source": ["#splitting the data into train and test\n", "from sklearn.model_selection import train_test_split\n", "\n", "x_train, x_test, y_train, y_test = train_test_split(data.drop('Fertilizer',axis=1),data.Fertilizer,test_size=0.2,random_state=1)\n", "print('Shape of Splitting :')\n", "print('x_train = {}, y_train = {}, x_test = {}, y_test = {}'.format(x_train.shape,y_train.shape,x_test.shape,y_test.shape))"]}, {"cell_type": "code", "execution_count": 27, "id": "handy-mortality", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 79 entries, 2 to 37\n", "Data columns (total 8 columns):\n", " #   Column       Non-Null Count  Dtype\n", "---  ------       --------------  -----\n", " 0   Temparature  79 non-null     int64\n", " 1   Humidity     79 non-null     int64\n", " 2   Moisture     79 non-null     int64\n", " 3   Soil_Type    79 non-null     int32\n", " 4   Crop_Type    79 non-null     int32\n", " 5   Nitrogen     79 non-null     int64\n", " 6   Potassium    79 non-null     int64\n", " 7   Phosphorous  79 non-null     int64\n", "dtypes: int32(2), int64(6)\n", "memory usage: 4.9 KB\n"]}], "source": ["x_train.info()"]}, {"cell_type": "markdown", "id": "alleged-lemon", "metadata": {}, "source": ["### Logistic regression model"]}, {"cell_type": "code", "execution_count": 28, "id": "finnish-killer", "metadata": {}, "outputs": [], "source": ["#importing libraries\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix"]}, {"cell_type": "code", "execution_count": 71, "id": "rising-tender", "metadata": {}, "outputs": [], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "rand = RandomForestClassifier()"]}, {"cell_type": "code", "execution_count": 72, "id": "independent-economics", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       1.00      0.33      0.50         3\n", "           1       0.75      1.00      0.86         3\n", "           2       0.67      1.00      0.80         2\n", "           3       1.00      1.00      1.00         2\n", "           4       1.00      1.00      1.00         2\n", "           5       1.00      1.00      1.00         2\n", "           6       1.00      1.00      1.00         6\n", "\n", "    accuracy                           0.90        20\n", "   macro avg       0.92      0.90      0.88        20\n", "weighted avg       0.93      0.90      0.88        20\n", "\n"]}], "source": ["pred_rand = rand.fit(x_train,y_train).predict(x_test)\n", "\n", "print(classification_report(y_test,pred_rand))"]}, {"cell_type": "code", "execution_count": 76, "id": "psychological-encounter", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import GridSearchCV"]}, {"cell_type": "code", "execution_count": 77, "id": "virgin-pride", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fitting 3 folds for each of 27 candidates, totalling 81 fits\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 4 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  24 tasks      | elapsed:  2.7min\n", "[Parallel(n_jobs=-1)]: Done  81 out of  81 | elapsed:  3.2min finished\n"]}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       1.00      0.33      0.50         3\n", "           1       0.75      1.00      0.86         3\n", "           2       0.67      1.00      0.80         2\n", "           3       1.00      1.00      1.00         2\n", "           4       1.00      1.00      1.00         2\n", "           5       1.00      1.00      1.00         2\n", "           6       1.00      1.00      1.00         6\n", "\n", "    accuracy                           0.90        20\n", "   macro avg       0.92      0.90      0.88        20\n", "weighted avg       0.93      0.90      0.88        20\n", "\n", "Best score :  0.9876543209876543\n", "Best params :  {'max_depth': 10, 'min_samples_split': 2, 'n_estimators': 500}\n"]}], "source": ["params = {\n", "    'n_estimators':[300,400,500],\n", "    'max_depth':[5,10,15],\n", "    'min_samples_split':[2,5,8]\n", "}\n", "\n", "grid_rand = GridSearchCV(rand,params,cv=3,verbose=3,n_jobs=-1)\n", "\n", "grid_rand.fit(x_train,y_train)\n", "\n", "pred_rand = grid_rand.predict(x_test)\n", "\n", "print(classification_report(y_test,pred_rand))\n", "\n", "print('Best score : ',grid_rand.best_score_)\n", "print('Best params : ',grid_rand.best_params_)"]}, {"cell_type": "code", "execution_count": null, "id": "seasonal-typing", "metadata": {}, "outputs": [], "source": ["#Final Model\n", "params = {\n", "    'n_estimators':[350,400,450],\n", "    'max_depth':[2,3,4,5,6,7],\n", "    'min_samples_split':[2,5,8]\n", "}\n", "\n", "grid_rand = GridSearchCV(rand,params,cv=3,verbose=3,n_jobs=-1)\n", "\n", "grid_rand.fit(data.drop('Fertilizer',axis=1),data.Fertilizer)\n", "\n", "print('Best score : ',grid_rand.best_score_)\n", "print('Best params : ',grid_rand.best_params_)"]}, {"cell_type": "code", "execution_count": null, "id": "documented-preference", "metadata": {}, "outputs": [], "source": ["x_train"]}, {"cell_type": "code", "execution_count": null, "id": "logical-palestinian", "metadata": {}, "outputs": [], "source": ["#pickling the file\n", "import pickle\n", "pickle_out = open('classifier.pkl','wb')\n", "pickle.dump(grid_rand,pickle_out)\n", "pickle_out.close()"]}, {"cell_type": "code", "execution_count": null, "id": "distant-mi<PERSON><PERSON>", "metadata": {}, "outputs": [], "source": ["model = pickle.load(open('classifier.pkl','rb'))\n", "model.predict([[34,67,62,0,1,7,0,30]])"]}, {"cell_type": "code", "execution_count": 49, "id": "prerequisite-handle", "metadata": {}, "outputs": [{"data": {"text/plain": ["2     1\n", "44    5\n", "59    5\n", "55    4\n", "19    5\n", "     ..\n", "75    1\n", "9     1\n", "72    4\n", "12    6\n", "37    5\n", "Name: Fertilizer, Length: 79, dtype: int32"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["y_train"]}, {"cell_type": "code", "execution_count": 53, "id": "appointed-spouse", "metadata": {}, "outputs": [], "source": ["#pickling the file\n", "import pickle\n", "pickle_out = open('fertilizer.pkl','wb')\n", "pickle.dump(encode_ferti,pickle_out)\n", "pickle_out.close()"]}, {"cell_type": "code", "execution_count": 66, "id": "surface-center", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Urea'"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["ferti = pickle.load(open('fertilizer.pkl','rb'))\n", "ferti.classes_[6]"]}, {"cell_type": "code", "execution_count": 75, "id": "affected-tobacco", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: scikit-learn\n", "Version: 0.23.0\n", "Summary: A set of python modules for machine learning and data mining\n", "Home-page: http://scikit-learn.org\n", "Author: None\n", "Author-email: None\n", "License: new BSD\n", "Location: c:\\users\\<USER>\\anaconda3\\lib\\site-packages\n", "Requires: joblib, scipy, numpy, threadpoolctl\n", "Required-by: pm<PERSON><PERSON>, mlxtend, lightgbm, imbalanced-learn\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip show scikit-learn"]}, {"cell_type": "code", "execution_count": null, "id": "deluxe-jungle", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 5}