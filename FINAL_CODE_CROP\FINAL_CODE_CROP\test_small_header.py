import requests

# Test data that should give high confidence
high_confidence_data = {
    'nitrogen': 90,
    'phosphorous': 42,
    'pottasium': 43,
    'temp': 20.9,
    'hum': 82.0,
    'ph': 6.5,
    'rainfall': 202.9
}

print("🧪 Testing smaller header card with high confidence...")
print(f"📊 Using data that should give ~95% confidence: {high_confidence_data}")

try:
    response = requests.post('http://127.0.0.1:5000/crop_predict', 
                           data=high_confidence_data, 
                           timeout=10)
    
    if response.status_code == 200:
        # Save the response to see the smaller header
        with open('small_header_result.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("✅ Response saved to small_header_result.html")
        
        # Check title sizing
        if 'Crop Recommendation Analysis' in response.text:
            print("📋 Header card found!")
            
    else:
        print(f"❌ Request failed: {response.status_code}")

except Exception as e:
    print(f"❌ Error: {e}")

print("\n📄 You can open 'small_header_result.html' to see the smaller header card!")
