<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Management - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .expense-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .expense-card:hover {
            transform: translateY(-2px);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .category-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .expense-row-new {
            animation: slideIn 0.5s ease-in-out;
        }
        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-seedling me-2"></i>AgroPro - Expense Management
                <span id="connection-status" class="badge bg-secondary ms-2">
                    <i class="fas fa-circle" id="status-icon"></i> Connecting...
                </span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/add_expense"><i class="fas fa-plus me-1"></i>Add Expense</a>
                <a class="nav-link" href="/view_expenses"><i class="fas fa-list me-1"></i>View All</a>
                <a class="nav-link" href="/expense_analytics"><i class="fas fa-chart-bar me-1"></i>Analytics</a>
                <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Home</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card expense-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                        <h3>₹{{ "%.2f"|format(monthly_total) }}</h3>
                        <p class="mb-0">This Month</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <h3>₹{{ "%.2f"|format(total_expenses) }}</h3>
                        <p class="mb-0">Total Expenses</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-tags fa-2x mb-2"></i>
                        <h3>{{ category_summary|length }}</h3>
                        <p class="mb-0">Categories</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-receipt fa-2x mb-2"></i>
                        <h3>{{ recent_expenses|length }}</h3>
                        <p class="mb-0">Recent Entries</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Expenses -->
            <div class="col-lg-8 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Expenses</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Category</th>
                                        <th>Description</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for expense in recent_expenses %}
                                    <tr>
                                        <td>{{ expense[1] }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ expense[2] }}</span>
                                        </td>
                                        <td>{{ expense[4] or 'No description' }}</td>
                                        <td class="text-end">
                                            <strong>₹{{ "%.2f"|format(expense[3]) }}</strong>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            <i class="fas fa-inbox fa-2x mb-2"></i>
                                            <p>No expenses recorded yet</p>
                                            <a href="/add_expense" class="btn btn-primary">Add Your First Expense</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Category Summary -->
            <div class="col-lg-4 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-pie-chart me-2"></i>Category Breakdown (30 Days)</h5>
                    </div>
                    <div class="card-body">
                        {% for category in category_summary %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-0">{{ category[0] }}</h6>
                                <small class="text-muted">{{ category[2] }} entries</small>
                            </div>
                            <div class="text-end">
                                <strong>₹{{ "%.2f"|format(category[1]) }}</strong>
                            </div>
                        </div>
                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: {{ (category[1] / category_summary[0][1] * 100) if category_summary else 0 }}%">
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-chart-pie fa-3x mb-2"></i>
                            <p>No category data available</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Trends -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Monthly Expense Trends</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="monthlyTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card expense-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <a href="/add_expense" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-plus fa-2x mb-2"></i><br>
                                    Add Expense
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/view_expenses" class="btn btn-info btn-lg w-100">
                                    <i class="fas fa-list fa-2x mb-2"></i><br>
                                    View All
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/expense_analytics" class="btn btn-success btn-lg w-100">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                                    Analytics
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-secondary btn-lg w-100" onclick="exportData()">
                                    <i class="fas fa-download fa-2x mb-2"></i><br>
                                    Export Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        // Monthly trend chart
        const ctx = document.getElementById('monthlyTrendChart').getContext('2d');
        const monthlyData = {{ monthly_totals | tojson }};
        
        const labels = monthlyData.map(item => item[0]);
        const data = monthlyData.map(item => item[1]);
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Monthly Expenses (₹)',
                    data: data,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount (₹)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });

        // Initialize Socket.IO for real-time updates
        const socket = io();
        let monthlyChart;

        // Connection status
        socket.on('connect', function() {
            console.log('Connected to real-time expense updates');
            updateConnectionStatus('connected');
            showNotification('Connected to real-time updates', 'success');
            socket.emit('get_live_data');
        });

        socket.on('disconnect', function() {
            console.log('Disconnected from server');
            updateConnectionStatus('disconnected');
            showNotification('Connection lost - trying to reconnect...', 'warning');
        });

        socket.on('reconnect', function() {
            updateConnectionStatus('connected');
            showNotification('Reconnected to real-time updates', 'success');
            socket.emit('get_live_data');
        });

        // Real-time data updates
        socket.on('live_data_update', function(data) {
            updateDashboard(data);
        });

        socket.on('expense_added', function(data) {
            showNotification(data.message, 'success');
            updateDashboard(data.dashboard_data);
            // Add visual effect for new expense
            highlightNewExpense();
        });

        socket.on('expense_deleted', function(data) {
            showNotification(data.message, 'info');
            updateDashboard(data.dashboard_data);
        });

        socket.on('error', function(data) {
            showNotification(data.message, 'danger');
        });

        // Update dashboard with real-time data
        function updateDashboard(data) {
            // Update summary cards
            document.querySelector('.metric-card:nth-child(1) h3').textContent = '₹' + data.monthly_total.toFixed(2);
            document.querySelector('.metric-card:nth-child(2) h3').textContent = '₹' + data.total_expenses.toFixed(2);
            document.querySelector('.metric-card:nth-child(3) h3').textContent = data.category_summary.length;
            document.querySelector('.metric-card:nth-child(4) h3').textContent = data.recent_expenses.length;

            // Update recent expenses table
            updateRecentExpensesTable(data.recent_expenses);

            // Update category summary
            updateCategorySummary(data.category_summary);

            // Update chart if needed
            if (monthlyChart) {
                // Request updated chart data
                fetch('/api/chart_data')
                    .then(response => response.json())
                    .then(chartData => updateChart(chartData));
            }
        }

        function updateRecentExpensesTable(expenses) {
            const tbody = document.querySelector('.table tbody');
            tbody.innerHTML = '';

            if (expenses.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>No expenses recorded yet</p>
                            <a href="/add_expense" class="btn btn-primary">Add Your First Expense</a>
                        </td>
                    </tr>
                `;
                return;
            }

            expenses.forEach(expense => {
                const row = document.createElement('tr');
                row.className = 'expense-row';
                row.innerHTML = `
                    <td>${expense.date}</td>
                    <td><span class="badge bg-secondary">${expense.category}</span></td>
                    <td>${expense.description || 'No description'}</td>
                    <td class="text-end"><strong>₹${expense.amount.toFixed(2)}</strong></td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateCategorySummary(categories) {
            const container = document.querySelector('.card-body').parentElement.querySelector('.card-body');
            // Find the category summary container
            const categoryContainer = document.querySelector('.col-lg-4 .card-body');

            if (categories.length === 0) {
                categoryContainer.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-chart-pie fa-3x mb-2"></i>
                        <p>No category data available</p>
                    </div>
                `;
                return;
            }

            let html = '';
            const maxAmount = categories[0]?.total || 1;

            categories.forEach(category => {
                const percentage = (category.total / maxAmount * 100);
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">${category.category}</h6>
                            <small class="text-muted">${category.count} entries</small>
                        </div>
                        <div class="text-end">
                            <strong>₹${category.total.toFixed(2)}</strong>
                        </div>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar" role="progressbar" style="width: ${percentage}%"></div>
                    </div>
                `;
            });

            categoryContainer.innerHTML = html;
        }

        function highlightNewExpense() {
            const firstRow = document.querySelector('.expense-row');
            if (firstRow) {
                firstRow.style.backgroundColor = '#d4edda';
                firstRow.style.transition = 'background-color 3s ease';
                setTimeout(() => {
                    firstRow.style.backgroundColor = '';
                }, 3000);
            }
        }

        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connection-status');
            const iconElement = document.getElementById('status-icon');

            if (status === 'connected') {
                statusElement.className = 'badge bg-success ms-2';
                statusElement.innerHTML = '<i class="fas fa-circle" id="status-icon"></i> Live';
                iconElement.style.animation = 'none';
            } else if (status === 'disconnected') {
                statusElement.className = 'badge bg-danger ms-2';
                statusElement.innerHTML = '<i class="fas fa-circle" id="status-icon"></i> Offline';
            } else {
                statusElement.className = 'badge bg-warning ms-2';
                statusElement.innerHTML = '<i class="fas fa-circle" id="status-icon"></i> Connecting...';
                iconElement.style.animation = 'pulse 1s infinite';
            }
        }

        // Request live data every 30 seconds
        setInterval(() => {
            socket.emit('get_live_data');
        }, 30000);

        // Export data function
        function exportData() {
            // Get current data and export
            fetch('/api/export_expenses')
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'expenses_' + new Date().toISOString().split('T')[0] + '.csv';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                });
        }
    </script>
</body>
</html>
