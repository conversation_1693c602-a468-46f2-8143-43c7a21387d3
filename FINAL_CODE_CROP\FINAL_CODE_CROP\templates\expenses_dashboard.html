<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Management - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .expense-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .expense-card:hover {
            transform: translateY(-2px);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .category-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .expense-row-new {
            animation: slideIn 0.5s ease-in-out;
        }
        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-seedling me-2"></i>AgroPro - Expense Management
                <span id="connection-status" class="badge bg-secondary ms-2">
                    <i class="fas fa-circle" id="status-icon"></i> Connecting...
                </span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/add_expense"><i class="fas fa-plus me-1"></i>Add Expense</a>
                <a class="nav-link" href="/view_expenses"><i class="fas fa-list me-1"></i>View All</a>
                <a class="nav-link" href="/expense_analytics"><i class="fas fa-chart-bar me-1"></i>Analytics</a>
                <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Home</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card expense-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                        <h3>₹{{ "%.2f"|format(monthly_total) }}</h3>
                        <p class="mb-0">This Month</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <h3>₹{{ "%.2f"|format(total_expenses) }}</h3>
                        <p class="mb-0">Total Expenses</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-tags fa-2x mb-2"></i>
                        <h3>{{ category_summary|length }}</h3>
                        <p class="mb-0">Categories</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-receipt fa-2x mb-2"></i>
                        <h3>{{ recent_expenses|length }}</h3>
                        <p class="mb-0">Recent Entries</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Real-Time Search and Filters -->
            <div class="col-12 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-search me-2"></i>Real-Time Search & Filters</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Search Expenses</label>
                                <input type="text" id="realtime-search" class="form-control"
                                       placeholder="Search by description or category..." autocomplete="off">
                                <div id="search-results" class="mt-2"></div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">Category Filter</label>
                                <select id="category-filter" class="form-select">
                                    <option value="">All Categories</option>
                                    <option value="Seeds">Seeds</option>
                                    <option value="Fertilizers">Fertilizers</option>
                                    <option value="Labor">Labor</option>
                                    <option value="Pesticides">Pesticides</option>
                                    <option value="Irrigation">Irrigation</option>
                                    <option value="Equipment">Equipment</option>
                                    <option value="Transport">Transport</option>
                                    <option value="Packaging">Packaging</option>
                                    <option value="Fuel">Fuel</option>
                                    <option value="Maintenance">Maintenance</option>
                                    <option value="Insurance">Insurance</option>
                                    <option value="Others">Others</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">From Date</label>
                                <input type="date" id="date-from" class="form-control">
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">To Date</label>
                                <input type="date" id="date-to" class="form-control">
                            </div>
                            <div class="col-md-1 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <button id="clear-filters" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Expenses with Real-Time Updates -->
            <div class="col-lg-8 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Expenses</h5>
                        <div>
                            <button id="refresh-data" class="btn btn-sm btn-light me-2">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <span class="badge bg-light text-dark" id="expense-count">{{ recent_expenses|length }}</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="expenses-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Category</th>
                                        <th>Description</th>
                                        <th>Amount</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="expenses-tbody">
                                    {% for expense in recent_expenses %}
                                    <tr data-expense-id="{{ expense[0] }}" class="expense-row">
                                        <td>{{ expense[1] }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ expense[2] }}</span>
                                        </td>
                                        <td>{{ expense[4] or 'No description' }}</td>
                                        <td class="text-end">
                                            <strong>₹{{ "%.2f"|format(expense[3]) }}</strong>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary edit-expense"
                                                    data-expense-id="{{ expense[0] }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-expense"
                                                    data-expense-id="{{ expense[0] }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr id="no-expenses-row">
                                        <td colspan="5" class="text-center text-muted">
                                            <i class="fas fa-inbox fa-2x mb-2"></i>
                                            <p>No expenses recorded yet</p>
                                            <a href="/add_expense" class="btn btn-primary">Add Your First Expense</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Category Summary -->
            <div class="col-lg-4 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-pie-chart me-2"></i>Category Breakdown (30 Days)</h5>
                    </div>
                    <div class="card-body">
                        {% for category in category_summary %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-0">{{ category[0] }}</h6>
                                <small class="text-muted">{{ category[2] }} entries</small>
                            </div>
                            <div class="text-end">
                                <strong>₹{{ "%.2f"|format(category[1]) }}</strong>
                            </div>
                        </div>
                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: {{ (category[1] / category_summary[0][1] * 100) if category_summary else 0 }}%">
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-chart-pie fa-3x mb-2"></i>
                            <p>No category data available</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-Time Analytics Dashboard -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Monthly Trends</h5>
                        <select id="trend-period" class="form-select form-select-sm" style="width: auto;">
                            <option value="30">Last 30 Days</option>
                            <option value="90">Last 3 Months</option>
                            <option value="180">Last 6 Months</option>
                            <option value="365">Last Year</option>
                        </select>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="monthlyTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Daily Spending Pattern</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dailyPatternChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-Time Budget Tracking -->
        <div class="row">
            <div class="col-lg-8 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Budget vs Actual Spending</h5>
                        <button id="set-budgets" class="btn btn-sm btn-light">
                            <i class="fas fa-cog me-1"></i>Set Budgets
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="budget-analysis">
                            <div class="text-center text-muted">
                                <i class="fas fa-chart-pie fa-3x mb-2"></i>
                                <p>Set category budgets to track spending</p>
                                <button class="btn btn-primary" onclick="showBudgetModal()">Set Budgets</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-crystal-ball me-2"></i>Spending Predictions</h5>
                    </div>
                    <div class="card-body" id="predictions-container">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading predictions...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-Time Activity Feed -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card expense-card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-stream me-2"></i>Live Activity Feed</h5>
                    </div>
                    <div class="card-body">
                        <div id="activity-feed" style="max-height: 300px; overflow-y: auto;">
                            <div class="text-center text-muted">
                                <i class="fas fa-rss fa-2x mb-2"></i>
                                <p>Activity will appear here in real-time</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card expense-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <a href="/add_expense" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-plus fa-2x mb-2"></i><br>
                                    Add Expense
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/view_expenses" class="btn btn-info btn-lg w-100">
                                    <i class="fas fa-list fa-2x mb-2"></i><br>
                                    View All
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/expense_analytics" class="btn btn-success btn-lg w-100">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                                    Analytics
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-secondary btn-lg w-100" onclick="exportData()">
                                    <i class="fas fa-download fa-2x mb-2"></i><br>
                                    Export Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Expense Modal -->
    <div class="modal fade" id="editExpenseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Expense</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-expense-form">
                        <input type="hidden" id="edit-expense-id">
                        <div class="mb-3">
                            <label class="form-label">Date</label>
                            <input type="date" id="edit-date" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Category</label>
                            <select id="edit-category" class="form-select" required>
                                <option value="">Select Category</option>
                                <option value="Seeds">Seeds</option>
                                <option value="Fertilizers">Fertilizers</option>
                                <option value="Labor">Labor</option>
                                <option value="Pesticides">Pesticides</option>
                                <option value="Irrigation">Irrigation</option>
                                <option value="Equipment">Equipment</option>
                                <option value="Transport">Transport</option>
                                <option value="Packaging">Packaging</option>
                                <option value="Fuel">Fuel</option>
                                <option value="Maintenance">Maintenance</option>
                                <option value="Insurance">Insurance</option>
                                <option value="Others">Others</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Amount (₹)</label>
                            <input type="number" step="0.01" id="edit-amount" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea id="edit-description" class="form-control" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="save-expense-changes" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Budget Setting Modal -->
    <div class="modal fade" id="budgetModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Set Category Budgets</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row" id="budget-form">
                        <!-- Budget inputs will be generated dynamically -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="save-budgets" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>Save Budgets
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        // Monthly trend chart
        const ctx = document.getElementById('monthlyTrendChart').getContext('2d');
        const monthlyData = {{ monthly_totals | tojson }};
        
        const labels = monthlyData.map(item => item[0]);
        const data = monthlyData.map(item => item[1]);
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Monthly Expenses (₹)',
                    data: data,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount (₹)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });

        // Initialize Socket.IO for real-time updates
        const socket = io();
        let monthlyChart, dailyChart;
        let currentBudgets = {};
        let searchTimeout;

        // Connection status
        socket.on('connect', function() {
            console.log('Connected to real-time expense updates');
            updateConnectionStatus('connected');
            showNotification('Connected to real-time updates', 'success');
            socket.emit('get_live_data');
            socket.emit('get_realtime_analytics', { period: '30' });
            socket.emit('get_expense_predictions', {});
            addActivityFeedItem('Connected to real-time system', 'success');
        });

        socket.on('disconnect', function() {
            console.log('Disconnected from server');
            updateConnectionStatus('disconnected');
            showNotification('Connection lost - trying to reconnect...', 'warning');
        });

        socket.on('reconnect', function() {
            updateConnectionStatus('connected');
            showNotification('Reconnected to real-time updates', 'success');
            socket.emit('get_live_data');
        });

        // Real-time data updates
        socket.on('live_data_update', function(data) {
            updateDashboard(data);
        });

        socket.on('expense_added', function(data) {
            showNotification(data.message, 'success');
            updateDashboard(data.dashboard_data);
            // Add visual effect for new expense
            highlightNewExpense();
        });

        socket.on('expense_deleted', function(data) {
            showNotification(data.message, 'info');
            updateDashboard(data.dashboard_data);
        });

        socket.on('error', function(data) {
            showNotification(data.message, 'danger');
            addActivityFeedItem('Error: ' + data.message, 'danger');
        });

        // Real-time search results
        socket.on('search_results', function(data) {
            displaySearchResults(data.results, data.search_term);
        });

        // Real-time filter results
        socket.on('filtered_expenses', function(data) {
            updateExpensesTable(data.expenses);
            updateFilterSummary(data);
            addActivityFeedItem(`Filtered ${data.count} expenses`, 'info');
        });

        // Real-time analytics updates
        socket.on('realtime_analytics', function(data) {
            updateAnalyticsCharts(data);
            addActivityFeedItem('Analytics updated', 'info');
        });

        // Budget analysis updates
        socket.on('budget_analysis', function(data) {
            updateBudgetAnalysis(data.analysis);
        });

        // Expense predictions
        socket.on('expense_predictions', function(data) {
            updatePredictions(data.predictions);
        });

        // Expense updated
        socket.on('expense_updated', function(data) {
            showNotification(data.message, 'success');
            updateDashboard(data.dashboard_data);
            addActivityFeedItem(data.message, 'success');
        });

        // Expense details
        socket.on('expense_details', function(data) {
            populateEditModal(data);
        });

        // Update dashboard with real-time data
        function updateDashboard(data) {
            // Update summary cards
            document.querySelector('.metric-card:nth-child(1) h3').textContent = '₹' + data.monthly_total.toFixed(2);
            document.querySelector('.metric-card:nth-child(2) h3').textContent = '₹' + data.total_expenses.toFixed(2);
            document.querySelector('.metric-card:nth-child(3) h3').textContent = data.category_summary.length;
            document.querySelector('.metric-card:nth-child(4) h3').textContent = data.recent_expenses.length;

            // Update recent expenses table
            updateRecentExpensesTable(data.recent_expenses);

            // Update category summary
            updateCategorySummary(data.category_summary);

            // Update chart if needed
            if (monthlyChart) {
                // Request updated chart data
                fetch('/api/chart_data')
                    .then(response => response.json())
                    .then(chartData => updateChart(chartData));
            }
        }

        function updateRecentExpensesTable(expenses) {
            const tbody = document.querySelector('.table tbody');
            tbody.innerHTML = '';

            if (expenses.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>No expenses recorded yet</p>
                            <a href="/add_expense" class="btn btn-primary">Add Your First Expense</a>
                        </td>
                    </tr>
                `;
                return;
            }

            expenses.forEach(expense => {
                const row = document.createElement('tr');
                row.className = 'expense-row';
                row.innerHTML = `
                    <td>${expense.date}</td>
                    <td><span class="badge bg-secondary">${expense.category}</span></td>
                    <td>${expense.description || 'No description'}</td>
                    <td class="text-end"><strong>₹${expense.amount.toFixed(2)}</strong></td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateCategorySummary(categories) {
            const container = document.querySelector('.card-body').parentElement.querySelector('.card-body');
            // Find the category summary container
            const categoryContainer = document.querySelector('.col-lg-4 .card-body');

            if (categories.length === 0) {
                categoryContainer.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-chart-pie fa-3x mb-2"></i>
                        <p>No category data available</p>
                    </div>
                `;
                return;
            }

            let html = '';
            const maxAmount = categories[0]?.total || 1;

            categories.forEach(category => {
                const percentage = (category.total / maxAmount * 100);
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">${category.category}</h6>
                            <small class="text-muted">${category.count} entries</small>
                        </div>
                        <div class="text-end">
                            <strong>₹${category.total.toFixed(2)}</strong>
                        </div>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar" role="progressbar" style="width: ${percentage}%"></div>
                    </div>
                `;
            });

            categoryContainer.innerHTML = html;
        }

        function highlightNewExpense() {
            const firstRow = document.querySelector('.expense-row');
            if (firstRow) {
                firstRow.style.backgroundColor = '#d4edda';
                firstRow.style.transition = 'background-color 3s ease';
                setTimeout(() => {
                    firstRow.style.backgroundColor = '';
                }, 3000);
            }
        }

        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Daily pattern chart
        const dailyCtx = document.getElementById('dailyPatternChart').getContext('2d');
        dailyChart = new Chart(dailyCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Daily Expenses (₹)',
                    data: [],
                    backgroundColor: 'rgba(255, 193, 7, 0.6)',
                    borderColor: 'rgba(255, 193, 7, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount (₹)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connection-status');
            const iconElement = document.getElementById('status-icon');

            if (status === 'connected') {
                statusElement.className = 'badge bg-success ms-2';
                statusElement.innerHTML = '<i class="fas fa-circle" id="status-icon"></i> Live';
                iconElement.style.animation = 'none';
            } else if (status === 'disconnected') {
                statusElement.className = 'badge bg-danger ms-2';
                statusElement.innerHTML = '<i class="fas fa-circle" id="status-icon"></i> Offline';
            } else {
                statusElement.className = 'badge bg-warning ms-2';
                statusElement.innerHTML = '<i class="fas fa-circle" id="status-icon"></i> Connecting...';
                iconElement.style.animation = 'pulse 1s infinite';
            }
        }

        // Real-time search functionality
        document.getElementById('realtime-search').addEventListener('input', function() {
            const searchTerm = this.value.trim();

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (searchTerm.length >= 2) {
                    socket.emit('search_expenses_realtime', { search_term: searchTerm });
                } else {
                    document.getElementById('search-results').innerHTML = '';
                }
            }, 300);
        });

        // Real-time filtering
        function applyFilters() {
            const filters = {
                category: document.getElementById('category-filter').value,
                date_from: document.getElementById('date-from').value,
                date_to: document.getElementById('date-to').value,
                search_term: document.getElementById('realtime-search').value
            };

            socket.emit('filter_expenses_realtime', filters);
            addActivityFeedItem('Applying filters...', 'info');
        }

        // Filter event listeners
        document.getElementById('category-filter').addEventListener('change', applyFilters);
        document.getElementById('date-from').addEventListener('change', applyFilters);
        document.getElementById('date-to').addEventListener('change', applyFilters);

        // Clear filters
        document.getElementById('clear-filters').addEventListener('click', function() {
            document.getElementById('category-filter').value = '';
            document.getElementById('date-from').value = '';
            document.getElementById('date-to').value = '';
            document.getElementById('realtime-search').value = '';
            document.getElementById('search-results').innerHTML = '';
            socket.emit('get_live_data');
        });

        // Refresh data button
        document.getElementById('refresh-data').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            socket.emit('get_live_data');
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-sync-alt"></i>';
            }, 1000);
        });

        // Analytics period change
        document.getElementById('trend-period').addEventListener('change', function() {
            socket.emit('get_realtime_analytics', { period: this.value });
        });

        // Edit expense functionality
        document.addEventListener('click', function(e) {
            if (e.target.closest('.edit-expense')) {
                const expenseId = e.target.closest('.edit-expense').dataset.expenseId;
                socket.emit('get_expense_details', { expense_id: expenseId });
                document.getElementById('edit-expense-id').value = expenseId;
            }

            if (e.target.closest('.delete-expense')) {
                const expenseId = e.target.closest('.delete-expense').dataset.expenseId;
                if (confirm('Are you sure you want to delete this expense?')) {
                    socket.emit('delete_expense_realtime', { expense_id: expenseId });
                }
            }
        });

        // Save expense changes
        document.getElementById('save-expense-changes').addEventListener('click', function() {
            const expenseData = {
                expense_id: document.getElementById('edit-expense-id').value,
                date: document.getElementById('edit-date').value,
                category: document.getElementById('edit-category').value,
                amount: document.getElementById('edit-amount').value,
                description: document.getElementById('edit-description').value
            };

            socket.emit('update_expense_realtime', expenseData);
            bootstrap.Modal.getInstance(document.getElementById('editExpenseModal')).hide();
        });

        // Request live data every 30 seconds
        setInterval(() => {
            socket.emit('get_live_data');
            socket.emit('get_realtime_analytics', { period: document.getElementById('trend-period').value });
        }, 30000);

        // Export data function
        function exportData() {
            // Get current data and export
            fetch('/api/export_expenses')
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'expenses_' + new Date().toISOString().split('T')[0] + '.csv';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                });
        }

        // Helper functions for real-time features
        function displaySearchResults(results, searchTerm) {
            const container = document.getElementById('search-results');

            if (results.length === 0) {
                container.innerHTML = '<small class="text-muted">No results found</small>';
                return;
            }

            let html = '<div class="list-group list-group-flush">';
            results.slice(0, 5).forEach(result => {
                html += `
                    <div class="list-group-item list-group-item-action py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">${result.date}</small>
                                <span class="badge bg-secondary ms-2">${result.category}</span>
                                <div class="mt-1">${result.description || 'No description'}</div>
                            </div>
                            <strong>₹${result.amount.toFixed(2)}</strong>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            if (results.length > 5) {
                html += `<small class="text-muted">Showing 5 of ${results.length} results</small>`;
            }

            container.innerHTML = html;
        }

        function updateExpensesTable(expenses) {
            const tbody = document.getElementById('expenses-tbody');
            tbody.innerHTML = '';

            if (expenses.length === 0) {
                tbody.innerHTML = `
                    <tr id="no-expenses-row">
                        <td colspan="5" class="text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>No expenses found</p>
                        </td>
                    </tr>
                `;
                return;
            }

            expenses.forEach(expense => {
                const row = document.createElement('tr');
                row.className = 'expense-row';
                row.dataset.expenseId = expense.id;
                row.innerHTML = `
                    <td>${expense.date}</td>
                    <td><span class="badge bg-secondary">${expense.category}</span></td>
                    <td>${expense.description || 'No description'}</td>
                    <td class="text-end"><strong>₹${expense.amount.toFixed(2)}</strong></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary edit-expense" data-expense-id="${expense.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-expense" data-expense-id="${expense.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update count
            document.getElementById('expense-count').textContent = expenses.length;
        }

        function updateFilterSummary(data) {
            // Update any filter summary displays
            const filters = data.filters;
            let filterText = 'Showing ';

            if (filters.category) filterText += `${filters.category} expenses `;
            if (filters.date_from || filters.date_to) {
                filterText += `from ${filters.date_from || 'start'} to ${filters.date_to || 'end'} `;
            }
            if (filters.search_term) filterText += `matching "${filters.search_term}" `;

            filterText += `(${data.count} results, ₹${data.total_amount.toFixed(2)} total)`;

            // You can display this summary somewhere in the UI
            console.log(filterText);
        }

        function updateAnalyticsCharts(data) {
            // Update monthly trend chart
            if (monthlyChart) {
                monthlyChart.data.labels = data.monthly_data.map(item => item[0]);
                monthlyChart.data.datasets[0].data = data.monthly_data.map(item => item[1]);
                monthlyChart.update('none');
            }

            // Update daily pattern chart
            if (dailyChart) {
                dailyChart.data.labels = data.daily_data.map(item => item[0]);
                dailyChart.data.datasets[0].data = data.daily_data.map(item => item[1]);
                dailyChart.update('none');
            }
        }

        function updateBudgetAnalysis(analysis) {
            const container = document.getElementById('budget-analysis');

            if (analysis.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-chart-pie fa-3x mb-2"></i>
                        <p>Set category budgets to track spending</p>
                        <button class="btn btn-primary" onclick="showBudgetModal()">Set Budgets</button>
                    </div>
                `;
                return;
            }

            let html = '';
            analysis.forEach(item => {
                const statusColor = item.status === 'good' ? 'success' :
                                  item.status === 'warning' ? 'warning' : 'danger';

                html += `
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <h6 class="mb-0">${item.category}</h6>
                            <span class="badge bg-${statusColor}">${item.percentage.toFixed(1)}%</span>
                        </div>
                        <div class="progress mb-1" style="height: 8px;">
                            <div class="progress-bar bg-${statusColor}" style="width: ${Math.min(item.percentage, 100)}%"></div>
                        </div>
                        <small class="text-muted">
                            ₹${item.spent.toFixed(2)} of ₹${item.budget.toFixed(2)}
                            (₹${item.remaining.toFixed(2)} remaining)
                        </small>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function updatePredictions(predictions) {
            const container = document.getElementById('predictions-container');

            const trendIcon = predictions.trend === 'increasing' ? 'fa-arrow-up text-danger' :
                            predictions.trend === 'decreasing' ? 'fa-arrow-down text-success' :
                            'fa-minus text-warning';

            container.innerHTML = `
                <div class="text-center mb-3">
                    <h3 class="text-primary">₹${predictions.next_month.toFixed(2)}</h3>
                    <p class="mb-0">Predicted next month</p>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Trend:</span>
                    <span><i class="fas ${trendIcon} me-1"></i>${predictions.trend}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Confidence:</span>
                    <span class="badge bg-secondary">${predictions.confidence}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Historical Avg:</span>
                    <span>₹${predictions.historical_avg?.toFixed(2) || '0.00'}</span>
                </div>
            `;
        }

        function populateEditModal(expense) {
            document.getElementById('edit-date').value = expense.date;
            document.getElementById('edit-category').value = expense.category;
            document.getElementById('edit-amount').value = expense.amount;
            document.getElementById('edit-description').value = expense.description || '';

            new bootstrap.Modal(document.getElementById('editExpenseModal')).show();
        }

        function addActivityFeedItem(message, type) {
            const feed = document.getElementById('activity-feed');
            const timestamp = new Date().toLocaleTimeString();

            const iconClass = type === 'success' ? 'fa-check-circle text-success' :
                            type === 'danger' ? 'fa-exclamation-triangle text-danger' :
                            type === 'warning' ? 'fa-exclamation-circle text-warning' :
                            'fa-info-circle text-info';

            const item = document.createElement('div');
            item.className = 'border-bottom pb-2 mb-2';
            item.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas ${iconClass} me-2"></i>
                    <div class="flex-grow-1">
                        <div>${message}</div>
                        <small class="text-muted">${timestamp}</small>
                    </div>
                </div>
            `;

            // Clear placeholder if exists
            if (feed.querySelector('.text-center')) {
                feed.innerHTML = '';
            }

            feed.insertBefore(item, feed.firstChild);

            // Keep only last 20 items
            while (feed.children.length > 20) {
                feed.removeChild(feed.lastChild);
            }
        }

        function showBudgetModal() {
            // Generate budget form for all categories
            const categories = ['Seeds', 'Fertilizers', 'Labor', 'Pesticides', 'Irrigation',
                              'Equipment', 'Transport', 'Packaging', 'Fuel', 'Maintenance',
                              'Insurance', 'Others'];

            const form = document.getElementById('budget-form');
            form.innerHTML = '';

            categories.forEach(category => {
                const col = document.createElement('div');
                col.className = 'col-md-6 mb-3';
                col.innerHTML = `
                    <label class="form-label">${category}</label>
                    <div class="input-group">
                        <span class="input-group-text">₹</span>
                        <input type="number" class="form-control" data-category="${category}"
                               value="${currentBudgets[category] || ''}" placeholder="0.00">
                    </div>
                `;
                form.appendChild(col);
            });

            new bootstrap.Modal(document.getElementById('budgetModal')).show();
        }

        // Save budgets
        document.getElementById('save-budgets').addEventListener('click', function() {
            const inputs = document.querySelectorAll('#budget-form input');
            const budgets = {};

            inputs.forEach(input => {
                const category = input.dataset.category;
                const amount = parseFloat(input.value) || 0;
                if (amount > 0) {
                    budgets[category] = amount;
                }
            });

            currentBudgets = budgets;
            socket.emit('get_budget_analysis', { budgets: budgets });

            bootstrap.Modal.getInstance(document.getElementById('budgetModal')).hide();
            showNotification('Budgets saved successfully', 'success');
        });
    </script>
</body>
</html>
