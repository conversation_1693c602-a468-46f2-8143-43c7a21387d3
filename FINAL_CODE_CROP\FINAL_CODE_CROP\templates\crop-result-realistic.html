{% extends 'layout.html' %}

{% block content %}
<style>
  body {
    background-image: linear-gradient(rgba(44, 90, 39, 0.2), rgba(74, 124, 89, 0.2)), 
                      url('/static/images/Background4.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    min-height: 100vh;
  }
  
  .result-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }
  
  .page-header {
    background: rgba(44, 90, 39, 0.95);
    color: white;
    padding: 20px 30px;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(44, 90, 39, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .page-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 5px;
  }
  
  .page-subtitle {
    font-size: 0.95rem;
    opacity: 0.9;
  }
  
  .result-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
  }
  
  .recommendation-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border-left: 5px solid #2c5a27;
  }
  
  .crop-image-container {
    width: 200px;
    height: 150px;
    margin: 0 auto 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #4a7c59 0%, #2c5a27 100%);
    position: relative;
  }
  
  .crop-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .crop-image:hover {
    transform: scale(1.05);
  }
  
  .crop-image-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
    background: linear-gradient(135deg, #4a7c59 0%, #2c5a27 100%);
  }
  
  .crop-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin: 20px 0;
    text-align: left;
  }
  
  .crop-info-item {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    border-left: 3px solid #2c5a27;
  }
  
  .crop-info-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 3px;
  }
  
  .crop-info-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
  }
  
  .crop-icon {
    font-size: 4rem;
    margin-bottom: 15px;
    display: block;
  }
  
  .crop-name {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2c5a27;
    margin-bottom: 20px;
    text-transform: capitalize;
  }
  
  .confidence-section {
    margin: 25px 0;
  }
  
  .confidence-label {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 10px;
    font-weight: 500;
  }
  
  .confidence-bar-container {
    background: #f8f9fa;
    height: 35px;
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid #e9ecef;
    position: relative;
  }
  
  .confidence-bar {
    height: 100%;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    transition: width 2s ease-in-out;
    position: relative;
    overflow: hidden;
  }
  
  .confidence-high { background: linear-gradient(90deg, #28a745, #20c997); }
  .confidence-medium { background: linear-gradient(90deg, #ffc107, #fd7e14); }
  .confidence-low { background: linear-gradient(90deg, #dc3545, #e74c3c); }
  
  .recommendation-status {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 600;
    margin-top: 15px;
  }
  
  .status-high { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
  .status-medium { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
  .status-low { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
  
  .analysis-panel {
    background: rgba(248, 249, 250, 0.95);
    border-radius: 12px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  .panel-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 25px;
  }
  
  .panel-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
  }
  
  .panel-icon {
    color: #2c5a27;
    font-size: 1.5rem;
  }
  
  .conditions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  .condition-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #2c5a27;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
  }
  
  .condition-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .condition-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .condition-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
  }
  
  .condition-unit {
    font-size: 1rem;
    color: #6c757d;
    font-weight: 400;
  }
  
  .actions-section {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    margin-top: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
  }
  
  .actions-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 25px;
  }
  
  .btn-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .btn {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-width: 160px;
    justify-content: center;
  }
  
  .btn-primary {
    background: #2c5a27;
    color: white;
    box-shadow: 0 4px 12px rgba(44, 90, 39, 0.3);
  }
  
  .btn-primary:hover {
    background: #1e3f1b;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(44, 90, 39, 0.4);
    color: white;
    text-decoration: none;
  }
  
  .btn-secondary {
    background: #6c757d;
    color: white;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
  }
  
  .btn-secondary:hover {
    background: #545b62;
    color: white;
    text-decoration: none;
  }
  
  .btn-outline {
    background: transparent;
    color: #2c5a27;
    border: 2px solid #2c5a27;
  }
  
  .btn-outline:hover {
    background: #2c5a27;
    color: white;
    text-decoration: none;
  }
  
  .analysis-date {
    text-align: center;
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
  }
  
  @media (max-width: 768px) {
    .result-layout {
      grid-template-columns: 1fr;
    }
    .conditions-grid {
      grid-template-columns: 1fr;
    }
    .btn-group {
      flex-direction: column;
      align-items: center;
    }
    .page-title {
      font-size: 1.5rem;
    }
    .crop-name {
      font-size: 1.6rem;
    }
    .page-header {
      padding: 15px 20px;
      margin-bottom: 15px;
    }
  }
</style>

<div class="result-container">
  <div class="page-header">
    <h1 class="page-title">Crop Recommendation Analysis</h1>
    <p class="page-subtitle">AI-Powered Agricultural Decision Support System</p>
  </div>

  <div class="result-layout">
    <!-- Recommendation Card -->
    <div class="recommendation-card">
      <!-- Crop Image -->
      <div class="crop-image-container">
        {% if crop_info and crop_info.image %}
          <img src="{{ crop_info.image }}" alt="{{ prediction.title() }}" class="crop-image"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
          <div class="crop-image-fallback" style="display: none;">
            {{ crop_info.emoji if crop_info.emoji else '�' }}
          </div>
        {% else %}
          <div class="crop-image-fallback">
            🌱
          </div>
        {% endif %}
      </div>
      
      <h2 class="crop-name">{{ prediction.title() }}</h2>
      
      {% if crop_info and crop_info.description %}
      <p style="color: #6c757d; font-size: 0.95rem; margin: 15px 0; line-height: 1.5;">
        {{ crop_info.description }}
      </p>
      {% endif %}
      
      <!-- Crop Information Grid -->
      {% if crop_info %}
      <div class="crop-info-grid">
        <div class="crop-info-item">
          <div class="crop-info-label">Season</div>
          <div class="crop-info-value">{{ crop_info.season }}</div>
        </div>
        <div class="crop-info-item">
          <div class="crop-info-label">Duration</div>
          <div class="crop-info-value">{{ crop_info.duration }}</div>
        </div>
        <div class="crop-info-item">
          <div class="crop-info-label">Ideal Temp</div>
          <div class="crop-info-value">{{ crop_info.ideal_temp }}</div>
        </div>
        <div class="crop-info-item">
          <div class="crop-info-label">Water Req</div>
          <div class="crop-info-value">{{ crop_info.water_req }}</div>
        </div>
      </div>
      {% endif %}
      
      {% if confidence %}
      <div class="confidence-section">
        <div class="confidence-label">Recommendation Confidence</div>
        <div class="confidence-bar-container">
          <div class="confidence-bar confidence-{% if confidence >= 75 %}high{% elif confidence >= 50 %}medium{% else %}low{% endif %}" 
               style="width: {{ confidence }}%;">
            {{ confidence }}%
          </div>
        </div>
        <div class="recommendation-status status-{% if confidence >= 75 %}high{% elif confidence >= 50 %}medium{% else %}low{% endif %}">
          {% if confidence >= 75 %}
            ✅ Highly Recommended - Excellent match for your conditions
          {% elif confidence >= 50 %}
            ⚠️ Good Match - Suitable for your soil and climate
          {% else %}
            💡 Alternative Recommendation - Consider multiple crops or adjust conditions
          {% endif %}
        </div>
        {% if confidence < 50 %}
        <div style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 15px; margin-top: 15px; font-size: 0.9rem; color: #1565c0;">
          <strong>💡 Low Confidence Explanation:</strong><br>
          Your soil conditions are borderline for multiple crops. This means:
          <ul style="margin: 8px 0; padding-left: 20px;">
            <li>{{ prediction.title() }} can still grow, but may not be optimal</li>
            <li>Consider testing nearby parameter ranges</li>
            <li>Multiple crop types might be suitable</li>
            <li>Consult local agricultural experts for region-specific advice</li>
          </ul>
        </div>
        {% endif %}
        
        {% if alternatives and alternatives|length > 0 %}
        <div style="background: #f3e5f5; border: 1px solid #9c27b0; border-radius: 8px; padding: 15px; margin-top: 15px; font-size: 0.9rem; color: #6a1b9a;">
          <strong>🌱 Alternative Crop Suggestions:</strong><br>
          Based on your conditions, these crops might also be suitable:
          <div style="margin-top: 10px;">
            {% for crop, conf in alternatives %}
            <span style="display: inline-block; background: rgba(156, 39, 176, 0.1); border: 1px solid rgba(156, 39, 176, 0.3); border-radius: 15px; padding: 5px 12px; margin: 3px; font-size: 0.85rem;">
              {{ crop.title() }} ({{ conf }}%)
            </span>
            {% endfor %}
          </div>
        </div>
        {% endif %}
      </div>
      {% endif %}
    </div>

    <!-- Analysis Panel -->
    <div class="analysis-panel">
      <div class="panel-header">
        <i class="fas fa-chart-bar panel-icon"></i>
        <h3 class="panel-title">Soil Analysis Report</h3>
      </div>
      
      <div class="conditions-grid">
        <div class="condition-item">
          <div class="condition-label">Nitrogen (N)</div>
          <div class="condition-value">{{ nitrogen }} <span class="condition-unit">kg/ha</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Phosphorous (P)</div>
          <div class="condition-value">{{ phosphorous }} <span class="condition-unit">kg/ha</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Potassium (K)</div>
          <div class="condition-value">{{ potassium }} <span class="condition-unit">kg/ha</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">pH Level</div>
          <div class="condition-value">{{ ph_level }}</div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Temperature</div>
          <div class="condition-value">{{ temperature }} <span class="condition-unit">°C</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Humidity</div>
          <div class="condition-value">{{ humidity }} <span class="condition-unit">%</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Rainfall</div>
          <div class="condition-value">{{ rainfall }} <span class="condition-unit">mm</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Analysis Date</div>
          <div class="condition-value" style="font-size: 1.1rem;">{{ moment().format('MMM DD, YYYY') if moment else 'Today' }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions Section -->
  <div class="actions-section">
    <h3 class="actions-title">Available Actions</h3>
    <div class="btn-group">
      <a href="/crop-recommend" class="btn btn-primary">
        <i class="fas fa-redo"></i> New Analysis
      </a>
      <a href="/index.html" class="btn btn-secondary">
        <i class="fas fa-home"></i> Dashboard
      </a>
      <a href="#" class="btn btn-outline" onclick="window.print()">
        <i class="fas fa-print"></i> Print Report
      </a>
      <a href="#" class="btn btn-outline" onclick="downloadReport()">
        <i class="fas fa-download"></i> Download PDF
      </a>
    </div>
    
    <div class="analysis-date">
      Report generated on {{ moment().format('MMMM DD, YYYY [at] HH:mm') if moment else 'Today' }} by AgroPro AI System
    </div>
  </div>
</div>

<script>
// Animate confidence bar on page load
document.addEventListener('DOMContentLoaded', function() {
  const confidenceBar = document.querySelector('.confidence-bar');
  if (confidenceBar) {
    const targetWidth = confidenceBar.style.width;
    confidenceBar.style.width = '0%';
    setTimeout(() => {
      confidenceBar.style.width = targetWidth;
    }, 500);
  }
});

function downloadReport() {
  // Simulate PDF download
  alert('PDF download feature coming soon! For now, you can use the print option.');
}
</script>

{% endblock %}
