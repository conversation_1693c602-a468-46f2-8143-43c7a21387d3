{% extends 'layout.html' %}

{% block content %}
<div class="container-fluid py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); min-height: 100vh;">
  <div class="row align-items-center">
    <div class="col-lg-6 text-center">
      <div class="hero-image fade-in">
        <img
          src="../static/images/bg.svg"
          alt="Crop Recommendation"
          class="img-fluid rounded-4 shadow-lg"
          style="max-height: 500px; width: auto;"
        />
      </div>
    </div>
    <div class="col-lg-6">
      <div class="card card-modern shadow-lg">
        <div class="card-header card-header-modern text-center">
          <h2 class="mb-0">
            <i class="fas fa-seedling me-2"></i>
            AI Crop Recommendation
          </h2>
          <p class="mb-0 mt-2 opacity-75">Find the most suitable crop for your farm</p>
        </div>
        <div class="card-body p-4">
          {% if error %}
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Error:</strong> {{ error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
          {% endif %}

          <form method="POST" action="{{ url_for('crop_predict') }}" class="needs-validation" novalidate>
            <div class="row g-3">
              <div class="col-md-6">
                <label for="Nitrogen" class="form-label fw-semibold">
                  <i class="fas fa-flask text-primary me-1"></i>Nitrogen (N)
                </label>
                <input
                  type="number"
                  class="form-control form-control-modern"
                  id="Nitrogen"
                  name="nitrogen"
                  value="{{n}}"
                  min="1"
                  max="140"
                  step="0.01"
                  placeholder="Enter nitrogen level"
                  required
                />
                <div class="form-text">Range: 1-140 kg/ha</div>
                <div class="invalid-feedback">
                  Please provide a valid nitrogen level (1-140 kg/ha).
                </div>
              </div>

              <div class="col-md-6">
                <label for="Phosphorous" class="form-label fw-semibold">
                  <i class="fas fa-flask text-warning me-1"></i>Phosphorous (P)
                </label>
                <input
                  type="number"
                  class="form-control form-control-modern"
                  id="Phosphorous"
                  name="phosphorous"
                  value="{{p}}"
                  min="5"
                  max="145"
                  step="0.01"
                  placeholder="Enter phosphorous level"
                  required
                />
                <div class="form-text">Range: 5-145 kg/ha</div>
                <div class="invalid-feedback">
                  Please provide a valid phosphorous level (5-145 kg/ha).
                </div>
              </div>

              <div class="col-md-6">
                <label for="Pottasium" class="form-label fw-semibold">
                  <i class="fas fa-flask text-success me-1"></i>Potassium (K)
                </label>
                <input
                  type="number"
                  class="form-control form-control-modern"
                  id="Pottasium"
                  name="pottasium"
                  value="{{k}}"
                  min="5"
                  max="205"
                  step="0.01"
                  placeholder="Enter potassium level"
                  required
                />
                <div class="form-text">Range: 5-205 kg/ha</div>
                <div class="invalid-feedback">
                  Please provide a valid potassium level (5-205 kg/ha).
                </div>
              </div>
              <div class="col-md-6">
                <label for="ph" class="form-label fw-semibold">
                  <i class="fas fa-vial text-info me-1"></i>pH Level
                </label>
                <input
                  type="number"
                  step="0.01"
                  class="form-control form-control-modern"
                  id="ph"
                  name="ph"
                  min="4"
                  max="10"
                  placeholder="Enter pH level"
                  required
                />
                <div class="form-text">Range: 4.0-10.0</div>
                <div class="invalid-feedback">
                  Please provide a valid pH level (4.0-10.0).
                </div>
              </div>

              <div class="col-md-6">
                <label for="Rainfall" class="form-label fw-semibold">
                  <i class="fas fa-cloud-rain text-primary me-1"></i>Rainfall (mm)
                </label>
                <input
                  type="number"
                  step="0.01"
                  class="form-control form-control-modern"
                  id="Rainfall"
                  name="rainfall"
                  min="20"
                  max="300"
                  placeholder="Enter rainfall amount"
                  required
                />
                <div class="form-text">Range: 20-300 mm</div>
                <div class="invalid-feedback">
                  Please provide a valid rainfall amount (20-300 mm).
                </div>
              </div>

              <div class="col-md-6">
                <label for="temp" class="form-label fw-semibold">
                  <i class="fas fa-thermometer-half text-danger me-1"></i>Temperature (°C)
                </label>
                <input
                  type="number"
                  step="0.01"
                  class="form-control form-control-modern"
                  id="temp"
                  name="temp"
                  min="8"
                  max="44"
                  value="{{temp}}"
                  placeholder="Enter temperature"
                  required
                />
                <div class="form-text">Range: 8-44°C</div>
                <div class="invalid-feedback">
                  Please provide a valid temperature (8-44°C).
                </div>
              </div>

              <div class="col-md-6">
                <label for="hum" class="form-label fw-semibold">
                  <i class="fas fa-tint text-info me-1"></i>Humidity (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  class="form-control form-control-modern"
                  id="hum"
                  value="{{hum}}"
                  min="14"
                  max="100"
                  name="hum"
                  placeholder="Enter humidity level"
                  required
                />
                <div class="form-text">Range: 14-100%</div>
                <div class="invalid-feedback">
                  Please provide a valid humidity level (14-100%).
                </div>
              </div>
            </div>

            <div class="text-center mt-4">
              <button
                type="submit"
                class="btn btn-primary-modern btn-lg px-5 py-3"
                id="recommendBtn"
              >
                <i class="fas fa-brain me-2"></i>Get AI Recommendation
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Form validation and enhancement
(function() {
  'use strict';
  
  // Get the form
  var form = document.querySelector('.needs-validation');
  
  if (form) {
    form.addEventListener('submit', function(event) {
      if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
      } else {
        // Show loading state
        var btn = document.getElementById('recommendBtn');
        if (btn) {
          btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
          btn.disabled = true;
        }
      }
      form.classList.add('was-validated');
    }, false);
    
    // Add real-time validation
    var inputs = form.querySelectorAll('input[type="number"]');
    inputs.forEach(function(input) {
      input.addEventListener('input', function() {
        var value = parseFloat(this.value);
        var min = parseFloat(this.min);
        var max = parseFloat(this.max);
        
        if (value < min || value > max || isNaN(value)) {
          this.setCustomValidity('Value must be between ' + min + ' and ' + max);
        } else {
          this.setCustomValidity('');
        }
      });
    });
  }
})();
</script>

{% endblock %}
