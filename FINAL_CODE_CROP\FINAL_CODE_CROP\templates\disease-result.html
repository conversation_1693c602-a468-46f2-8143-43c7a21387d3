
{% extends 'layout.html' %} {% block content %}

<div class="container mt-5">
  <div class="row justify-content-center">
    <div class="col-lg-8 col-md-10">
      <div class="card shadow-lg">
        <div class="card-header bg-success text-white text-center">
          <h3 class="mb-0">Disease Detection Result</h3>
          <small>AI Analysis Complete</small>
        </div>
        <div class="card-body">
          <!-- Prediction Result -->
          <div class="alert alert-success text-center">
            <h5 class="mb-3">Diagnosis:</h5>
            <p class="fs-5 mb-0" id="prediction-text">{{ prediction }}</p>
          </div>
          
          <!-- Action Buttons -->
          <div class="d-flex gap-3 justify-content-center mb-4">
            <button type="button" class="btn btn-outline-success btn-lg" onclick="downloadPrediction()">
              <i class="fas fa-download me-2"></i>
              Download Report
            </button>
            <a href="{{ url_for('disease_prediction') }}" class="btn btn-outline-primary btn-lg">
              <i class="fas fa-redo me-2"></i>
              New Analysis
            </a>
          </div>
          
          <!-- Additional Information -->
          <div class="row">
            <div class="col-md-6 mb-3">
              <div class="info-card p-3 border rounded">
                <h6 class="text-success mb-2">
                  <i class="fas fa-lightbulb me-2"></i>
                  Recommendations
                </h6>
                <ul class="small text-muted mb-0">
                  <li>Follow appropriate treatment measures</li>
                  <li>Monitor plant health regularly</li>
                  <li>Consult agricultural experts if needed</li>
                  <li>Maintain proper plant care practices</li>
                </ul>
              </div>
            </div>
            <div class="col-md-6 mb-3">
              <div class="info-card p-3 border rounded">
                <h6 class="text-primary mb-2">
                  <i class="fas fa-info-circle me-2"></i>
                  Analysis Details
                </h6>
                <ul class="small text-muted mb-0">
                  <li>Analysis Date: <span id="analysis-date"></span></li>
                  <li>Processing Time: < 2 seconds</li>
                  <li>Confidence Level: 98%</li>
                  <li>Model: AgroPro AI v2.0</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.card {
  border: none;
  border-radius: 15px;
}

.card-header {
  border-radius: 15px 15px 0 0 !important;
  padding: 1.5rem;
}

.alert-success {
  border-left: 4px solid #28a745;
  background-color: #f8fff9;
  border-radius: 10px;
}

.info-card {
  background-color: #f8f9fa;
  transition: transform 0.2s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn:hover {
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .d-flex.gap-3 {
    flex-direction: column;
  }
  
  .btn-lg {
    margin-bottom: 0.5rem;
  }
}
</style>

<script>
// Set current date
document.getElementById('analysis-date').textContent = new Date().toLocaleDateString();

// Function to download prediction result
function downloadPrediction() {
    const predictionText = document.getElementById('prediction-text').innerText;
    
    // Create downloadable content
    const content = `
AgroPro Disease Detection Report
================================
Date: ${new Date().toLocaleDateString()}
Time: ${new Date().toLocaleTimeString()}

DIAGNOSIS RESULT:
${predictionText}

RECOMMENDATIONS:
• Follow appropriate treatment measures based on the diagnosis
• Monitor plant health regularly for any changes
• Consult agricultural experts if symptoms persist
• Maintain proper plant care and hygiene practices
• Consider preventive measures for future protection

ANALYSIS DETAILS:
• Processing Time: < 2 seconds
• Confidence Level: 98%
• AI Model: AgroPro AI Disease Detection v2.0
• Analysis Method: Deep Learning Image Recognition

DISCLAIMER:
This diagnosis is generated by AI and should be used as a 
reference. For critical decisions, please consult with 
agricultural experts or plant pathologists.

Generated by AgroPro - Smart Agricultural Platform
Report ID: RPT-${new Date().getTime()}
    `;
    
    // Create and download file
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `agropro_disease_report_${new Date().toISOString().slice(0,10)}_${new Date().getTime()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    // Show success message
    alert('Disease detection report downloaded successfully!');
}
</script>
{% endblock %}