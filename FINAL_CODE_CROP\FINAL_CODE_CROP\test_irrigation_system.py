"""
Comprehensive Test Suite for Enhanced Irrigation Management System
Tests all components: ML models, API, dashboard, and integration
"""

import unittest
import pandas as pd
import numpy as np
import sqlite3
import tempfile
import os
from datetime import datetime, timedelta
import json

# Import irrigation system components
from enhanced_irrigation_api import EnhancedIrrigationAPI
from soil_moisture_monitor import SoilMoistureMonitor
from weather_irrigation_integration import WeatherIrrigationIntegrator
from irrigation_preprocessing import IrrigationDataPreprocessor
from irrigation_model import IrrigationPredictor

class TestIrrigationPreprocessing(unittest.TestCase):
    """Test irrigation data preprocessing"""
    
    def setUp(self):
        self.preprocessor = IrrigationDataPreprocessor()
        
        # Create sample data
        self.sample_data = pd.DataFrame({
            'crop_type': ['wheat', 'rice', 'corn'],
            'soil_type': ['loam', 'clay', 'sandy'],
            'climate_zone': ['temperate', 'tropical', 'arid'],
            'temperature': [25.0, 30.0, 35.0],
            'humidity': [65.0, 80.0, 40.0],
            'rainfall': [5.0, 15.0, 2.0],
            'wind_speed': [10.0, 8.0, 15.0],
            'solar_radiation': [20.0, 18.0, 25.0],
            'soil_moisture': [35.0, 45.0, 25.0],
            'soil_ph': [6.5, 6.8, 7.2],
            'soil_ec': [1.5, 1.2, 2.0],
            'crop_days': [30, 60, 45],
            'growth_stage': [1, 2, 1],
            'crop_coefficient': [1.0, 1.2, 0.8],
            'et0': [4.5, 5.2, 6.0],
            'etc': [4.5, 6.24, 4.8],
            'effective_rainfall': [4.0, 12.0, 1.6],
            'moisture_stress': [0.3, 0.1, 0.6],
            'irrigation_amount_mm': [25.0, 0.0, 35.0],
            'irrigation_needed': [1, 0, 1],
            'field_capacity': [35, 45, 25],
            'wilting_point': [15, 25, 10]
        })
    
    def test_feature_engineering(self):
        """Test feature engineering functionality"""
        processed_data = self.preprocessor.feature_engineering(self.sample_data)
        
        # Check if new features are created
        expected_features = ['water_stress', 'vpd', 'gdd', 'available_water', 
                           'water_availability_ratio', 'early_stage', 'mid_stage', 'late_stage']
        
        for feature in expected_features:
            self.assertIn(feature, processed_data.columns, f"Feature {feature} not created")
        
        # Check feature value ranges
        self.assertTrue(all(processed_data['water_availability_ratio'] >= 0), 
                       "Water availability ratio should be non-negative")
        self.assertTrue(all(processed_data['water_availability_ratio'] <= 1), 
                       "Water availability ratio should not exceed 1")
    
    def test_categorical_encoding(self):
        """Test categorical feature encoding"""
        encoded_data = self.preprocessor.encode_categorical_features(self.sample_data, fit=True)
        
        # Check if categorical columns are encoded
        categorical_columns = ['crop_type', 'soil_type', 'climate_zone']
        for col in categorical_columns:
            self.assertTrue(encoded_data[col].dtype in ['int64', 'int32'], 
                           f"Column {col} should be encoded as integer")
    
    def test_data_scaling(self):
        """Test feature scaling"""
        X = self.sample_data.select_dtypes(include=[np.number])
        X_scaled = self.preprocessor.scale_features(X, fit=True)
        
        # Check if scaling is applied
        self.assertEqual(X.shape, X_scaled.shape, "Scaled data should have same shape")
        self.assertIsInstance(X_scaled, pd.DataFrame, "Scaled data should be DataFrame")

class TestIrrigationModel(unittest.TestCase):
    """Test irrigation ML models"""
    
    def setUp(self):
        self.predictor = IrrigationPredictor()
        
        # Create sample training data
        np.random.seed(42)
        n_samples = 100
        
        self.X_train = pd.DataFrame({
            'feature_1': np.random.normal(0, 1, n_samples),
            'feature_2': np.random.normal(0, 1, n_samples),
            'feature_3': np.random.normal(0, 1, n_samples)
        })
        
        self.y_class = np.random.choice([0, 1], n_samples, p=[0.7, 0.3])
        self.y_reg = np.random.exponential(10, n_samples)
        
        self.X_test = self.X_train.iloc[:20].copy()
        self.y_class_test = self.y_class[:20]
        self.y_reg_test = self.y_reg[:20]
    
    def test_model_initialization(self):
        """Test model initialization"""
        self.predictor.initialize_models()
        
        self.assertGreater(len(self.predictor.classification_models), 0, 
                          "Classification models should be initialized")
        self.assertGreater(len(self.predictor.regression_models), 0, 
                          "Regression models should be initialized")
    
    def test_model_training(self):
        """Test model training process"""
        self.predictor.initialize_models()
        
        # Train models
        class_results = self.predictor.train_classification_models(
            self.X_train, self.y_class, self.X_test, self.y_class_test
        )
        
        reg_results = self.predictor.train_regression_models(
            self.X_train, self.y_reg, self.X_test, self.y_reg_test
        )
        
        # Check if models are trained
        self.assertIsNotNone(self.predictor.best_classifier, "Best classifier should be selected")
        self.assertIsNotNone(self.predictor.best_regressor, "Best regressor should be selected")
        
        # Check if results contain expected metrics
        for model_name, results in class_results.items():
            self.assertIn('test_accuracy', results, f"Test accuracy missing for {model_name}")
            self.assertIn('cv_mean', results, f"CV mean missing for {model_name}")
        
        for model_name, results in reg_results.items():
            self.assertIn('test_rmse', results, f"Test RMSE missing for {model_name}")
            self.assertIn('test_r2', results, f"Test R² missing for {model_name}")

class TestSoilMoistureMonitor(unittest.TestCase):
    """Test soil moisture monitoring system"""
    
    def setUp(self):
        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.monitor = SoilMoistureMonitor(db_path=self.temp_db.name)
    
    def tearDown(self):
        # Clean up temporary database
        os.unlink(self.temp_db.name)
    
    def test_sensor_management(self):
        """Test sensor addition and configuration"""
        # Add sensor
        result = self.monitor.add_sensor('TEST_001', 'Test_Field', 'FIELD_1', 'wheat')
        self.assertTrue(result, "Sensor should be added successfully")
        
        # Try to add duplicate sensor
        result = self.monitor.add_sensor('TEST_001', 'Test_Field', 'FIELD_1', 'wheat')
        self.assertFalse(result, "Duplicate sensor should not be added")
    
    def test_reading_recording(self):
        """Test moisture reading recording"""
        # Add sensor first
        self.monitor.add_sensor('TEST_002', 'Test_Field', 'FIELD_2', 'corn')
        
        # Record reading
        result = self.monitor.record_reading('TEST_002', 35.5, temperature=25.0, ph_level=6.8)
        self.assertTrue(result, "Reading should be recorded successfully")
        
        # Try to record for non-existent sensor
        result = self.monitor.record_reading('NONEXISTENT', 30.0)
        self.assertFalse(result, "Reading for non-existent sensor should fail")
    
    def test_data_retrieval(self):
        """Test data retrieval functions"""
        # Add sensor and readings
        self.monitor.add_sensor('TEST_003', 'Test_Field', 'FIELD_3', 'rice')
        self.monitor.record_reading('TEST_003', 40.0, temperature=28.0)
        
        # Get current readings
        current_readings = self.monitor.get_current_readings(location='Test_Field')
        self.assertIsInstance(current_readings, pd.DataFrame, "Should return DataFrame")
        
        # Get historical data
        historical_data = self.monitor.get_historical_data(sensor_id='TEST_003', days=1)
        self.assertIsInstance(historical_data, pd.DataFrame, "Should return DataFrame")

class TestWeatherIntegration(unittest.TestCase):
    """Test weather integration system"""
    
    def setUp(self):
        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.weather_integrator = WeatherIrrigationIntegrator(db_path=self.temp_db.name)
    
    def tearDown(self):
        # Clean up temporary database
        os.unlink(self.temp_db.name)
    
    def test_weather_data_processing(self):
        """Test weather data processing"""
        # Test fallback weather data
        weather_data = self.weather_integrator.get_fallback_weather_data('Test_Location')
        
        required_fields = ['temperature', 'humidity', 'rainfall', 'wind_speed', 'solar_radiation']
        for field in required_fields:
            self.assertIn(field, weather_data, f"Weather data should contain {field}")
            self.assertIsInstance(weather_data[field], (int, float), f"{field} should be numeric")
    
    def test_weather_score_calculation(self):
        """Test weather score calculation"""
        sample_weather = {
            'temperature': 25.0,
            'humidity': 65.0,
            'wind_speed': 10.0,
            'rainfall': 2.0
        }
        
        score = self.weather_integrator.calculate_weather_score(sample_weather)
        self.assertIsInstance(score, (int, float), "Weather score should be numeric")
        self.assertGreaterEqual(score, 0, "Weather score should be non-negative")
        self.assertLessEqual(score, 100, "Weather score should not exceed 100")
    
    def test_irrigation_urgency_determination(self):
        """Test irrigation urgency determination"""
        current_weather = {'temperature': 35.0, 'humidity': 30.0, 'wind_speed': 10.0, 'rainfall': 0.0}
        forecast = [{'expected_rainfall': 0.0}, {'expected_rainfall': 2.0}]
        
        urgency = self.weather_integrator.determine_irrigation_urgency(current_weather, forecast)
        self.assertIn(urgency, ['LOW', 'MEDIUM', 'HIGH'], "Urgency should be valid level")

class TestEnhancedIrrigationAPI(unittest.TestCase):
    """Test enhanced irrigation API"""
    
    def setUp(self):
        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # Mock the API to use temporary database
        self.api = EnhancedIrrigationAPI()
        self.api.db_path = self.temp_db.name
        self.api.init_database()
    
    def tearDown(self):
        # Clean up temporary database
        os.unlink(self.temp_db.name)
    
    def test_soil_properties_retrieval(self):
        """Test soil properties retrieval"""
        soil_types = ['clay', 'loam', 'sandy', 'silt', 'sandy_loam']
        
        for soil_type in soil_types:
            properties = self.api.get_soil_properties(soil_type)
            self.assertIn('field_capacity', properties, f"Field capacity missing for {soil_type}")
            self.assertIn('wilting_point', properties, f"Wilting point missing for {soil_type}")
            self.assertLess(properties['wilting_point'], properties['field_capacity'], 
                           f"Wilting point should be less than field capacity for {soil_type}")
    
    def test_crop_stage_calculation(self):
        """Test crop stage calculation"""
        # Test with planting date
        planting_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        stage = self.api.calculate_crop_stage('wheat', planting_date)
        self.assertIn(stage, [0, 1, 2, 3], "Crop stage should be valid")
        
        # Test without planting date
        stage = self.api.calculate_crop_stage('wheat', None)
        self.assertEqual(stage, 1, "Default stage should be 1")
    
    def test_irrigation_recommendation_generation(self):
        """Test irrigation recommendation generation"""
        # Test with sample input
        input_data = {
            'crop_type': 'wheat',
            'soil_type': 'loam',
            'temperature': 25.0,
            'humidity': 65.0,
            'rainfall': 5.0,
            'wind_speed': 10.0,
            'solar_radiation': 20.0,
            'soil_moisture': 35.0,
            'soil_ph': 6.5,
            'soil_ec': 1.5,
            'crop_days': 30,
            'growth_stage': 1,
            'crop_coefficient': 1.0,
            'et0': 4.5,
            'etc': 4.5,
            'effective_rainfall': 4.0,
            'moisture_stress': 0.3,
            'field_capacity': 35,
            'wilting_point': 15
        }
        
        recommendation = self.api.get_ml_irrigation_recommendation(input_data)
        
        # Check required fields
        required_fields = ['irrigation_needed', 'irrigation_probability', 'irrigation_amount_mm', 
                          'confidence', 'explanations', 'warnings', 'tips']
        
        for field in required_fields:
            self.assertIn(field, recommendation, f"Recommendation should contain {field}")

class TestSystemIntegration(unittest.TestCase):
    """Test complete system integration"""
    
    def test_end_to_end_workflow(self):
        """Test complete irrigation recommendation workflow"""
        try:
            # Test data preprocessing
            preprocessor = IrrigationDataPreprocessor()
            
            # Create sample input
            sample_input = pd.DataFrame({
                'crop_type': ['wheat'],
                'soil_type': ['loam'],
                'climate_zone': ['temperate'],
                'temperature': [25.0],
                'humidity': [65.0],
                'rainfall': [5.0],
                'wind_speed': [10.0],
                'solar_radiation': [20.0],
                'soil_moisture': [35.0],
                'soil_ph': [6.5],
                'soil_ec': [1.5],
                'crop_days': [30],
                'growth_stage': [1],
                'crop_coefficient': [1.0],
                'et0': [4.5],
                'etc': [4.5],
                'effective_rainfall': [4.0],
                'moisture_stress': [0.3],
                'irrigation_amount_mm': [25.0],
                'irrigation_needed': [1],
                'field_capacity': [35],
                'wilting_point': [15]
            })
            
            # Test preprocessing
            processed_data = preprocessor.feature_engineering(sample_input)
            self.assertGreater(len(processed_data.columns), len(sample_input.columns), 
                             "Feature engineering should add new features")
            
            # Test API integration
            api = EnhancedIrrigationAPI()
            recommendation = api.get_comprehensive_irrigation_recommendation(
                'wheat', 'loam', 'Test_Location', field_size=1.0
            )
            
            self.assertIsInstance(recommendation, dict, "Should return recommendation dictionary")
            self.assertIn('irrigation_needed', recommendation, "Should contain irrigation decision")
            
            print("✅ End-to-end workflow test passed")
            
        except Exception as e:
            self.fail(f"End-to-end workflow failed: {e}")

def run_comprehensive_tests():
    """Run all irrigation system tests"""
    print("🧪 Running Comprehensive Irrigation System Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestIrrigationPreprocessing,
        TestIrrigationModel,
        TestSoilMoistureMonitor,
        TestWeatherIntegration,
        TestEnhancedIrrigationAPI,
        TestSystemIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🏁 Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n🎉 All tests passed successfully!")
    else:
        print(f"\n⚠️ {len(result.failures + result.errors)} test(s) failed")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_comprehensive_tests()
    exit(0 if success else 1)
