{% extends 'layout.html' %} {% block body %}

<style>
  .mt-0 {
    margin-top: 50 !important;
  }
  html body {
    /* background-color: rgb(206, 206, 228); */
    background-color: rgb(104 108 115);
  }
</style>

<section
  style="
    height: 90vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
  "
>
  <div class="container mx-auto my-50" style="margin: 5px">
    <div class="row text-center">
      <div class="col-sm-12">
        <h3
          class="text-center"
          style="
            font-weight: bolder;
            text-transform: capitalize;
            color: #cfc547;
            font-size: 49px;

            margin-bottom: 20px;
            text-shadow: 10px 10px 25px rgb(81, 67, 21),
              -10px 10px 25px rgb(81, 67, 21), -10px -10px 25px rgb(81, 67, 21),
              10px -10px 25px rgb(81, 67, 21);
          "
        >
          Random Forest
        </h3>
        <div class="" style="justify-content: center">
          <h3
            class="text-center"
            style="
              font-weight: bolder;
              text-transform: capitalize;
              color: #cfc547;
              font-size: 49px;
              text-shadow: 10px 10px 25px rgb(81, 67, 21),
                -10px 10px 25px rgb(81, 67, 21),
                -10px -10px 25px rgb(81, 67, 21),
                10px -10px 25px rgb(81, 67, 21);
            "
          >
            <b>The price predicted is </b>
          </h3>
          <div class="uderline1"></div>
        </div>
      </div>
    </div>
  </div>

  <section>
    <aside class="profile-card1">
      <header>
        <!-- here’s the avatar -->
        <a>
          <img src="../static/images/farmer2.jpg" />
        </a>

        <!-- the username -->
        <h1><i>{{ p_result }} </i>₹</h1>

        <!-- and role or location -->
      </header>
    </aside>
  </section>
</section>
{% endblock %}
