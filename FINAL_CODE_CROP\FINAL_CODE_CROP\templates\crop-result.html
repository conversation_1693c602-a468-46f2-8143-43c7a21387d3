{% extends 'layout.html' %} {% block body %}

<style>
  .mt-0 {
    margin-top: 50 !important;
  }
  html body {
    /* background-color: rgb(206, 206, 228); */
    background-color: rgb(104 108 115);
  }
</style>
<section
  style="
    height: 90vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
  "
>
  <div class="container mx-auto my-50" style="margin: 5px">
    <div class="row">
      <div class="col-sm py-2 py-md-3">
        <div class="" style="justify-content: center">
          <h1
            class="text-center"
            style="
              font-weight: bolder;
              text-transform: capitalize;
              color: #cfc547;
              font-size: 49px;
              text-shadow: 10px 10px 25px rgb(81, 67, 21),
                -10px 10px 25px rgb(81, 67, 21),
                -10px -10px 25px rgb(81, 67, 21),
                10px -10px 25px rgb(81, 67, 21);
            "
          >
            You should grow in your farm!
          </h1>
          <div class="uderline1"></div>
        </div>
      </div>
    </div>
  </div>

  <section>
    <aside class="profile-card">
      <header>
        <!-- here’s the avatar -->
        <a>
          <img src="../static/images/farm_background.jpg" />
        </a>

        <!-- the username -->
        <h1 style="text-transform: uppercase; color: #cfc547; font-size: 2.5em; margin: 20px 0;">
          🌾 {{ prediction.title() }}
        </h1>
        {% if confidence %}
        <div style="text-align: center; margin-top: 15px;">
          <p style="color: #cfc547; font-size: 20px; margin: 10px 0;">
            <i class="fas fa-chart-line"></i> AI Confidence: {{ confidence }}%
          </p>
          {% if confidence >= 80 %}
            <p style="color: #28a745; font-size: 16px;"><i class="fas fa-check-circle"></i> Highly Recommended</p>
          {% elif confidence >= 60 %}
            <p style="color: #ffc107; font-size: 16px;"><i class="fas fa-info-circle"></i> Good Match</p>
          {% else %}
            <p style="color: #fd7e14; font-size: 16px;"><i class="fas fa-exclamation-triangle"></i> Consider Alternatives</p>
          {% endif %}
        </div>
        {% endif %}

        <!-- Input Summary -->
        <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
          <h3 style="color: #cfc547; text-align: center; margin-bottom: 15px;">
            <i class="fas fa-seedling"></i> Your Farm Conditions
          </h3>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
            <div style="text-align: center;">
              <strong style="color: #cfc547;">Nitrogen:</strong><br>
              <span style="color: white;">{{ nitrogen }} kg/ha</span>
            </div>
            <div style="text-align: center;">
              <strong style="color: #cfc547;">Phosphorous:</strong><br>
              <span style="color: white;">{{ phosphorous }} kg/ha</span>
            </div>
            <div style="text-align: center;">
              <strong style="color: #cfc547;">Potassium:</strong><br>
              <span style="color: white;">{{ potassium }} kg/ha</span>
            </div>
            <div style="text-align: center;">
              <strong style="color: #cfc547;">pH Level:</strong><br>
              <span style="color: white;">{{ ph_level }}</span>
            </div>
            <div style="text-align: center;">
              <strong style="color: #cfc547;">Temperature:</strong><br>
              <span style="color: white;">{{ temperature }}°C</span>
            </div>
            <div style="text-align: center;">
              <strong style="color: #cfc547;">Humidity:</strong><br>
              <span style="color: white;">{{ humidity }}%</span>
            </div>
            <div style="text-align: center;">
              <strong style="color: #cfc547;">Rainfall:</strong><br>
              <span style="color: white;">{{ rainfall }} mm</span>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin-top: 25px;">
          <a href="/crop-recommend" style="display: inline-block; padding: 12px 30px; background: #cfc547; color: #000; text-decoration: none; border-radius: 25px; margin: 0 10px; font-weight: bold;">
            <i class="fas fa-redo"></i> Try Again
          </a>
          <a href="/index.html" style="display: inline-block; padding: 12px 30px; background: #28a745; color: white; text-decoration: none; border-radius: 25px; margin: 0 10px; font-weight: bold;">
            <i class="fas fa-home"></i> Dashboard
          </a>
        </div>
      </header>
    </aside>
  </section>
</section>

{% endblock %}
