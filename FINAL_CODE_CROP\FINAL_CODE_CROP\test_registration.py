#!/usr/bin/env python3
"""
Quick registration test script
"""

import requests
import sqlite3
import random
import string

def test_registration():
    """Test registration functionality"""
    print("📝 Testing Registration Functionality")
    print("=" * 40)
    
    # Generate test user data
    random_suffix = ''.join(random.choices(string.digits, k=4))
    test_user = {
        'name': f'testuser{random_suffix}',
        'email': f'test{random_suffix}@example.com',
        'phone': f'98765{random_suffix}',
        'password': 'testpass123'
    }
    
    print(f"Testing registration with user: {test_user['name']}")
    
    try:
        session = requests.Session()
        
        # Test registration
        response = session.post('http://127.0.0.1:5000/userreg', 
                               data=test_user, 
                               timeout=10,
                               allow_redirects=False)
        
        print(f"Registration response status: {response.status_code}")
        
        if response.status_code == 302:  # Redirect means success
            location = response.headers.get('Location', '')
            print(f"✅ Registration successful - redirected to: {location}")
            
            # Verify user was created in database
            conn = sqlite3.connect('user_data.db')
            cursor = conn.cursor()
            cursor.execute("SELECT name, email FROM user WHERE name = ?", (test_user['name'],))
            result = cursor.fetchone()
            conn.close()
            
            if result:
                print(f"✅ User found in database: {result}")
                return True
            else:
                print("❌ User not found in database")
                return False
                
        elif response.status_code == 200:
            if 'already exists' in response.text.lower():
                print("⚠️ User already exists (expected for duplicate test)")
                return True
            else:
                print("❌ Registration failed")
                print("Response preview:", response.text[:300])
                return False
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during registration test: {e}")
        return False

if __name__ == "__main__":
    success = test_registration()
    print(f"\nResult: {'PASS' if success else 'FAIL'}")
