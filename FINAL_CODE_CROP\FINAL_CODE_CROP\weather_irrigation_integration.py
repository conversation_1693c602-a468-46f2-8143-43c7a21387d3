"""
Weather Data Integration for Irrigation Management
Integrates real-time weather data and forecasts for intelligent irrigation decisions
"""

import requests
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import config
from pathlib import Path

class WeatherIrrigationIntegrator:
    def __init__(self, db_path="irrigation_data.db"):
        self.db_path = db_path
        self.api_key = getattr(config, 'weather_api_key', 'demo_key')
        self.init_database()
    
    def init_database(self):
        """Initialize weather data storage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Weather data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS weather_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location TEXT NOT NULL,
                latitude REAL,
                longitude REAL,
                temperature REAL,
                humidity REAL,
                rainfall REAL,
                wind_speed REAL,
                wind_direction REAL,
                pressure REAL,
                solar_radiation REAL,
                uv_index REAL,
                visibility REAL,
                cloud_cover REAL,
                weather_condition TEXT,
                data_timestamp TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Weather forecast table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS weather_forecast (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location TEXT NOT NULL,
                forecast_date DATE,
                min_temp REAL,
                max_temp REAL,
                avg_humidity REAL,
                rainfall_probability REAL,
                expected_rainfall REAL,
                wind_speed REAL,
                weather_condition TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Irrigation weather recommendations table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS irrigation_weather_recommendations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location TEXT NOT NULL,
                recommendation_date DATE,
                current_weather_score REAL,
                forecast_weather_score REAL,
                irrigation_urgency TEXT,
                weather_based_delay_hours INTEGER,
                rainfall_expected_24h REAL,
                rainfall_expected_48h REAL,
                optimal_irrigation_window TEXT,
                weather_warnings TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Weather irrigation database initialized")
    
    def fetch_current_weather(self, location):
        """Fetch current weather data from API"""
        try:
            # OpenWeatherMap API
            base_url = "http://api.openweathermap.org/data/2.5/weather"
            params = {
                'q': location,
                'appid': self.api_key,
                'units': 'metric'
            }
            
            response = requests.get(base_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                weather_data = {
                    'location': location,
                    'latitude': data['coord']['lat'],
                    'longitude': data['coord']['lon'],
                    'temperature': data['main']['temp'],
                    'humidity': data['main']['humidity'],
                    'pressure': data['main']['pressure'],
                    'wind_speed': data['wind'].get('speed', 0) * 3.6,  # Convert m/s to km/h
                    'wind_direction': data['wind'].get('deg', 0),
                    'cloud_cover': data['clouds']['all'],
                    'visibility': data.get('visibility', 10000) / 1000,  # Convert to km
                    'weather_condition': data['weather'][0]['description'],
                    'rainfall': data.get('rain', {}).get('1h', 0),  # mm in last hour
                    'data_timestamp': datetime.fromtimestamp(data['dt'])
                }
                
                # Calculate solar radiation estimate
                weather_data['solar_radiation'] = self.estimate_solar_radiation(
                    weather_data['cloud_cover'], 
                    weather_data['data_timestamp']
                )
                
                # Store in database
                self.store_weather_data(weather_data)
                
                return weather_data
            else:
                print(f"❌ Weather API error: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error fetching weather data: {e}")
            return self.get_fallback_weather_data(location)
    
    def fetch_weather_forecast(self, location, days=5):
        """Fetch weather forecast data"""
        try:
            base_url = "http://api.openweathermap.org/data/2.5/forecast"
            params = {
                'q': location,
                'appid': self.api_key,
                'units': 'metric'
            }
            
            response = requests.get(base_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                forecasts = []
                
                # Process 5-day forecast (3-hour intervals)
                daily_data = {}
                
                for item in data['list'][:days*8]:  # 8 intervals per day
                    dt = datetime.fromtimestamp(item['dt'])
                    date_key = dt.date()
                    
                    if date_key not in daily_data:
                        daily_data[date_key] = {
                            'temps': [],
                            'humidity': [],
                            'rainfall': 0,
                            'wind_speeds': [],
                            'conditions': []
                        }
                    
                    daily_data[date_key]['temps'].append(item['main']['temp'])
                    daily_data[date_key]['humidity'].append(item['main']['humidity'])
                    daily_data[date_key]['rainfall'] += item.get('rain', {}).get('3h', 0)
                    daily_data[date_key]['wind_speeds'].append(item['wind'].get('speed', 0) * 3.6)
                    daily_data[date_key]['conditions'].append(item['weather'][0]['description'])
                
                # Convert to daily forecasts
                for date_key, day_data in daily_data.items():
                    forecast = {
                        'location': location,
                        'forecast_date': date_key,
                        'min_temp': min(day_data['temps']),
                        'max_temp': max(day_data['temps']),
                        'avg_humidity': np.mean(day_data['humidity']),
                        'expected_rainfall': day_data['rainfall'],
                        'rainfall_probability': min(100, day_data['rainfall'] * 10),  # Rough estimate
                        'wind_speed': np.mean(day_data['wind_speeds']),
                        'weather_condition': max(set(day_data['conditions']), key=day_data['conditions'].count)
                    }
                    forecasts.append(forecast)
                
                # Store forecasts
                self.store_weather_forecast(forecasts)
                
                return forecasts
            else:
                print(f"❌ Forecast API error: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Error fetching forecast: {e}")
            return []
    
    def estimate_solar_radiation(self, cloud_cover, timestamp):
        """Estimate solar radiation based on cloud cover and time"""
        # Maximum solar radiation (clear sky)
        max_radiation = 25  # MJ/m²/day
        
        # Adjust for cloud cover
        cloud_factor = 1 - (cloud_cover / 100) * 0.7
        
        # Adjust for time of day (simplified)
        hour = timestamp.hour
        if 6 <= hour <= 18:
            time_factor = np.sin(np.pi * (hour - 6) / 12)
        else:
            time_factor = 0
        
        return max_radiation * cloud_factor * time_factor
    
    def get_fallback_weather_data(self, location):
        """Fallback weather data when API is unavailable"""
        return {
            'location': location,
            'temperature': np.random.normal(25, 5),
            'humidity': np.random.normal(65, 15),
            'rainfall': np.random.exponential(2),
            'wind_speed': np.random.gamma(2, 2),
            'solar_radiation': np.random.normal(20, 3),
            'pressure': 1013.25,
            'cloud_cover': np.random.uniform(0, 100),
            'weather_condition': 'partly cloudy',
            'data_timestamp': datetime.now()
        }
    
    def store_weather_data(self, weather_data):
        """Store weather data in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO weather_data 
            (location, latitude, longitude, temperature, humidity, rainfall, 
             wind_speed, wind_direction, pressure, solar_radiation, cloud_cover, 
             visibility, weather_condition, data_timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            weather_data['location'], weather_data.get('latitude'), weather_data.get('longitude'),
            weather_data['temperature'], weather_data['humidity'], weather_data['rainfall'],
            weather_data['wind_speed'], weather_data.get('wind_direction'), weather_data.get('pressure'),
            weather_data['solar_radiation'], weather_data.get('cloud_cover'), weather_data.get('visibility'),
            weather_data['weather_condition'], weather_data['data_timestamp']
        ))
        
        conn.commit()
        conn.close()
    
    def store_weather_forecast(self, forecasts):
        """Store weather forecast data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for forecast in forecasts:
            cursor.execute('''
                INSERT OR REPLACE INTO weather_forecast 
                (location, forecast_date, min_temp, max_temp, avg_humidity, 
                 rainfall_probability, expected_rainfall, wind_speed, weather_condition)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                forecast['location'], forecast['forecast_date'], forecast['min_temp'],
                forecast['max_temp'], forecast['avg_humidity'], forecast['rainfall_probability'],
                forecast['expected_rainfall'], forecast['wind_speed'], forecast['weather_condition']
            ))
        
        conn.commit()
        conn.close()
    
    def analyze_irrigation_weather_conditions(self, location):
        """Analyze weather conditions for irrigation recommendations"""
        # Get current weather
        current_weather = self.fetch_current_weather(location)
        if not current_weather:
            return None
        
        # Get forecast
        forecast = self.fetch_weather_forecast(location, days=3)
        
        # Calculate weather scores
        current_score = self.calculate_weather_score(current_weather)
        
        # Analyze forecast for next 24-48 hours
        rainfall_24h = sum(f['expected_rainfall'] for f in forecast[:1])
        rainfall_48h = sum(f['expected_rainfall'] for f in forecast[:2])
        
        # Determine irrigation urgency
        urgency = self.determine_irrigation_urgency(current_weather, forecast)
        
        # Calculate optimal irrigation window
        optimal_window = self.find_optimal_irrigation_window(current_weather, forecast)
        
        # Generate weather warnings
        warnings = self.generate_weather_warnings(current_weather, forecast)
        
        recommendation = {
            'location': location,
            'current_weather_score': current_score,
            'irrigation_urgency': urgency,
            'rainfall_expected_24h': rainfall_24h,
            'rainfall_expected_48h': rainfall_48h,
            'optimal_irrigation_window': optimal_window,
            'weather_warnings': '; '.join(warnings),
            'current_weather': current_weather,
            'forecast': forecast
        }
        
        # Store recommendation
        self.store_irrigation_weather_recommendation(recommendation)
        
        return recommendation
    
    def calculate_weather_score(self, weather_data):
        """Calculate weather favorability score for irrigation (0-100)"""
        score = 50  # Base score
        
        # Temperature factor
        temp = weather_data['temperature']
        if 20 <= temp <= 30:
            score += 20
        elif 15 <= temp <= 35:
            score += 10
        else:
            score -= 10
        
        # Humidity factor
        humidity = weather_data['humidity']
        if 40 <= humidity <= 70:
            score += 15
        elif humidity > 80:
            score -= 10
        
        # Wind factor
        wind = weather_data['wind_speed']
        if wind > 20:
            score -= 15  # High wind reduces irrigation efficiency
        elif wind > 10:
            score -= 5
        
        # Rainfall factor
        if weather_data['rainfall'] > 5:
            score -= 30  # Recent rain reduces irrigation need
        
        return max(0, min(100, score))
    
    def determine_irrigation_urgency(self, current_weather, forecast):
        """Determine irrigation urgency based on weather"""
        # Check for upcoming rain
        upcoming_rain = sum(f['expected_rainfall'] for f in forecast[:2])
        
        if upcoming_rain > 10:
            return "LOW"  # Rain expected
        elif current_weather['temperature'] > 35 or current_weather['humidity'] < 30:
            return "HIGH"  # Hot and dry conditions
        elif current_weather['wind_speed'] > 20:
            return "MEDIUM"  # Windy conditions
        else:
            return "MEDIUM"
    
    def find_optimal_irrigation_window(self, current_weather, forecast):
        """Find optimal time window for irrigation"""
        # Avoid irrigation during high wind or extreme heat
        current_hour = datetime.now().hour
        
        if current_weather['wind_speed'] > 15:
            return "Wait for calmer conditions (wind > 15 km/h)"
        elif current_weather['temperature'] > 35:
            return "Early morning (5-8 AM) or evening (6-9 PM)"
        elif 10 <= current_hour <= 16:
            return "Evening (6-9 PM) recommended"
        else:
            return "Current conditions suitable"
    
    def generate_weather_warnings(self, current_weather, forecast):
        """Generate weather-based warnings"""
        warnings = []
        
        if current_weather['wind_speed'] > 20:
            warnings.append("High wind conditions - irrigation efficiency reduced")
        
        if current_weather['temperature'] > 40:
            warnings.append("Extreme heat - avoid midday irrigation")
        
        upcoming_rain = sum(f['expected_rainfall'] for f in forecast[:1])
        if upcoming_rain > 15:
            warnings.append("Heavy rain expected - delay irrigation")
        
        if current_weather.get('cloud_cover', 0) > 90:
            warnings.append("Overcast conditions - reduced evaporation")
        
        return warnings
    
    def store_irrigation_weather_recommendation(self, recommendation):
        """Store weather-based irrigation recommendation"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO irrigation_weather_recommendations 
            (location, recommendation_date, current_weather_score, irrigation_urgency,
             rainfall_expected_24h, rainfall_expected_48h, optimal_irrigation_window, weather_warnings)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            recommendation['location'], datetime.now().date(),
            recommendation['current_weather_score'], recommendation['irrigation_urgency'],
            recommendation['rainfall_expected_24h'], recommendation['rainfall_expected_48h'],
            recommendation['optimal_irrigation_window'], recommendation['weather_warnings']
        ))
        
        conn.commit()
        conn.close()

# Initialize the weather integrator
weather_integrator = WeatherIrrigationIntegrator()

def main():
    """Test weather irrigation integration"""
    print("🌤️ Weather Irrigation Integration Test")
    print("=" * 50)
    
    # Test with a sample location
    location = "Delhi"
    
    print(f"📍 Analyzing weather conditions for {location}...")
    recommendation = weather_integrator.analyze_irrigation_weather_conditions(location)
    
    if recommendation:
        print(f"\n📊 Weather Analysis Results:")
        print(f"Weather Score: {recommendation['current_weather_score']:.1f}/100")
        print(f"Irrigation Urgency: {recommendation['irrigation_urgency']}")
        print(f"Expected Rainfall (24h): {recommendation['rainfall_expected_24h']:.1f} mm")
        print(f"Expected Rainfall (48h): {recommendation['rainfall_expected_48h']:.1f} mm")
        print(f"Optimal Window: {recommendation['optimal_irrigation_window']}")
        
        if recommendation['weather_warnings']:
            print(f"⚠️ Warnings: {recommendation['weather_warnings']}")
        
        print(f"\n🌡️ Current Weather:")
        weather = recommendation['current_weather']
        print(f"Temperature: {weather['temperature']:.1f}°C")
        print(f"Humidity: {weather['humidity']:.1f}%")
        print(f"Wind Speed: {weather['wind_speed']:.1f} km/h")
        print(f"Condition: {weather['weather_condition']}")
    
    print("\n✅ Weather irrigation integration test completed!")

if __name__ == "__main__":
    main()
