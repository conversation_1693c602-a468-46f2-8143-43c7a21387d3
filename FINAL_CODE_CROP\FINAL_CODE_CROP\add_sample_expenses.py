"""
Add sample expense data to test the expense management system
"""

import sqlite3
from datetime import datetime, timedelta
import random

def add_sample_expenses():
    """Add sample expense data for testing"""
    
    # Sample expense data
    sample_expenses = [
        # Seeds
        ('2024-01-15', 'Seeds', 2500.00, 'Wheat seeds for 5 acres'),
        ('2024-01-20', 'Seeds', 1800.00, 'Corn seeds for 3 acres'),
        ('2024-02-05', 'Seeds', 3200.00, 'Rice seeds for 8 acres'),
        
        # Fertilizers
        ('2024-01-25', 'Fertilizers', 4500.00, 'NPK fertilizer - 10 bags'),
        ('2024-02-10', 'Fertilizers', 2800.00, 'Urea fertilizer - 8 bags'),
        ('2024-02-20', 'Fertilizers', 3600.00, 'Organic compost - 15 bags'),
        
        # Labor
        ('2024-01-30', 'Labor', 8000.00, 'Field preparation - 4 workers for 5 days'),
        ('2024-02-15', 'Labor', 6000.00, 'Planting - 3 workers for 4 days'),
        ('2024-03-01', 'Labor', 4500.00, 'Weeding - 2 workers for 3 days'),
        
        # Pesticides
        ('2024-02-25', 'Pesticides', 1200.00, 'Insecticide spray - 3 bottles'),
        ('2024-03-05', 'Pesticides', 800.00, 'Fungicide - 2 bottles'),
        
        # Irrigation
        ('2024-01-10', 'Irrigation', 15000.00, 'Drip irrigation system installation'),
        ('2024-02-01', 'Irrigation', 500.00, 'Water pump maintenance'),
        ('2024-03-10', 'Irrigation', 300.00, 'Irrigation pipes repair'),
        
        # Equipment
        ('2024-01-05', 'Equipment', 25000.00, 'New tractor attachment'),
        ('2024-02-12', 'Equipment', 3500.00, 'Hand tools and equipment'),
        
        # Transport
        ('2024-01-22', 'Transport', 1500.00, 'Transportation of seeds and fertilizers'),
        ('2024-02-28', 'Transport', 2200.00, 'Market transportation'),
        
        # Fuel
        ('2024-01-18', 'Fuel', 3000.00, 'Diesel for tractor - 200 liters'),
        ('2024-02-08', 'Fuel', 2800.00, 'Diesel for tractor - 180 liters'),
        ('2024-03-02', 'Fuel', 3200.00, 'Diesel for tractor - 220 liters'),
        
        # Maintenance
        ('2024-01-12', 'Maintenance', 2500.00, 'Tractor servicing'),
        ('2024-02-18', 'Maintenance', 800.00, 'Equipment maintenance'),
        
        # Others
        ('2024-01-08', 'Others', 1200.00, 'Farm insurance premium'),
        ('2024-02-03', 'Others', 500.00, 'Soil testing'),
        ('2024-03-08', 'Others', 300.00, 'Farm supplies'),
    ]
    
    # Connect to database
    conn = sqlite3.connect('agriculture.db')
    cursor = conn.cursor()
    
    print("🌾 Adding sample expense data...")
    
    # Add sample expenses
    for expense in sample_expenses:
        cursor.execute('''
            INSERT INTO expenses (date, category, amount, description, user_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (*expense, 1))  # user_id = 1
    
    # Add some recent expenses for current month
    current_date = datetime.now()
    recent_expenses = [
        (current_date - timedelta(days=5), 'Labor', 2000.00, 'Harvesting assistance'),
        (current_date - timedelta(days=3), 'Transport', 1500.00, 'Crop transportation to market'),
        (current_date - timedelta(days=2), 'Fuel', 1200.00, 'Diesel for harvesting'),
        (current_date - timedelta(days=1), 'Packaging', 800.00, 'Packaging materials'),
    ]
    
    for expense in recent_expenses:
        cursor.execute('''
            INSERT INTO expenses (date, category, amount, description, user_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (expense[0].strftime('%Y-%m-%d'), expense[1], expense[2], expense[3], 1))
    
    conn.commit()
    
    # Get summary
    cursor.execute('SELECT COUNT(*), SUM(amount) FROM expenses')
    count, total = cursor.fetchone()
    
    conn.close()
    
    print(f"✅ Added {len(sample_expenses) + len(recent_expenses)} sample expenses")
    print(f"📊 Total expenses in database: {count} records")
    print(f"💰 Total amount: ₹{total:.2f}")
    print("\n🎉 Sample data added successfully!")
    print("You can now test the expense management system with realistic data.")

if __name__ == "__main__":
    add_sample_expenses()
