{% extends "layout.html" %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-info text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Farm Activity Calendar
                            </h3>
                            <p class="mb-0 opacity-8">Plan and track your farming activities</p>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addActivityModal">
                                <i class="fas fa-plus me-2"></i>Add Activity
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Activity Summary -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ activities|length }}</h4>
                                    <p class="mb-0">Total Activities</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>{{ activities|selectattr('4', 'equalto', 'completed')|list|length }}</h4>
                                    <p class="mb-0">Completed</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>{{ activities|selectattr('4', 'equalto', 'pending')|list|length }}</h4>
                                    <p class="mb-0">Pending</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>{{ activities|selectattr('4', 'equalto', 'scheduled')|list|length }}</h4>
                                    <p class="mb-0">Scheduled</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activities List -->
                    <h5 class="mb-3">Recent Activities</h5>
                    {% if activities %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Activity Type</th>
                                    <th>Crop</th>
                                    <th>Description</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in activities %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ activity[0] }}</span>
                                    </td>
                                    <td>{{ activity[1] }}</td>
                                    <td>{{ activity[2] }}</td>
                                    <td>{{ activity[3] }}</td>
                                    <td>
                                        {% if activity[4] == 'completed' %}
                                        <span class="badge bg-success">Completed</span>
                                        {% elif activity[4] == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                        {% else %}
                                        <span class="badge bg-info">Scheduled</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No activities scheduled yet</h5>
                        <p class="text-muted">Start planning your farm activities by adding your first entry</p>
                    </div>
                    {% endif %}

                    <!-- Quick Activity Templates -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="mb-3">Quick Activity Templates</h5>
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-outline-primary w-100">
                                        <i class="fas fa-seedling me-2"></i>Planting
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-outline-success w-100">
                                        <i class="fas fa-tint me-2"></i>Irrigation
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-outline-warning w-100">
                                        <i class="fas fa-spray-can me-2"></i>Fertilizing
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-outline-danger w-100">
                                        <i class="fas fa-cut me-2"></i>Harvesting
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Activity Modal -->
<div class="modal fade" id="addActivityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">Add New Activity</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_activity') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Activity Type</label>
                        <select class="form-select" name="activity_type" required>
                            <option value="">Select Activity</option>
                            <option value="Planting">Planting</option>
                            <option value="Irrigation">Irrigation</option>
                            <option value="Fertilizing">Fertilizing</option>
                            <option value="Pest Control">Pest Control</option>
                            <option value="Weeding">Weeding</option>
                            <option value="Harvesting">Harvesting</option>
                            <option value="Soil Preparation">Soil Preparation</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Crop</label>
                        <input type="text" class="form-control" name="crop" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Activity Date</label>
                        <input type="date" class="form-control" name="activity_date" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Add Activity</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 15px;
}
.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}
.table-hover tbody tr:hover {
    background-color: rgba(23, 162, 184, 0.1);
}
</style>
{% endblock %}
