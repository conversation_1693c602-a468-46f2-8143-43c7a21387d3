<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Irrigation Prediction Results - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .result-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .prediction-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .irrigation-needed {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
            color: white;
        }
        .irrigation-not-needed {
            background: linear-gradient(45deg, #28a745, #2ecc71);
            color: white;
        }
        .confidence-high { background-color: #28a745; }
        .confidence-medium { background-color: #ffc107; color: #000; }
        .confidence-low { background-color: #dc3545; }
        .metric-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #495057;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">
                <i class="fas fa-seedling me-2"></i>AgroPro
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('irrigation_manual') }}">
                    <i class="fas fa-edit me-1"></i>New Prediction
                </a>
                <a class="nav-link" href="{{ url_for('irrigation_table') }}">
                    <i class="fas fa-table me-1"></i>Prediction Table
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="display-5 fw-bold text-white">
                <i class="fas fa-chart-line me-3"></i>Irrigation Prediction Results
            </h1>
            <p class="lead text-white-50">AI-powered recommendation based on your field conditions</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="result-container">
                    
                    <!-- Main Prediction Result -->
                    <div class="prediction-card card {% if recommendation.irrigation_needed %}irrigation-needed{% else %}irrigation-not-needed{% endif %}">
                        <div class="card-body text-center">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    {% if recommendation.irrigation_needed %}
                                        <i class="fas fa-exclamation-triangle fa-4x"></i>
                                    {% else %}
                                        <i class="fas fa-check-circle fa-4x"></i>
                                    {% endif %}
                                </div>
                                <div class="col-md-8">
                                    <h2 class="mb-2">
                                        {% if recommendation.irrigation_needed %}
                                            Irrigation Required
                                        {% else %}
                                            No Irrigation Needed
                                        {% endif %}
                                    </h2>
                                    <p class="mb-0 fs-5">{{ recommendation.schedule }}</p>
                                </div>
                                <div class="col-md-2">
                                    <span class="badge confidence-{{ recommendation.confidence }} fs-6 px-3 py-2">
                                        {{ recommendation.confidence.title() }} Confidence
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Key Metrics -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value text-primary">
                                    {{ "%.1f"|format(recommendation.irrigation_amount_mm) }}
                                </div>
                                <div class="metric-label">mm Water Needed</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value text-success">
                                    {{ "%.0f"|format(recommendation.irrigation_probability * 100) }}%
                                </div>
                                <div class="metric-label">Probability</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value text-info">
                                    {{ recommendation.duration_minutes }}
                                </div>
                                <div class="metric-label">Minutes</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value text-warning">
                                    {{ "%.1f"|format(recommendation.daily_water_mm) }}
                                </div>
                                <div class="metric-label">mm/day</div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Information -->
                    <div class="row mt-4">
                        <!-- Your Input Summary -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Your Field Conditions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6"><strong>Crop:</strong></div>
                                        <div class="col-6">{{ crop_type.title() }}</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6"><strong>Soil:</strong></div>
                                        <div class="col-6">{{ soil_type.title() }}</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6"><strong>Location:</strong></div>
                                        <div class="col-6">{{ location }}</div>
                                    </div>
                                    {% if recommendation.user_inputs %}
                                    <hr>
                                    <div class="row">
                                        <div class="col-6"><strong>Temperature:</strong></div>
                                        <div class="col-6">{{ recommendation.user_inputs.temperature }}°C</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6"><strong>Humidity:</strong></div>
                                        <div class="col-6">{{ recommendation.user_inputs.humidity }}%</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6"><strong>Rainfall:</strong></div>
                                        <div class="col-6">{{ recommendation.user_inputs.rainfall }}mm</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6"><strong>Soil Moisture:</strong></div>
                                        <div class="col-6">{{ recommendation.user_inputs.soil_moisture }}%</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6"><strong>Field Size:</strong></div>
                                        <div class="col-6">{{ recommendation.user_inputs.field_size }} hectares</div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Recommendations -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Recommendations</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong><i class="fas fa-clock me-1"></i>Best Time:</strong><br>
                                        <span class="text-muted">{{ recommendation.best_time }}</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong><i class="fas fa-tools me-1"></i>Method:</strong><br>
                                        <span class="text-muted">{{ recommendation.method }}</span>
                                    </div>
                                    {% if recommendation.irrigation_needed %}
                                    <div class="mb-3">
                                        <strong><i class="fas fa-droplet me-1"></i>Amount per Session:</strong><br>
                                        <span class="text-muted">{{ "%.1f"|format(recommendation.amount_per_session) }} mm</span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Explanations -->
                    {% if recommendation.explanations %}
                    <div class="card mt-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-brain me-2"></i>AI Analysis Factors</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                {% for explanation in recommendation.explanations %}
                                <li class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>{{ explanation }}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Tips and Warnings -->
                    <div class="row mt-4">
                        {% if recommendation.warnings %}
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Important Warnings</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0">
                                        {% for warning in recommendation.warnings %}
                                        <li class="mb-1">
                                            <i class="fas fa-arrow-right text-warning me-2"></i>{{ warning }}
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if recommendation.tips %}
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Helpful Tips</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0">
                                        {% for tip in recommendation.tips %}
                                        <li class="mb-1">
                                            <i class="fas fa-arrow-right text-info me-2"></i>{{ tip }}
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Action Buttons -->
                    <div class="text-center mt-4">
                        <a href="{{ url_for('irrigation_manual') }}" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-edit me-2"></i>New Prediction
                        </a>
                        <a href="{{ url_for('irrigation_table') }}" class="btn btn-success btn-lg">
                            <i class="fas fa-table me-2"></i>View All Predictions
                        </a>
                    </div>

                    <!-- ML Badge -->
                    <div class="text-center mt-3">
                        {% if recommendation.ml_powered %}
                        <span class="badge bg-primary fs-6">
                            <i class="fas fa-robot me-1"></i>Powered by Machine Learning
                        </span>
                        {% else %}
                        <span class="badge bg-secondary fs-6">
                            <i class="fas fa-calculator me-1"></i>Basic Calculation
                        </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
