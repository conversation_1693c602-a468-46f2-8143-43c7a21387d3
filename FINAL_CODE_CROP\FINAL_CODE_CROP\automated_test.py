#!/usr/bin/env python3
"""
AgroPro Platform - Automated Feature Testing System
Tests all 18+ features automatically and generates a comprehensive report
"""

import requests
import json
import time
import os
from datetime import datetime
import sys

class AgroProTester:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
        
    def log_test(self, feature_name, status, details="", response_time=0):
        """Log test results"""
        result = {
            'feature': feature_name,
            'status': status,
            'details': details,
            'response_time': response_time,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        
        if status == "PASS":
            self.passed_tests += 1
            print(f"✅ {feature_name}: PASSED ({response_time:.2f}s)")
        else:
            self.failed_tests += 1
            print(f"❌ {feature_name}: FAILED - {details}")
    
    def test_endpoint(self, endpoint, feature_name, method="GET", data=None, files=None):
        """Test a specific endpoint"""
        try:
            start_time = time.time()
            url = f"{self.base_url}{endpoint}"
            
            if method == "GET":
                response = self.session.get(url, timeout=10)
            elif method == "POST":
                response = self.session.post(url, data=data, files=files, timeout=10)
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.log_test(feature_name, "PASS", f"Status: {response.status_code}", response_time)
                return True
            else:
                self.log_test(feature_name, "FAIL", f"Status: {response.status_code}", response_time)
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_test(feature_name, "FAIL", f"Connection error: {str(e)}")
            return False
        except Exception as e:
            self.log_test(feature_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_core_pages(self):
        """Test core application pages"""
        print("\n🏠 TESTING CORE PAGES...")
        
        core_pages = [
            ("/", "Home Page"),
            ("/index.html", "Dashboard"),
            ("/signup", "Signup Page"),
        ]
        
        for endpoint, name in core_pages:
            self.test_endpoint(endpoint, name)
    
    def test_crop_management(self):
        """Test crop management features"""
        print("\n🌱 TESTING CROP MANAGEMENT FEATURES...")
        
        # Test crop recommendation page
        self.test_endpoint("/crop-recommend", "Crop Recommendation Page")
        
        # Test crop recommendation with sample data
        crop_data = {
            'nitrogen': '90',
            'phosphorous': '42',
            'pottasium': '43',
            'ph': '6.5',
            'rainfall': '202.9',
            'temp': '26.5',
            'hum': '80.3'
        }
        self.test_endpoint("/crop_predict", "Crop Prediction API", "POST", crop_data)

        # Test disease detection page
        self.test_endpoint("/disease_prediction", "Disease Detection Page")

        # Test fertilizer recommendation page
        self.test_endpoint("/crop_fer", "Fertilizer Recommendation Page")

        # Test fertilizer prediction with sample data
        fertilizer_data = {
            'temp': '30',
            'humid': '60',
            'mois': '40',
            'soil': '2',
            'crop': '3',
            'nitro': '20',
            'pota': '15',
            'phos': '25'
        }
        self.test_endpoint("/fer_predict", "Fertilizer Prediction API", "POST", fertilizer_data)
    
    def test_farm_tools(self):
        """Test farm management tools"""
        print("\n🚜 TESTING FARM MANAGEMENT TOOLS...")
        
        farm_tools = [
            ("/irrigation", "Irrigation Management"),
            ("/expenses", "Farm Expenses"),
            ("/farm_calendar", "Farm Calendar"),
            ("/market_linkage", "Market Linkage"),
            ("/pest_management", "Pest Management"),
        ]
        
        for endpoint, name in farm_tools:
            self.test_endpoint(endpoint, name)
    
    def test_analytics_features(self):
        """Test analytics and prediction features"""
        print("\n📊 TESTING ANALYTICS & PREDICTIONS...")
        
        analytics_features = [
            ("/analytics", "Yield Analytics"),
            ("/yield", "Yield Prediction Page"),
            ("/crop_price", "Price Prediction Page"),
            ("/satellite_health", "Satellite Health Viewer"),
            ("/satellite_data", "Precision Agriculture"),
            ("/market_prices", "Live Market Prices"),
        ]
        
        for endpoint, name in analytics_features:
            self.test_endpoint(endpoint, name)
    
    def test_resources_community(self):
        """Test resources and community features"""
        print("\n📚 TESTING RESOURCES & COMMUNITY...")
        
        resource_features = [
            ("/sustainable_tips", "Sustainable Farming Tips"),
            ("/government_schemes", "Government Schemes"),
            ("/community_forum", "Community Forum"),
            ("/notifications", "Notifications System"),
        ]
        
        for endpoint, name in resource_features:
            self.test_endpoint(endpoint, name)
    
    def test_weather_system(self):
        """Test weather forecasting system"""
        print("\n🌤️ TESTING WEATHER SYSTEM...")
        
        self.test_endpoint("/weather", "Weather Forecast Page")
        
        # Test weather API with sample city
        weather_data = {'city': 'Delhi'}
        self.test_endpoint("/weather", "Weather API", "POST", weather_data)
    
    def test_api_endpoints(self):
        """Test API endpoints"""
        print("\n🔌 TESTING API ENDPOINTS...")
        
        # Test satellite data API
        satellite_data = {
            'latitude': '28.6139',
            'longitude': '77.2090',
            'start_date': '2024-01-01',
            'end_date': '2024-01-31',
            'index_type': 'NDVI'
        }
        self.test_endpoint("/get_satellite_data", "Satellite Data API", "POST", satellite_data)
        
        # Test field analysis API
        field_data = {
            'coordinates': '[[28.6139, 77.2090], [28.6140, 77.2091]]',
            'name': 'Test Field'
        }
        self.test_endpoint("/api/field_analysis", "Field Analysis API", "POST", field_data)
        
        # Test alert creation API
        alert_data = {
            'lat': '28.6139',
            'lon': '77.2090',
            'index': 'NDVI',
            'threshold': '0.5',
            'type': 'below'
        }
        self.test_endpoint("/api/set_alert", "Alert Creation API", "POST", alert_data)
    
    def test_database_operations(self):
        """Test database-related operations"""
        print("\n🗄️ TESTING DATABASE OPERATIONS...")
        
        # Test community forum post
        forum_data = {
            'title': 'Test Post',
            'content': 'This is a test post for automated testing',
            'category': 'General'
        }
        self.test_endpoint("/community_forum", "Forum Post Creation", "POST", forum_data)
        
        # Test irrigation schedule
        irrigation_data = {
            'crop_type': 'Wheat',
            'soil_type': 'Loamy',
            'location': 'Test Farm',
            'schedule_date': '2024-02-01',
            'water_amount': '100'
        }
        self.test_endpoint("/irrigation", "Irrigation Schedule", "POST", irrigation_data)
    
    def run_all_tests(self):
        """Run all automated tests"""
        print("🚀 STARTING AUTOMATED TESTING OF AGROPRO PLATFORM")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test all feature categories
        self.test_core_pages()
        self.test_crop_management()
        self.test_farm_tools()
        self.test_analytics_features()
        self.test_resources_community()
        self.test_weather_system()
        self.test_api_endpoints()
        self.test_database_operations()
        
        total_time = time.time() - start_time
        
        # Generate comprehensive report
        self.generate_report(total_time)
    
    def generate_report(self, total_time):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📋 AUTOMATED TESTING REPORT")
        print("=" * 60)
        
        total_tests = self.passed_tests + self.failed_tests
        success_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"🕒 Total Testing Time: {total_time:.2f} seconds")
        print(f"📊 Total Tests Run: {total_tests}")
        print(f"✅ Tests Passed: {self.passed_tests}")
        print(f"❌ Tests Failed: {self.failed_tests}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 EXCELLENT! Platform is working great!")
        elif success_rate >= 75:
            print("👍 GOOD! Most features are working well")
        elif success_rate >= 50:
            print("⚠️ MODERATE! Some issues need attention")
        else:
            print("🚨 CRITICAL! Multiple issues detected")
        
        # Save detailed report to file
        self.save_detailed_report()
    
    def save_detailed_report(self):
        """Save detailed test report to file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"agropro_test_report_{timestamp}.json"
        
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': self.passed_tests + self.failed_tests,
                'passed': self.passed_tests,
                'failed': self.failed_tests,
                'success_rate': (self.passed_tests / (self.passed_tests + self.failed_tests) * 100) if (self.passed_tests + self.failed_tests) > 0 else 0
            },
            'detailed_results': self.test_results
        }
        
        try:
            with open(filename, 'w') as f:
                json.dump(report_data, f, indent=2)
            print(f"\n📄 Detailed report saved to: {filename}")
        except Exception as e:
            print(f"\n⚠️ Could not save report: {e}")

if __name__ == "__main__":
    print("🌾 AgroPro Platform - Automated Testing System")
    print("Testing all 18+ features automatically...\n")
    
    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:5000", timeout=5)
        print("✅ Server is running and accessible")
    except:
        print("❌ Server is not accessible at http://127.0.0.1:5000")
        print("Please make sure your Flask application is running first!")
        sys.exit(1)
    
    # Run automated tests
    tester = AgroProTester()
    tester.run_all_tests()
