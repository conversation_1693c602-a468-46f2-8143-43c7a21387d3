"""
Irrigation Data Preprocessing Pipeline
Handles feature engineering, normalization, and data preparation for ML models
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.impute import SimpleImputer
import joblib
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class IrrigationDataPreprocessor:
    def __init__(self):
        self.scalers = {}
        self.encoders = {}
        self.feature_columns = []
        self.target_columns = []
        self.is_fitted = False
        
    def load_data(self, file_path="datasets/comprehensive_irrigation_dataset.csv"):
        """Load irrigation dataset"""
        try:
            df = pd.read_csv(file_path)
            print(f"✅ Loaded dataset with shape: {df.shape}")
            return df
        except Exception as e:
            print(f"❌ Error loading dataset: {e}")
            return None
    
    def explore_data(self, df):
        """Explore and analyze the dataset"""
        print("\n📊 Dataset Exploration:")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        print("\n📈 Data Types:")
        print(df.dtypes)
        
        print("\n📉 Missing Values:")
        missing = df.isnull().sum()
        if missing.sum() > 0:
            print(missing[missing > 0])
        else:
            print("No missing values found!")
        
        print("\n📊 Statistical Summary:")
        print(df.describe())
        
        print("\n🎯 Target Variable Distribution:")
        print(df['irrigation_needed'].value_counts())
        print(f"Irrigation needed percentage: {df['irrigation_needed'].mean():.2%}")
        
        return df
    
    def feature_engineering(self, df):
        """Create additional features for better predictions"""
        df_processed = df.copy()
        
        print("🔧 Feature Engineering...")
        
        # 1. Water stress indicator
        df_processed['water_stress'] = (df_processed['wilting_point'] + 
                                      0.5 * (df_processed['field_capacity'] - df_processed['wilting_point']) - 
                                      df_processed['soil_moisture']) / df_processed['field_capacity']
        df_processed['water_stress'] = np.clip(df_processed['water_stress'], 0, 1)
        
        # 2. Vapor Pressure Deficit (VPD) - important for plant water stress
        def calculate_vpd(temp, humidity):
            # Saturated vapor pressure (kPa)
            svp = 0.6108 * np.exp(17.27 * temp / (temp + 237.3))
            # Actual vapor pressure
            avp = svp * humidity / 100
            # VPD
            return svp - avp
        
        df_processed['vpd'] = calculate_vpd(df_processed['temperature'], df_processed['humidity'])
        
        # 3. Growing Degree Days (GDD)
        base_temp = 10  # Base temperature for most crops
        df_processed['gdd'] = np.maximum(0, df_processed['temperature'] - base_temp)
        
        # 4. Soil water availability
        df_processed['available_water'] = df_processed['soil_moisture'] - df_processed['wilting_point']
        df_processed['water_availability_ratio'] = (df_processed['available_water'] / 
                                                   (df_processed['field_capacity'] - df_processed['wilting_point']))
        df_processed['water_availability_ratio'] = np.clip(df_processed['water_availability_ratio'], 0, 1)
        
        # 5. Crop stage indicators
        df_processed['early_stage'] = (df_processed['growth_stage'] == 0).astype(int)
        df_processed['mid_stage'] = (df_processed['growth_stage'] == 1).astype(int)
        df_processed['late_stage'] = (df_processed['growth_stage'] >= 2).astype(int)
        
        # 6. Climate stress indicators
        df_processed['heat_stress'] = (df_processed['temperature'] > 35).astype(int)
        df_processed['cold_stress'] = (df_processed['temperature'] < 10).astype(int)
        df_processed['drought_stress'] = (df_processed['rainfall'] < 2).astype(int)
        
        # 7. Interaction features
        df_processed['temp_humidity_interaction'] = df_processed['temperature'] * df_processed['humidity'] / 100
        df_processed['rainfall_soil_interaction'] = df_processed['rainfall'] * df_processed['soil_moisture'] / 100
        
        # 8. Seasonal indicators (based on crop days)
        df_processed['season_sin'] = np.sin(2 * np.pi * df_processed['crop_days'] / 365)
        df_processed['season_cos'] = np.cos(2 * np.pi * df_processed['crop_days'] / 365)
        
        print(f"✅ Added {len(df_processed.columns) - len(df.columns)} new features")
        return df_processed
    
    def encode_categorical_features(self, df, fit=True):
        """Encode categorical features"""
        df_encoded = df.copy()
        categorical_columns = ['crop_type', 'soil_type', 'climate_zone']
        
        for col in categorical_columns:
            if col in df_encoded.columns:
                if fit:
                    self.encoders[col] = LabelEncoder()
                    df_encoded[col] = self.encoders[col].fit_transform(df_encoded[col])
                else:
                    if col in self.encoders:
                        # Handle unseen categories
                        unique_values = set(df_encoded[col].unique())
                        known_values = set(self.encoders[col].classes_)
                        unseen_values = unique_values - known_values
                        
                        if unseen_values:
                            print(f"⚠️ Unseen values in {col}: {unseen_values}")
                            # Replace unseen values with the most frequent class
                            most_frequent = self.encoders[col].classes_[0]
                            df_encoded[col] = df_encoded[col].replace(list(unseen_values), most_frequent)
                        
                        df_encoded[col] = self.encoders[col].transform(df_encoded[col])
        
        return df_encoded
    
    def scale_features(self, X, fit=True):
        """Scale numerical features"""
        X_scaled = X.copy()
        
        # Features that should be normalized (0-1)
        minmax_features = ['humidity', 'soil_moisture', 'water_availability_ratio', 'moisture_stress']
        
        # Features that should be standardized (mean=0, std=1)
        standard_features = [col for col in X.columns if col not in minmax_features]
        
        if fit:
            # MinMax scaling for percentage-based features
            if minmax_features:
                self.scalers['minmax'] = MinMaxScaler()
                X_scaled[minmax_features] = self.scalers['minmax'].fit_transform(X_scaled[minmax_features])
            
            # Standard scaling for other features
            if standard_features:
                self.scalers['standard'] = StandardScaler()
                X_scaled[standard_features] = self.scalers['standard'].fit_transform(X_scaled[standard_features])
        else:
            # Transform using fitted scalers
            if 'minmax' in self.scalers and minmax_features:
                X_scaled[minmax_features] = self.scalers['minmax'].transform(X_scaled[minmax_features])
            
            if 'standard' in self.scalers and standard_features:
                X_scaled[standard_features] = self.scalers['standard'].transform(X_scaled[standard_features])
        
        return X_scaled
    
    def prepare_features_targets(self, df):
        """Prepare features and targets for modeling"""
        # Define target columns
        target_columns = ['irrigation_needed', 'irrigation_amount_mm']
        
        # Define feature columns (exclude targets and intermediate columns)
        exclude_columns = target_columns + ['field_capacity', 'wilting_point']
        feature_columns = [col for col in df.columns if col not in exclude_columns]
        
        X = df[feature_columns]
        y_classification = df['irrigation_needed']
        y_regression = df['irrigation_amount_mm']
        
        self.feature_columns = feature_columns
        self.target_columns = target_columns
        
        return X, y_classification, y_regression
    
    def fit_transform(self, df):
        """Fit preprocessor and transform data"""
        print("🔄 Fitting and transforming data...")
        
        # Feature engineering
        df_engineered = self.feature_engineering(df)
        
        # Encode categorical features
        df_encoded = self.encode_categorical_features(df_engineered, fit=True)
        
        # Prepare features and targets
        X, y_class, y_reg = self.prepare_features_targets(df_encoded)
        
        # Scale features
        X_scaled = self.scale_features(X, fit=True)
        
        self.is_fitted = True
        print("✅ Preprocessing pipeline fitted successfully!")
        
        return X_scaled, y_class, y_reg
    
    def transform(self, df):
        """Transform new data using fitted preprocessor"""
        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before transforming data")
        
        print("🔄 Transforming new data...")
        
        # Feature engineering
        df_engineered = self.feature_engineering(df)
        
        # Encode categorical features
        df_encoded = self.encode_categorical_features(df_engineered, fit=False)
        
        # Prepare features
        X = df_encoded[self.feature_columns]
        
        # Scale features
        X_scaled = self.scale_features(X, fit=False)
        
        return X_scaled
    
    def save_preprocessor(self, filepath="models/irrigation_preprocessor.pkl"):
        """Save the fitted preprocessor"""
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        preprocessor_data = {
            'scalers': self.scalers,
            'encoders': self.encoders,
            'feature_columns': self.feature_columns,
            'target_columns': self.target_columns,
            'is_fitted': self.is_fitted
        }
        
        joblib.dump(preprocessor_data, filepath)
        print(f"✅ Preprocessor saved to {filepath}")
    
    def load_preprocessor(self, filepath="models/irrigation_preprocessor.pkl"):
        """Load a fitted preprocessor"""
        try:
            preprocessor_data = joblib.load(filepath)
            
            self.scalers = preprocessor_data['scalers']
            self.encoders = preprocessor_data['encoders']
            self.feature_columns = preprocessor_data['feature_columns']
            self.target_columns = preprocessor_data['target_columns']
            self.is_fitted = preprocessor_data['is_fitted']
            
            print(f"✅ Preprocessor loaded from {filepath}")
            return True
        except Exception as e:
            print(f"❌ Error loading preprocessor: {e}")
            return False

def main():
    """Main function to test preprocessing pipeline"""
    print("🌾 Irrigation Data Preprocessing Pipeline")
    print("=" * 50)
    
    # Initialize preprocessor
    preprocessor = IrrigationDataPreprocessor()
    
    # Load data
    df = preprocessor.load_data()
    if df is None:
        return
    
    # Explore data
    preprocessor.explore_data(df)
    
    # Fit and transform data
    X, y_class, y_reg = preprocessor.fit_transform(df)
    
    print(f"\n📊 Processed Data Shape:")
    print(f"Features: {X.shape}")
    print(f"Classification target: {y_class.shape}")
    print(f"Regression target: {y_reg.shape}")
    
    print(f"\n🔧 Feature Columns ({len(X.columns)}):")
    for i, col in enumerate(X.columns):
        print(f"{i+1:2d}. {col}")
    
    # Split data for training
    X_train, X_test, y_class_train, y_class_test, y_reg_train, y_reg_test = train_test_split(
        X, y_class, y_reg, test_size=0.2, random_state=42, stratify=y_class
    )
    
    print(f"\n📊 Train/Test Split:")
    print(f"Training set: {X_train.shape[0]} samples")
    print(f"Test set: {X_test.shape[0]} samples")
    
    # Save preprocessor
    preprocessor.save_preprocessor()
    
    print("\n✅ Preprocessing pipeline completed successfully!")
    
    return X_train, X_test, y_class_train, y_class_test, y_reg_train, y_reg_test

if __name__ == "__main__":
    main()
