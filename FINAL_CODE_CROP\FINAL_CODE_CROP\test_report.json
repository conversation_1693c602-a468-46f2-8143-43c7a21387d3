{"timestamp": "2025-07-08 22:18:14", "total_tests": 18, "passed": 17, "failed": 1, "success_rate": 94.44444444444444, "detailed_results": ["✓ File exists: dataset_collector.py", "✓ File exists: train_pest_model.py", "✓ File exists: pest_detector.py", "✓ File exists: setup_pest_system.py", "✓ File exists: requirements.txt", "✓ File exists: app.py", "✓ Directory exists: datasets", "✓ Directory exists: models", "✓ Directory exists: templates", "✓ Directory exists: static", "✓ Import test passed: dataset_collector", "✓ Import test passed: train_pest_model", "✓ Import test passed: pest_detector", "✓ Dataset collector test passed", "✓ Pest detector test passed", "✗ Flask integration test failed: 'charmap' codec can't decode byte 0x8f in position 1838: character maps to <undefined>", "✓ Config file exists: training_config.json", "✓ Config file exists: DATA_COLLECTION_GUIDE.md"]}