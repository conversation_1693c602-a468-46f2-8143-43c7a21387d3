{% extends 'layout.html' %}

{% block content %}
<style>
  /* Override layout wrapper constraints */
  .content-wrapper {
    min-height: 100vh !important;
    padding-top: 0 !important;
    margin: 0 !important;
  }
  
  html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    font-family: 'Arial', sans-serif;
  }
  
  .fullscreen-form-container {
    min-height: 100vh;
    width: 100vw;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    margin-left: -15px;
    margin-right: -15px;
    margin-top: -2rem;
  }
  
  .fullscreen-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(46, 204, 113, 0.05) 0%, transparent 25%),
      radial-gradient(circle at 75% 75%, rgba(52, 152, 219, 0.05) 0%, transparent 25%),
      radial-gradient(circle at 50% 10%, rgba(155, 89, 182, 0.03) 0%, transparent 25%);
    background-size: 300px 300px, 400px 400px, 200px 200px;
  }
  
  .form-card {
    background: #ffffff;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 1000px;
    width: 100%;
    position: relative;
    z-index: 2;
    border: 1px solid #e9ecef;
  }
  
  .form-header {
    text-align: center;
    margin-bottom: 40px;
  }
  
  .form-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c5a27;
    margin-bottom: 15px;
    font-family: 'Poppins', sans-serif;
  }
  
  .form-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 20px;
  }
  
  .input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
  }
  
  .input-group {
    position: relative;
  }
  
  .input-label {
    display: block;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1rem;
  }
  
  .input-field {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #ffffff;
    box-sizing: border-box;
  }
  
  .input-field:focus {
    outline: none;
    border-color: #2c5a27;
    box-shadow: 0 0 0 3px rgba(44, 90, 39, 0.1);
  }
  
  .input-icon {
    color: #2c5a27;
    margin-right: 8px;
  }
  
  .submit-btn {
    width: 100%;
    padding: 16px;
    background: #2c5a27;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
  }
  
  .submit-btn:hover {
    background: #4a7c59;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(44, 90, 39, 0.3);
  }
  
  .submit-btn:active {
    transform: translateY(-1px);
  }
  
  .error-alert {
    background: #dc3545;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #c82333;
  }
  
  .input-hint {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-top: 5px;
  }
  
  @media (max-width: 768px) {
    .form-title {
      font-size: 2rem;
    }
    .input-grid {
      grid-template-columns: 1fr;
    }
    .form-card {
      padding: 30px 20px;
    }
  }
</style>

<div class="fullscreen-form-container">
  <div class="form-card">
    <div class="form-header">
      <h1 class="form-title">🌾 AI Crop Recommendation</h1>
      <p class="form-subtitle">Enter your farm conditions to get intelligent crop suggestions</p>
    </div>

    {% if error %}
    <div class="error-alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      <strong>Error:</strong> {{ error }}
    </div>
    {% endif %}

    <form method="POST" action="{{ url_for('crop_predict') }}" class="needs-validation" novalidate>
      <div class="input-grid">
        <div class="input-group">
          <label for="Nitrogen" class="input-label">
            <i class="fas fa-flask input-icon"></i>Nitrogen (N)
          </label>
          <input
            type="number"
            class="input-field"
            id="Nitrogen"
            name="nitrogen"
            value="{{n}}"
            min="1"
            max="140"
            step="0.01"
            placeholder="Enter nitrogen level"
            required
          />
          <div class="input-hint">Range: 1-140 kg/ha</div>
        </div>

        <div class="input-group">
          <label for="Phosphorus" class="input-label">
            <i class="fas fa-atom input-icon"></i>Phosphorous (P)
          </label>
          <input
            type="number"
            class="input-field"
            id="Phosphorus"
            name="phosphorous"
            value="{{p}}"
            min="5"
            max="145"
            step="0.01"
            placeholder="Enter phosphorous level"
            required
          />
          <div class="input-hint">Range: 5-145 kg/ha</div>
        </div>

        <div class="input-group">
          <label for="Potassium" class="input-label">
            <i class="fas fa-seedling input-icon"></i>Potassium (K)
          </label>
          <input
            type="number"
            class="input-field"
            id="Potassium"
            name="pottasium"
            value="{{k}}"
            min="5"
            max="205"
            step="0.01"
            placeholder="Enter potassium level"
            required
          />
          <div class="input-hint">Range: 5-205 kg/ha</div>
        </div>

        <div class="input-group">
          <label for="ph" class="input-label">
            <i class="fas fa-tint input-icon"></i>pH Level
          </label>
          <input
            type="number"
            class="input-field"
            id="ph"
            name="ph"
            value="{{ph}}"
            min="4"
            max="10"
            step="0.01"
            placeholder="Enter soil pH"
            required
          />
          <div class="input-hint">Range: 4.0-10.0</div>
        </div>

        <div class="input-group">
          <label for="Rainfall" class="input-label">
            <i class="fas fa-cloud-rain input-icon"></i>Rainfall
          </label>
          <input
            type="number"
            class="input-field"
            id="Rainfall"
            name="rainfall"
            value="{{rainfall}}"
            min="20"
            max="300"
            step="0.01"
            placeholder="Enter rainfall amount"
            required
          />
          <div class="input-hint">Range: 20-300 mm</div>
        </div>

        <div class="input-group">
          <label for="Temperature" class="input-label">
            <i class="fas fa-thermometer-half input-icon"></i>Temperature
          </label>
          <input
            type="number"
            class="input-field"
            id="Temperature"
            name="temp"
            value="{{temperature}}"
            min="8"
            max="44"
            step="0.01"
            placeholder="Enter temperature"
            required
          />
          <div class="input-hint">Range: 8-44°C</div>
        </div>

        <div class="input-group">
          <label for="Humidity" class="input-label">
            <i class="fas fa-water input-icon"></i>Humidity
          </label>
          <input
            type="number"
            class="input-field"
            id="Humidity"
            name="hum"
            value="{{humidity}}"
            min="14"
            max="100"
            step="0.01"
            placeholder="Enter humidity level"
            required
          />
          <div class="input-hint">Range: 14-100%</div>
        </div>
      </div>

      <button type="submit" class="submit-btn">
        <i class="fas fa-magic me-2"></i>Get AI Recommendation
      </button>
    </form>
  </div>
</div>

<script>
// Form validation
(function() {
  'use strict';
  window.addEventListener('load', function() {
    var forms = document.getElementsByClassName('needs-validation');
    var validation = Array.prototype.filter.call(forms, function(form) {
      form.addEventListener('submit', function(event) {
        if (form.checkValidity() === false) {
          event.preventDefault();
          event.stopPropagation();
        }
        form.classList.add('was-validated');
      }, false);
    });
  }, false);
})();

// Input animations
document.querySelectorAll('.input-field').forEach(input => {
  input.addEventListener('focus', function() {
    this.parentElement.style.transform = 'scale(1.02)';
  });
  
  input.addEventListener('blur', function() {
    this.parentElement.style.transform = 'scale(1)';
  });
});
</script>

{% endblock %}
