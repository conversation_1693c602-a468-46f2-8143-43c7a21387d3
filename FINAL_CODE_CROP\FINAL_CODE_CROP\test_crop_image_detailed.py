import requests
import time

# Test data for crop recommendation
test_data = {
    'nitrogen': 90,
    'phosphorous': 42,
    'pottasium': 43,
    'temp': 20.9,
    'hum': 82.0,
    'ph': 6.5,
    'rainfall': 202.9
}

try:
    print("🧪 Testing crop recommendation with sample data...")
    print(f"📊 Test data: {test_data}")
    
    # Submit POST request to crop prediction endpoint
    response = requests.post('http://127.0.0.1:5000/crop_predict', 
                           data=test_data, 
                           timeout=10)
    
    if response.status_code == 200:
        print("✅ Request successful!")
        
        # Save the response to a file
        with open('crop_result_test.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("📄 Response saved to crop_result_test.html")
        
        # Check for crop image information
        import re
        
        # Look for the crop image source
        img_matches = re.findall(r'<img[^>]*src="([^"]*)"[^>]*crop-image', response.text)
        if img_matches:
            print(f"🖼️ Crop image source: {img_matches[0]}")
        
        # Look for crop information
        crop_match = re.search(r'class="crop-name"[^>]*>([^<]+)', response.text)
        if crop_match:
            crop_name = crop_match.group(1).strip()
            print(f"🌾 Predicted crop: {crop_name}")
            
        # Look for crop description
        desc_match = re.search(r'Rice is one of the most important staple crops', response.text)
        if desc_match:
            print("📝 Crop description found!")
            
    else:
        print(f"❌ Request failed with status code: {response.status_code}")

except requests.exceptions.RequestException as e:
    print(f"❌ Network error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
