"""
Dataset Download and Organization Script
This script helps you download and organize real datasets for pest and weed management
"""

import os
import requests
import zipfile
import shutil
from pathlib import Path
import json
import time
from urllib.parse import urlparse

class DatasetDownloader:
    def __init__(self, base_path="datasets"):
        self.base_path = Path(base_path)
        self.download_path = self.base_path / "raw_downloads"
        self.download_path.mkdir(parents=True, exist_ok=True)
        
    def download_plantvillage_dataset(self):
        """Download PlantVillage dataset"""
        print("📥 Downloading PlantVillage Dataset...")
        
        # PlantVillage dataset URL
        url = "https://github.com/spMohanty/PlantVillage-Dataset/archive/refs/heads/master.zip"
        
        try:
            print("  🌐 Connecting to GitHub...")
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            zip_file = self.download_path / "plantvillage.zip"
            
            print("  📦 Downloading dataset (this may take a while)...")
            with open(zip_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print("  📂 Extracting dataset...")
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall(self.download_path)
            
            print("  ✅ PlantVillage dataset downloaded successfully!")
            return True
            
        except Exception as e:
            print(f"  ❌ Error downloading PlantVillage: {e}")
            return False
    
    def download_kaggle_datasets(self):
        """Instructions for downloading Kaggle datasets"""
        print("📥 Kaggle Dataset Instructions:")
        print("  1. Install Kaggle CLI: pip install kaggle")
        print("  2. Get API key from https://www.kaggle.com/settings")
        print("  3. Place kaggle.json in ~/.kaggle/ directory")
        print("  4. Run these commands:")
        print("     kaggle datasets download -d vipoooool/new-plant-diseases-dataset")
        print("     kaggle datasets download -d emmarex/plantdisease")
        print("     kaggle datasets download -d abdallahalidev/plantvillage-dataset")
        print("  5. Move downloaded files to raw_downloads/ folder")
        return True
    
    def create_sample_dataset(self):
        """Create sample dataset with placeholder images"""
        print("📸 Creating sample dataset structure...")
        
        categories = {
            "pests": ["aphids", "whiteflies", "spider_mites", "thrips", "caterpillars", "beetles"],
            "weeds": ["dandelion", "crabgrass", "pigweed", "chickweed", "bindweed"],
            "healthy": ["crops"]
        }
        
        sample_count = 10  # Create 10 sample files per category
        
        for category, subcategories in categories.items():
            for subcategory in subcategories:
                category_path = self.base_path / category / subcategory
                category_path.mkdir(parents=True, exist_ok=True)
                
                # Create placeholder text files (in real scenario, these would be images)
                for i in range(sample_count):
                    placeholder_file = category_path / f"sample_{i:03d}.txt"
                    with open(placeholder_file, 'w') as f:
                        f.write(f"Placeholder for {category}_{subcategory} image {i}")
                
                print(f"  ✅ Created {sample_count} samples for {category}/{subcategory}")
        
        return True
    
    def organize_existing_dataset(self, source_path):
        """Organize existing dataset into our structure"""
        print(f"📁 Organizing dataset from {source_path}...")
        
        source = Path(source_path)
        if not source.exists():
            print(f"  ❌ Source path {source_path} does not exist")
            return False
        
        # Look for common dataset structures
        if (source / "PlantVillage").exists():
            self.organize_plantvillage(source / "PlantVillage")
        elif (source / "plant_disease").exists():
            self.organize_plant_disease(source / "plant_disease")
        else:
            print(f"  ⚠️  Unknown dataset structure in {source_path}")
            self.organize_generic(source)
        
        return True
    
    def organize_plantvillage(self, source_path):
        """Organize PlantVillage dataset"""
        print("  📋 Organizing PlantVillage dataset...")
        
        # PlantVillage has structure: Plant_Disease/
        for plant_dir in source_path.iterdir():
            if plant_dir.is_dir():
                plant_name = plant_dir.name.lower()
                
                # Determine category based on plant name
                if any(pest in plant_name for pest in ["aphid", "whitefly", "mite", "thrip", "caterpillar", "beetle"]):
                    category = "pests"
                elif any(weed in plant_name for weed in ["dandelion", "crabgrass", "pigweed", "chickweed"]):
                    category = "weeds"
                elif "healthy" in plant_name:
                    category = "healthy"
                    plant_name = "crops"
                else:
                    category = "pests"  # Default to pests
                
                # Create target directory
                target_dir = self.base_path / category / plant_name
                target_dir.mkdir(parents=True, exist_ok=True)
                
                # Copy images
                image_count = 0
                for image_file in plant_dir.glob("*.jpg"):
                    target_file = target_dir / f"{image_count:05d}.jpg"
                    shutil.copy2(image_file, target_file)
                    image_count += 1
                
                print(f"    ✅ Organized {image_count} images for {category}/{plant_name}")
    
    def organize_generic(self, source_path):
        """Organize generic dataset structure"""
        print("  📋 Organizing generic dataset...")
        
        # Look for image files and organize them
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        
        for root, dirs, files in os.walk(source_path):
            for file in files:
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    # Try to determine category from path
                    path_parts = Path(root).parts
                    
                    category = "pests"  # Default
                    subcategory = "unknown"
                    
                    for part in path_parts:
                        part_lower = part.lower()
                        if any(pest in part_lower for pest in ["aphid", "whitefly", "mite", "thrip"]):
                            category = "pests"
                            subcategory = part_lower
                        elif any(weed in part_lower for weed in ["dandelion", "crabgrass", "pigweed"]):
                            category = "weeds"
                            subcategory = part_lower
                        elif "healthy" in part_lower:
                            category = "healthy"
                            subcategory = "crops"
                    
                    # Create target directory and copy file
                    target_dir = self.base_path / category / subcategory
                    target_dir.mkdir(parents=True, exist_ok=True)
                    
                    source_file = Path(root) / file
                    target_file = target_dir / file
                    shutil.copy2(source_file, target_file)
    
    def show_dataset_status(self):
        """Show current dataset status"""
        print("\n📊 Current Dataset Status:")
        print("=" * 50)
        
        total_files = 0
        
        for category in ["pests", "weeds", "healthy"]:
            category_path = self.base_path / category
            if category_path.exists():
                print(f"\n🗂️  {category.title()}:")
                category_total = 0
                
                for subcategory in category_path.iterdir():
                    if subcategory.is_dir():
                        # Count image files
                        image_count = len(list(subcategory.glob("*.jpg"))) + \
                                    len(list(subcategory.glob("*.png"))) + \
                                    len(list(subcategory.glob("*.txt")))  # Include sample files
                        
                        print(f"  📁 {subcategory.name}: {image_count} files")
                        category_total += image_count
                
                print(f"  📈 Total {category}: {category_total} files")
                total_files += category_total
        
        print(f"\n🎯 Grand Total: {total_files} files")
        
        if total_files == 0:
            print("\n⚠️  No dataset found! Please:")
            print("   1. Download datasets using this script")
            print("   2. Or manually place images in the appropriate folders")
            print("   3. Or create sample dataset for testing")
        
        return total_files
    
    def download_free_datasets(self):
        """Download free datasets from various sources"""
        print("🆓 Free Dataset Sources:")
        print("=" * 50)
        
        free_sources = [
            {
                "name": "PlantVillage",
                "url": "https://github.com/spMohanty/PlantVillage-Dataset",
                "description": "54,000+ images of diseased and healthy plants",
                "download_cmd": "wget https://github.com/spMohanty/PlantVillage-Dataset/archive/master.zip"
            },
            {
                "name": "Open Images Dataset",
                "url": "https://storage.googleapis.com/openimages/web/index.html",
                "description": "Search for 'plant', 'pest', 'weed' categories",
                "download_cmd": "Use FiftyOne library: fiftyone zoo datasets load-dataset open-images-v6"
            },
            {
                "name": "iNaturalist",
                "url": "https://www.inaturalist.org/",
                "description": "Community-driven species identification",
                "download_cmd": "Use API: https://www.inaturalist.org/pages/api+reference"
            },
            {
                "name": "Crop Disease Dataset",
                "url": "https://www.kaggle.com/datasets/vipoooool/new-plant-diseases-dataset",
                "description": "38 different classes of plant diseases",
                "download_cmd": "kaggle datasets download -d vipoooool/new-plant-diseases-dataset"
            }
        ]
        
        for source in free_sources:
            print(f"\n📦 {source['name']}")
            print(f"   🌐 URL: {source['url']}")
            print(f"   📝 Description: {source['description']}")
            print(f"   💻 Download: {source['download_cmd']}")
        
        return True

def main():
    """Main function to run dataset operations"""
    print("🌾 PEST AND WEED DATASET MANAGER")
    print("=" * 50)
    
    downloader = DatasetDownloader()
    
    # Show current status
    total_files = downloader.show_dataset_status()
    
    if total_files == 0:
        print("\n🚀 Getting Started Options:")
        print("1. Download PlantVillage dataset (recommended)")
        print("2. Create sample dataset for testing")
        print("3. View free dataset sources")
        print("4. Organize existing dataset")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            downloader.download_plantvillage_dataset()
        elif choice == "2":
            downloader.create_sample_dataset()
        elif choice == "3":
            downloader.download_free_datasets()
        elif choice == "4":
            source_path = input("Enter path to existing dataset: ").strip()
            downloader.organize_existing_dataset(source_path)
        else:
            print("Invalid choice. Creating sample dataset...")
            downloader.create_sample_dataset()
        
        # Show updated status
        print("\n" + "=" * 50)
        downloader.show_dataset_status()
    
    print("\n✅ Dataset operations completed!")
    print("\n🚀 Next steps:")
    print("1. Review your dataset in the datasets/ folder")
    print("2. Run: python train_pest_model.py")
    print("3. Test with: python demo_pest_system.py")

if __name__ == "__main__":
    main()
