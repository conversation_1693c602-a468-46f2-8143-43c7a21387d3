{% extends 'layout.html' %} {% block body %}

<style>
  .mt-0 {
    margin-top: 50 !important;
  }
  html body {
    /* background-color: rgb(206, 206, 228); */
    background-color: rgb(104 108 115);
  }
</style>

<section
  style="
    height: 90vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
  "
>
  <div class="container mx-auto my-50" style="margin: 5px">
    <div class="row">
      <div class="col-sm py-2 py-md-3">
        <div class="" style="justify-content: center">
          <h1
            class="text-center"
            style="
              font-weight: bolder;
              text-transform: capitalize;
              color: #cfc547;
              font-size: 49px;
              text-shadow: 10px 10px 25px rgb(81, 67, 21),
                -10px 10px 25px rgb(81, 67, 21),
                -10px -10px 25px rgb(81, 67, 21),
                10px -10px 25px rgb(81, 67, 21);
            "
          >
            The fertilizer you have to use is
          </h1>
          <div class="uderline1"></div>
        </div>
      </div>
    </div>
  </div>

  <section>
    <aside class="profile-card">
      <header>
        <!-- here’s the avatar -->
        <a>
          <img src="../static/images/farmer1.jpg" />
        </a>

        <!-- the username -->
        <h1>{{ res }} </h1>

        <!-- and role or location -->
      </header>
    </aside>
  </section>
</section>
{% endblock %}
