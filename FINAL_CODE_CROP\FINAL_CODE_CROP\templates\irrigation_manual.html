<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Irrigation Prediction - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .form-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .form-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .section-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #28a745;
            padding-bottom: 5px;
        }
        .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        .btn-predict {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .btn-predict:hover {
            background: linear-gradient(45deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .input-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        .input-wrapper {
            position: relative;
        }
        .input-wrapper input,
        .input-wrapper select {
            padding-left: 35px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">
                <i class="fas fa-seedling me-2"></i>AgroPro
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('irrigation_table') }}">
                    <i class="fas fa-table me-1"></i>Prediction Table
                </a>
                <a class="nav-link" href="{{ url_for('irrigation_management') }}">
                    <i class="fas fa-tint me-1"></i>Simple Form
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="display-5 fw-bold text-white">
                <i class="fas fa-edit me-3"></i>Manual Irrigation Prediction
            </h1>
            <p class="lead text-white-50">Enter your field conditions to get AI-powered irrigation recommendations</p>
        </div>

        <!-- Error Message -->
        {% if error %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endif %}

        <!-- Manual Input Form -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-container">
                    <form method="POST" action="{{ url_for('irrigation_manual') }}">
                        
                        <!-- Basic Crop Information -->
                        <div class="form-section">
                            <h4 class="section-title">
                                <i class="fas fa-seedling me-2"></i>Crop & Location Information
                            </h4>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Crop Type</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-seedling input-icon"></i>
                                        <select class="form-select" name="crop_type" required>
                                            <option value="">Select Crop</option>
                                            <option value="wheat">Wheat</option>
                                            <option value="rice">Rice</option>
                                            <option value="corn">Corn/Maize</option>
                                            <option value="cotton">Cotton</option>
                                            <option value="sugarcane">Sugarcane</option>
                                            <option value="tomato">Tomato</option>
                                            <option value="potato">Potato</option>
                                            <option value="onion">Onion</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Soil Type</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-mountain input-icon"></i>
                                        <select class="form-select" name="soil_type" required>
                                            <option value="">Select Soil Type</option>
                                            <option value="sandy">Sandy</option>
                                            <option value="loam">Loam</option>
                                            <option value="clay">Clay</option>
                                            <option value="silt">Silt</option>
                                            <option value="sandy_loam">Sandy Loam</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Location/State</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-map-marker-alt input-icon"></i>
                                        <input type="text" class="form-control" name="location" 
                                               placeholder="e.g., Punjab, Maharashtra" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Weather Conditions -->
                        <div class="form-section">
                            <h4 class="section-title">
                                <i class="fas fa-cloud-sun me-2"></i>Current Weather Conditions
                            </h4>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Temperature (°C)</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-thermometer-half input-icon"></i>
                                        <input type="number" class="form-control" name="temperature" 
                                               min="0" max="50" step="0.1" value="25" required>
                                    </div>
                                    <small class="text-muted">Current air temperature</small>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Humidity (%)</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-tint input-icon"></i>
                                        <input type="number" class="form-control" name="humidity" 
                                               min="0" max="100" step="1" value="60" required>
                                    </div>
                                    <small class="text-muted">Relative humidity percentage</small>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Recent Rainfall (mm)</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-cloud-rain input-icon"></i>
                                        <input type="number" class="form-control" name="rainfall" 
                                               min="0" max="200" step="0.1" value="10" required>
                                    </div>
                                    <small class="text-muted">Rainfall in last 7 days</small>
                                </div>
                            </div>
                        </div>

                        <!-- Soil Conditions -->
                        <div class="form-section">
                            <h4 class="section-title">
                                <i class="fas fa-layer-group me-2"></i>Soil & Field Conditions
                            </h4>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Current Soil Moisture (%)</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-droplet input-icon"></i>
                                        <input type="number" class="form-control" name="soil_moisture" 
                                               min="0" max="100" step="0.1" value="30" required>
                                    </div>
                                    <small class="text-muted">Soil moisture content (optimal: 35-45%)</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Field Size (hectares)</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-expand-arrows-alt input-icon"></i>
                                        <input type="number" class="form-control" name="field_size" 
                                               min="0.1" max="1000" step="0.1" value="1.0" required>
                                    </div>
                                    <small class="text-muted">Total field area to irrigate</small>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-success btn-lg btn-predict">
                                <i class="fas fa-brain me-2"></i>Get AI Prediction
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Info Cards -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card text-center border-0 bg-light">
                    <div class="card-body">
                        <i class="fas fa-robot fa-2x text-primary mb-2"></i>
                        <h6>AI-Powered</h6>
                        <small class="text-muted">Uses trained machine learning models</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center border-0 bg-light">
                    <div class="card-body">
                        <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                        <h6>Real-time Analysis</h6>
                        <small class="text-muted">Instant predictions based on your inputs</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center border-0 bg-light">
                    <div class="card-body">
                        <i class="fas fa-leaf fa-2x text-info mb-2"></i>
                        <h6>Crop Specific</h6>
                        <small class="text-muted">Tailored recommendations for each crop</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
