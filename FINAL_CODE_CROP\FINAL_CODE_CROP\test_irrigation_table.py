#!/usr/bin/env python3
"""
Test script to verify irrigation_table route works
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_irrigation_table():
    try:
        # Import the app
        from app import app
        
        # Create test client
        with app.test_client() as client:
            print("🧪 Testing /irrigation_table route...")
            
            # Test the route
            response = client.get('/irrigation_table')
            
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ irrigation_table route works!")
                print(f"📄 Response length: {len(response.data)} bytes")
                
                # Check if it contains expected content
                content = response.data.decode('utf-8')
                if 'Real-time Irrigation Predictions' in content:
                    print("✅ Response contains expected title")
                if 'Wheat' in content:
                    print("✅ Response contains crop data")
                if 'irrigation_needed' in content.lower():
                    print("✅ Response contains irrigation data")
                    
                return True
            else:
                print(f"❌ Route failed with status {response.status_code}")
                print(f"📄 Response: {response.data.decode('utf-8')[:500]}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing irrigation_table: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_prediction():
    try:
        print("\n🧪 Testing direct ML prediction...")
        from app import get_direct_irrigation_prediction, IRRIGATION_ML_AVAILABLE
        
        print(f"📊 ML Available: {IRRIGATION_ML_AVAILABLE}")
        
        if IRRIGATION_ML_AVAILABLE:
            result = get_direct_irrigation_prediction(
                crop_type='wheat',
                soil_type='loam',
                location='Punjab',
                planting_date='2024-01-01',
                field_size=1.0
            )
            
            if result:
                print("✅ Direct ML prediction works!")
                print(f"🎯 Irrigation needed: {result.get('irrigation_needed')}")
                print(f"💧 Amount: {result.get('irrigation_amount_mm')} mm")
                print(f"📊 Probability: {result.get('irrigation_probability'):.1%}")
                return True
            else:
                print("❌ Direct ML prediction returned None")
                return False
        else:
            print("⚠️ ML models not available")
            return False
            
    except Exception as e:
        print(f"❌ Error testing direct prediction: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 Testing Irrigation Table System")
    print("=" * 50)
    
    # Test direct prediction first
    prediction_works = test_direct_prediction()
    
    # Test the route
    route_works = test_irrigation_table()
    
    print("\n" + "=" * 50)
    print("📋 Test Results:")
    print(f"  Direct ML Prediction: {'✅ PASS' if prediction_works else '❌ FAIL'}")
    print(f"  Irrigation Table Route: {'✅ PASS' if route_works else '❌ FAIL'}")
    
    if prediction_works and route_works:
        print("\n🎉 All tests passed! Irrigation table should work.")
        print("🌐 Try accessing: http://127.0.0.1:5000/irrigation_table")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")
