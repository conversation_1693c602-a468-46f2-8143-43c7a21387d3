{% extends 'layout.html' %}

{% block content %}
<style>
  body {
    background-image: linear-gradient(rgba(44, 90, 39, 0.3), rgba(74, 124, 89, 0.3)), 
                      url('/static/images/Background4.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    min-height: 100vh;
    animation: subtle-bg-shift 20s ease-in-out infinite alternate;
  }
  
  @keyframes subtle-bg-shift {
    0% { background-position: center center; }
    100% { background-position: center bottom; }
  }
  
  .crop-form-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 30px 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    display: grid;
    grid-template-columns: 1fr 600px;
    gap: 40px;
    align-items: start;
  }
  
  .left-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 60vh;
  }
  
  .right-content {
    position: sticky;
    top: 100px;
  }
  
  .page-header {
    background: rgba(44, 90, 39, 0.95);
    color: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(44, 90, 39, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
  }
  
  .page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
  }
  
  .form-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
  }
  
  .input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
  }
  
  .input-group {
    position: relative;
  }
  
  .input-label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1rem;
  }
  
  .input-field {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #ffffff;
    box-sizing: border-box;
  }
  
  .input-field:focus {
    outline: none;
    border-color: #2c5a27;
    box-shadow: 0 0 0 3px rgba(44, 90, 39, 0.1);
  }
  
  .input-icon {
    color: #2c5a27;
    margin-right: 8px;
  }
  
  .submit-btn {
    width: 100%;
    padding: 16px;
    background: #2c5a27;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
  }
  
  .submit-btn:hover {
    background: #4a7c59;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(44, 90, 39, 0.3);
  }
  
  .error-alert {
    background: #dc3545;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #c82333;
  }
  
  .input-hint {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-top: 5px;
  }
  
  @media (max-width: 768px) {
    .page-title {
      font-size: 2rem;
    }
    .input-grid {
      grid-template-columns: 1fr;
    }
    .form-card {
      padding: 30px 20px;
    }
    .crop-form-container {
      grid-template-columns: 1fr;
      gap: 20px;
    }
    .right-content {
      position: static;
    }
  }
</style>

<div class="crop-form-container">
  <div class="left-content">
    <div class="page-header">
      <h1 class="page-title">AI Crop Recommendation</h1>
      <p class="page-subtitle">Enter your farm conditions to get intelligent crop suggestions</p>
    </div>
    
    <div style="background: rgba(255, 255, 255, 0.9); border-radius: 12px; padding: 30px; margin-top: 20px;">
      <h3 style="color: #2c5a27; margin-bottom: 20px;">
        How It Works
      </h3>
      <div style="color: #2c3e50; line-height: 1.6;">
        <p><span style="font-weight: bold; color: #2c5a27; margin-right: 10px;">1.</span>Our AI analyzes your soil and environmental conditions</p>
        <p><span style="font-weight: bold; color: #2c5a27; margin-right: 10px;">2.</span>Machine learning models predict optimal crops</p>
        <p><span style="font-weight: bold; color: #2c5a27; margin-right: 10px;">3.</span>Get personalized recommendations for maximum yield</p>
        <p><span style="font-weight: bold; color: #2c5a27; margin-right: 10px;">4.</span>Sustainable farming practices integrated</p>
      </div>
    </div>
  </div>

  <div class="right-content">
    <div class="form-card">
    {% if error %}
    <div class="error-alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      <strong>Error:</strong> {{ error }}
    </div>
    {% endif %}

    <form method="POST" action="{{ url_for('crop_predict') }}" class="needs-validation" novalidate>
      <div class="input-grid">
        <div class="input-group">
          <label for="Nitrogen" class="input-label">
            <i class="fas fa-flask input-icon"></i>Nitrogen (N)
          </label>
          <input
            type="number"
            class="input-field"
            id="Nitrogen"
            name="nitrogen"
            value="{{n}}"
            min="1"
            max="140"
            step="0.01"
            placeholder="Enter nitrogen level"
            required
          />
          <div class="input-hint">Range: 1-140 kg/ha</div>
        </div>

        <div class="input-group">
          <label for="Phosphorus" class="input-label">
            <i class="fas fa-atom input-icon"></i>Phosphorous (P)
          </label>
          <input
            type="number"
            class="input-field"
            id="Phosphorus"
            name="phosphorous"
            value="{{p}}"
            min="5"
            max="145"
            step="0.01"
            placeholder="Enter phosphorous level"
            required
          />
          <div class="input-hint">Range: 5-145 kg/ha</div>
        </div>

        <div class="input-group">
          <label for="Potassium" class="input-label">
            <i class="fas fa-seedling input-icon"></i>Potassium (K)
          </label>
          <input
            type="number"
            class="input-field"
            id="Potassium"
            name="pottasium"
            value="{{k}}"
            min="5"
            max="205"
            step="0.01"
            placeholder="Enter potassium level"
            required
          />
          <div class="input-hint">Range: 5-205 kg/ha</div>
        </div>

        <div class="input-group">
          <label for="ph" class="input-label">
            <i class="fas fa-tint input-icon"></i>pH Level
          </label>
          <input
            type="number"
            class="input-field"
            id="ph"
            name="ph"
            value="{{ph}}"
            min="4"
            max="10"
            step="0.01"
            placeholder="Enter soil pH"
            required
          />
          <div class="input-hint">Range: 4.0-10.0</div>
        </div>

        <div class="input-group">
          <label for="Rainfall" class="input-label">
            <i class="fas fa-cloud-rain input-icon"></i>Rainfall
          </label>
          <input
            type="number"
            class="input-field"
            id="Rainfall"
            name="rainfall"
            value="{{rainfall}}"
            min="20"
            max="300"
            step="0.01"
            placeholder="Enter rainfall amount"
            required
          />
          <div class="input-hint">Range: 20-300 mm</div>
        </div>

        <div class="input-group">
          <label for="Temperature" class="input-label">
            <i class="fas fa-thermometer-half input-icon"></i>Temperature
          </label>
          <input
            type="number"
            class="input-field"
            id="Temperature"
            name="temp"
            value="{{temperature}}"
            min="8"
            max="44"
            step="0.01"
            placeholder="Enter temperature"
            required
          />
          <div class="input-hint">Range: 8-44°C</div>
        </div>

        <div class="input-group">
          <label for="Humidity" class="input-label">
            <i class="fas fa-water input-icon"></i>Humidity
          </label>
          <input
            type="number"
            class="input-field"
            id="Humidity"
            name="hum"
            value="{{humidity}}"
            min="14"
            max="100"
            step="0.01"
            placeholder="Enter humidity level"
            required
          />
          <div class="input-hint">Range: 14-100%</div>
        </div>
      </div>

      <button type="submit" class="submit-btn">
        Predict
      </button>
    </form>
    </div>
  </div>
</div>

<script>
// Form validation
(function() {
  'use strict';
  window.addEventListener('load', function() {
    var forms = document.getElementsByClassName('needs-validation');
    var validation = Array.prototype.filter.call(forms, function(form) {
      form.addEventListener('submit', function(event) {
        if (form.checkValidity() === false) {
          event.preventDefault();
          event.stopPropagation();
        }
        form.classList.add('was-validated');
      }, false);
    });
  }, false);
})();

// Input animations
document.querySelectorAll('.input-field').forEach(input => {
  input.addEventListener('focus', function() {
    this.parentElement.style.transform = 'scale(1.02)';
  });
  
  input.addEventListener('blur', function() {
    this.parentElement.style.transform = 'scale(1)';
  });
});
</script>

{% endblock %}
