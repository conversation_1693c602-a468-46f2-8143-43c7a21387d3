import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import sqlite3
import time

class MarketPriceAPI:
    def __init__(self):
        # Government of India API endpoints
        self.agmarknet_url = "https://api.data.gov.in/resource/9ef84268-d588-465a-a308-a864a43d0070"
        self.api_key = "579b464db66ec23bdd000001cdd3946e44ce4aad7209ff7b23ac571b"  # Replace with your API key
        
        # Initialize database
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database for storing market prices"""
        conn = sqlite3.connect('market_prices.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_prices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                crop TEXT NOT NULL,
                state TEXT NOT NULL,
                district TEXT,
                market TEXT NOT NULL,
                current_price REAL,
                min_price REAL,
                max_price REAL,
                modal_price REAL,
                variety TEXT,
                arrival_date TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def fetch_agmarknet_data(self, limit=100):
        """Fetch data from Government's Agmarknet API"""
        try:
            params = {
                'api-key': self.api_key,
                'format': 'json',
                'limit': limit,
                'offset': 0
            }
            
            response = requests.get(self.agmarknet_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return data.get('records', [])
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching Agmarknet data: {e}")
            return []
    
    def fetch_commodity_prices(self):
        """Fetch commodity prices from multiple sources"""
        try:
            # Alternative API for commodity prices
            url = "https://api.commodities-api.com/v1/latest"
            params = {
                'access_key': 'your-commodities-api-key',  # Replace with actual key
                'base': 'INR',
                'symbols': 'WHEAT,RICE,CORN,COTTON,SUGAR'
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                return response.json()
            
        except Exception as e:
            print(f"Error fetching commodity prices: {e}")
        
        return None
    
    def get_sample_market_data(self):
        """Generate sample market data for demonstration"""
        crops = ['Rice', 'Wheat', 'Cotton', 'Sugarcane', 'Tomato', 'Onion', 'Potato', 'Corn']
        states = ['Punjab', 'Haryana', 'Uttar Pradesh', 'Maharashtra', 'Karnataka', 'Tamil Nadu']
        markets = ['Delhi', 'Mumbai', 'Bangalore', 'Chennai', 'Kolkata', 'Hyderabad']
        
        sample_data = []
        
        for i, crop in enumerate(crops):
            # Generate realistic price ranges for different crops
            base_prices = {
                'Rice': 1800, 'Wheat': 2100, 'Cotton': 5200, 'Sugarcane': 300,
                'Tomato': 1500, 'Onion': 800, 'Potato': 1200, 'Corn': 1600
            }
            
            base_price = base_prices.get(crop, 1500)
            
            # Add some variation
            import random
            price_variation = random.uniform(-10, 10)  # ±10% variation
            current_price = base_price + (base_price * price_variation / 100)
            
            # Calculate change from previous day
            previous_price = current_price * random.uniform(0.95, 1.05)
            change = current_price - previous_price
            change_percent = (change / previous_price) * 100
            
            sample_data.append({
                'crop': crop,
                'state': states[i % len(states)],
                'market': markets[i % len(markets)],
                'current_price': round(current_price, 2),
                'min_price': round(current_price * 0.95, 2),
                'max_price': round(current_price * 1.05, 2),
                'change': round(change, 2),
                'change_percent': round(change_percent, 2),
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'arrival_date': datetime.now().strftime('%Y-%m-%d')
            })
        
        return sample_data
    
    def store_market_data(self, data):
        """Store market data in database"""
        conn = sqlite3.connect('market_prices.db')
        cursor = conn.cursor()
        
        for record in data:
            cursor.execute('''
                INSERT INTO market_prices 
                (crop, state, market, current_price, min_price, max_price, arrival_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.get('crop'),
                record.get('state'),
                record.get('market'),
                record.get('current_price'),
                record.get('min_price'),
                record.get('max_price'),
                record.get('arrival_date')
            ))
        
        conn.commit()
        conn.close()
    
    def get_latest_prices(self, crop_filter=None, state_filter=None):
        """Get latest market prices from database"""
        conn = sqlite3.connect('market_prices.db')
        
        query = '''
            SELECT crop, state, market, current_price, min_price, max_price, 
                   arrival_date, created_at
            FROM market_prices 
            WHERE created_at >= datetime('now', '-1 day')
        '''
        
        params = []
        if crop_filter and crop_filter != 'all':
            query += ' AND LOWER(crop) = LOWER(?)'
            params.append(crop_filter)
        
        if state_filter and state_filter != 'all':
            query += ' AND LOWER(state) = LOWER(?)'
            params.append(state_filter)
        
        query += ' ORDER BY created_at DESC'
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        if df.empty:
            # Return sample data if no real data available
            return self.get_sample_market_data()
        
        return df.to_dict('records')
    
    def update_market_prices(self):
        """Update market prices from APIs"""
        try:
            # Try to fetch from government API first
            agmarknet_data = self.fetch_agmarknet_data()
            
            if agmarknet_data:
                # Process and store real data
                processed_data = self.process_agmarknet_data(agmarknet_data)
                self.store_market_data(processed_data)
                return processed_data
            else:
                # Fallback to sample data
                sample_data = self.get_sample_market_data()
                self.store_market_data(sample_data)
                return sample_data
                
        except Exception as e:
            print(f"Error updating market prices: {e}")
            return self.get_sample_market_data()
    
    def process_agmarknet_data(self, raw_data):
        """Process raw Agmarknet data into our format"""
        processed_data = []
        
        for record in raw_data:
            try:
                processed_data.append({
                    'crop': record.get('commodity', 'Unknown'),
                    'state': record.get('state', 'Unknown'),
                    'market': record.get('market', 'Unknown'),
                    'current_price': float(record.get('modal_price', 0)),
                    'min_price': float(record.get('min_price', 0)),
                    'max_price': float(record.get('max_price', 0)),
                    'arrival_date': record.get('arrival_date', datetime.now().strftime('%Y-%m-%d')),
                    'change': 0,  # Calculate based on historical data
                    'change_percent': 0,
                    'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            except (ValueError, TypeError):
                continue
        
        return processed_data

# Initialize the API
market_api = MarketPriceAPI()
