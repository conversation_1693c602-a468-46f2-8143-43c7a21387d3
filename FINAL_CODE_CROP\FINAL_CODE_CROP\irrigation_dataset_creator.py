"""
Comprehensive Irrigation Dataset Creator
Creates a realistic irrigation dataset based on agricultural science principles
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import requests

def download_crop_recommendation_dataset():
    """Download crop recommendation dataset from GitHub"""
    datasets_dir = Path("datasets")
    datasets_dir.mkdir(exist_ok=True)
    
    # Crop recommendation dataset
    crop_url = "https://raw.githubusercontent.com/atharvaingle/Crop-Recommendation-Dataset/master/Crop_recommendation.csv"
    crop_file = datasets_dir / "crop_recommendation.csv"
    
    try:
        print(f"Downloading crop recommendation dataset...")
        response = requests.get(crop_url)
        response.raise_for_status()
        
        with open(crop_file, 'wb') as f:
            f.write(response.content)
        
        # Load and preview the dataset
        df = pd.read_csv(crop_file)
        print(f"✅ Crop Recommendation Dataset Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print(f"Sample data:\n{df.head()}")
        
        return df
    except Exception as e:
        print(f"❌ Error downloading dataset: {e}")
        return None

def create_comprehensive_irrigation_dataset():
    """Create a comprehensive irrigation dataset based on real agricultural science"""
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Define crop types and their detailed characteristics
    crops = {
        'rice': {
            'water_need': 'very_high', 'growth_days': 120, 'base_water': 55,
            'critical_stages': [30, 60, 90], 'kc_values': [0.6, 1.2, 0.8]
        },
        'wheat': {
            'water_need': 'medium', 'growth_days': 90, 'base_water': 25,
            'critical_stages': [20, 45, 70], 'kc_values': [0.5, 1.1, 0.7]
        },
        'corn': {
            'water_need': 'medium_high', 'growth_days': 100, 'base_water': 32,
            'critical_stages': [25, 55, 80], 'kc_values': [0.6, 1.3, 0.8]
        },
        'cotton': {
            'water_need': 'medium', 'growth_days': 150, 'base_water': 35,
            'critical_stages': [40, 80, 120], 'kc_values': [0.7, 1.2, 0.6]
        },
        'sugarcane': {
            'water_need': 'very_high', 'growth_days': 365, 'base_water': 65,
            'critical_stages': [60, 180, 300], 'kc_values': [0.8, 1.4, 0.9]
        },
        'tomato': {
            'water_need': 'medium', 'growth_days': 80, 'base_water': 28,
            'critical_stages': [20, 40, 65], 'kc_values': [0.6, 1.1, 0.8]
        },
        'potato': {
            'water_need': 'medium', 'growth_days': 70, 'base_water': 22,
            'critical_stages': [15, 35, 55], 'kc_values': [0.5, 1.0, 0.7]
        },
        'onion': {
            'water_need': 'low_medium', 'growth_days': 120, 'base_water': 18,
            'critical_stages': [30, 70, 100], 'kc_values': [0.5, 0.9, 0.6]
        },
        'soybean': {
            'water_need': 'medium', 'growth_days': 95, 'base_water': 26,
            'critical_stages': [25, 50, 75], 'kc_values': [0.6, 1.1, 0.7]
        },
        'barley': {
            'water_need': 'low_medium', 'growth_days': 85, 'base_water': 20,
            'critical_stages': [20, 40, 65], 'kc_values': [0.5, 1.0, 0.6]
        }
    }
    
    # Soil types with detailed properties
    soil_types = {
        'clay': {
            'retention': 0.85, 'drainage': 0.15, 'field_capacity': 45,
            'wilting_point': 25, 'bulk_density': 1.3
        },
        'loam': {
            'retention': 0.65, 'drainage': 0.50, 'field_capacity': 35,
            'wilting_point': 15, 'bulk_density': 1.4
        },
        'sandy_loam': {
            'retention': 0.45, 'drainage': 0.70, 'field_capacity': 25,
            'wilting_point': 10, 'bulk_density': 1.5
        },
        'sandy': {
            'retention': 0.25, 'drainage': 0.85, 'field_capacity': 15,
            'wilting_point': 5, 'bulk_density': 1.6
        },
        'silt': {
            'retention': 0.75, 'drainage': 0.30, 'field_capacity': 40,
            'wilting_point': 20, 'bulk_density': 1.35
        }
    }
    
    # Climate zones
    climate_zones = {
        'arid': {'temp_range': (20, 45), 'humidity_range': (10, 40), 'rainfall_range': (0, 10)},
        'semi_arid': {'temp_range': (15, 40), 'humidity_range': (20, 60), 'rainfall_range': (0, 20)},
        'temperate': {'temp_range': (10, 30), 'humidity_range': (40, 80), 'rainfall_range': (0, 30)},
        'tropical': {'temp_range': (20, 35), 'humidity_range': (60, 95), 'rainfall_range': (0, 50)},
        'subtropical': {'temp_range': (15, 35), 'humidity_range': (50, 85), 'rainfall_range': (0, 40)}
    }
    
    # Generate comprehensive dataset
    n_samples = 8000
    data = []
    
    print("🔄 Generating comprehensive irrigation dataset...")
    
    for i in range(n_samples):
        if i % 1000 == 0:
            print(f"Generated {i}/{n_samples} samples...")
            
        # Random selections
        crop = np.random.choice(list(crops.keys()))
        soil = np.random.choice(list(soil_types.keys()))
        climate = np.random.choice(list(climate_zones.keys()))
        
        # Environmental factors based on climate zone
        temp_range = climate_zones[climate]['temp_range']
        humidity_range = climate_zones[climate]['humidity_range']
        rainfall_range = climate_zones[climate]['rainfall_range']
        
        temperature = np.random.uniform(temp_range[0], temp_range[1])
        humidity = np.random.uniform(humidity_range[0], humidity_range[1])
        rainfall = np.random.exponential(np.mean(rainfall_range))
        wind_speed = np.random.gamma(2, 2)
        solar_radiation = np.random.normal(20, 5)  # MJ/m²/day
        
        # Soil conditions
        field_capacity = soil_types[soil]['field_capacity']
        wilting_point = soil_types[soil]['wilting_point']
        soil_moisture = np.random.uniform(wilting_point, field_capacity + 10)
        soil_ph = np.random.normal(6.5, 1.2)
        soil_ec = np.random.normal(1.5, 0.8)  # Electrical conductivity
        
        # Crop stage and development
        crop_days = np.random.randint(1, crops[crop]['growth_days'])
        growth_stage = min(3, int(crop_days / (crops[crop]['growth_days'] / 4)))
        
        # Calculate crop coefficient (Kc) based on growth stage
        kc_values = crops[crop]['kc_values']
        if growth_stage == 0:
            kc = kc_values[0]
        elif growth_stage == 1:
            kc = kc_values[1]
        else:
            kc = kc_values[2]
        
        # Calculate reference evapotranspiration (ET0) using simplified Penman equation
        et0 = 0.0023 * (temperature + 17.8) * np.sqrt(abs(temperature - humidity)) * (solar_radiation * 0.408)
        
        # Calculate crop evapotranspiration (ETc)
        etc = et0 * kc
        
        # Effective rainfall (not all rainfall is useful)
        effective_rainfall = rainfall * 0.8 if rainfall < 10 else rainfall * 0.6
        
        # Calculate irrigation requirement
        irrigation_requirement = max(0, etc - effective_rainfall)
        
        # Adjust for soil moisture deficit
        available_water = soil_moisture - wilting_point
        max_available_water = field_capacity - wilting_point
        moisture_stress = 1 - (available_water / max_available_water)
        
        if moisture_stress > 0.5:  # Soil is getting dry
            irrigation_requirement *= (1 + moisture_stress)
        
        # Apply efficiency factors
        irrigation_efficiency = 0.85  # Drip irrigation efficiency
        final_irrigation = irrigation_requirement / irrigation_efficiency
        
        # Determine irrigation decision
        irrigation_needed = 1 if (
            soil_moisture < (wilting_point + 0.6 * max_available_water) or
            final_irrigation > 5 or
            moisture_stress > 0.4
        ) and rainfall < 5 else 0
        
        # Add some realistic constraints
        if temperature < 5 or temperature > 50:  # Extreme temperatures
            irrigation_needed = 0
            final_irrigation = 0
        
        data.append({
            'crop_type': crop,
            'soil_type': soil,
            'climate_zone': climate,
            'temperature': round(temperature, 1),
            'humidity': round(humidity, 1),
            'rainfall': round(rainfall, 1),
            'wind_speed': round(wind_speed, 1),
            'solar_radiation': round(solar_radiation, 1),
            'soil_moisture': round(soil_moisture, 1),
            'soil_ph': round(soil_ph, 1),
            'soil_ec': round(soil_ec, 1),
            'crop_days': crop_days,
            'growth_stage': growth_stage,
            'crop_coefficient': round(kc, 2),
            'et0': round(et0, 2),
            'etc': round(etc, 2),
            'effective_rainfall': round(effective_rainfall, 1),
            'moisture_stress': round(moisture_stress, 2),
            'irrigation_amount_mm': round(final_irrigation, 1),
            'irrigation_needed': irrigation_needed,
            'field_capacity': field_capacity,
            'wilting_point': wilting_point
        })
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Save to CSV
    datasets_dir = Path("datasets")
    datasets_dir.mkdir(exist_ok=True)
    irrigation_file = datasets_dir / "comprehensive_irrigation_dataset.csv"
    df.to_csv(irrigation_file, index=False)
    
    print(f"\n📊 Created Comprehensive Irrigation Dataset: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"Sample data:\n{df.head()}")
    print(f"Irrigation needed distribution:\n{df['irrigation_needed'].value_counts()}")
    print(f"Crop distribution:\n{df['crop_type'].value_counts()}")
    print(f"Climate zone distribution:\n{df['climate_zone'].value_counts()}")
    
    return df

if __name__ == "__main__":
    print("🌾 Irrigation Dataset Creator")
    print("=" * 50)
    
    # Download crop recommendation dataset
    crop_df = download_crop_recommendation_dataset()
    
    # Create comprehensive irrigation dataset
    irrigation_df = create_comprehensive_irrigation_dataset()
    
    print("\n✅ Dataset creation complete!")
    print("Available datasets:")
    print("- datasets/crop_recommendation.csv")
    print("- datasets/comprehensive_irrigation_dataset.csv")
