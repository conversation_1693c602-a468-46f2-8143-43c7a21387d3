<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Irrigation Predictions - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .prediction-table {
            font-size: 14px;
        }
        .badge-yes {
            background-color: #dc3545 !important;
        }
        .badge-no {
            background-color: #28a745 !important;
        }
        .confidence-high {
            background-color: #28a745 !important;
        }
        .confidence-medium {
            background-color: #ffc107 !important;
            color: #000 !important;
        }
        .confidence-low {
            background-color: #dc3545 !important;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">
                <i class="fas fa-seedling me-2"></i>AgroPro
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('irrigation_management') }}">
                    <i class="fas fa-tint me-1"></i>Irrigation Form
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="text-center">
                    <h1 class="display-5 fw-bold text-success">
                        <i class="fas fa-table me-3"></i>Real-time Irrigation Predictions
                    </h1>
                    <p class="lead text-muted">AI-powered irrigation recommendations based on trained ML models</p>
                    <div class="badge bg-primary fs-6">
                        <i class="fas fa-robot me-1"></i>Powered by Machine Learning
                    </div>
                </div>
            </div>
        </div>

        <!-- Predictions Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>Current Irrigation Predictions
                            <small class="float-end">
                                <i class="fas fa-clock me-1"></i>Real-time Data
                            </small>
                        </h4>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover prediction-table mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th><i class="fas fa-seedling me-1"></i>Crop</th>
                                        <th><i class="fas fa-mountain me-1"></i>Soil Type</th>
                                        <th><i class="fas fa-map-marker-alt me-1"></i>Location</th>
                                        <th><i class="fas fa-tint me-1"></i>Irrigation Needed</th>
                                        <th><i class="fas fa-droplet me-1"></i>Amount</th>
                                        <th><i class="fas fa-percentage me-1"></i>Probability</th>
                                        <th><i class="fas fa-star me-1"></i>Confidence</th>
                                        <th><i class="fas fa-calendar me-1"></i>Schedule</th>
                                        <th><i class="fas fa-tools me-1"></i>Method</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prediction in predictions %}
                                    <tr>
                                        <td>
                                            <span class="fw-bold text-success">{{ prediction.crop_type }}</span>
                                        </td>
                                        <td>{{ prediction.soil_type }}</td>
                                        <td>
                                            <i class="fas fa-map-pin text-danger me-1"></i>{{ prediction.location }}
                                        </td>
                                        <td>
                                            {% if prediction.irrigation_needed == 'Yes' %}
                                                <span class="badge badge-yes">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>{{ prediction.irrigation_needed }}
                                                </span>
                                            {% else %}
                                                <span class="badge badge-no">
                                                    <i class="fas fa-check-circle me-1"></i>{{ prediction.irrigation_needed }}
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="fw-bold text-primary">{{ prediction.irrigation_amount }}</span>
                                        </td>
                                        <td>
                                            <span class="fw-bold">{{ prediction.probability }}</span>
                                        </td>
                                        <td>
                                            <span class="badge confidence-{{ prediction.confidence.lower() }}">
                                                {{ prediction.confidence }}
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ prediction.schedule }}</small>
                                        </td>
                                        <td>
                                            <small class="text-info">{{ prediction.method }}</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-center border-success">
                    <div class="card-body">
                        <h5 class="card-title text-success">
                            {{ predictions|selectattr('irrigation_needed', 'equalto', 'Yes')|list|length }}
                        </h5>
                        <p class="card-text">Crops Need Irrigation</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-info">
                    <div class="card-body">
                        <h5 class="card-title text-info">
                            {{ predictions|selectattr('irrigation_needed', 'equalto', 'No')|list|length }}
                        </h5>
                        <p class="card-text">Crops Don't Need Irrigation</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-warning">
                    <div class="card-body">
                        <h5 class="card-title text-warning">
                            {{ predictions|selectattr('confidence', 'equalto', 'High')|list|length }}
                        </h5>
                        <p class="card-text">High Confidence Predictions</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-primary">
                    <div class="card-body">
                        <h5 class="card-title text-primary">{{ predictions|length }}</h5>
                        <p class="card-text">Total Predictions</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Legend -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Legend</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Irrigation Status:</h6>
                                <span class="badge badge-yes me-2">Yes - Irrigation Required</span>
                                <span class="badge badge-no">No - No Irrigation Needed</span>
                            </div>
                            <div class="col-md-6">
                                <h6>Confidence Levels:</h6>
                                <span class="badge confidence-high me-2">High</span>
                                <span class="badge confidence-medium me-2">Medium</span>
                                <span class="badge confidence-low">Low</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="btn btn-success btn-lg refresh-btn" onclick="location.reload()">
        <i class="fas fa-sync-alt me-2"></i>Refresh Data
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Auto-refresh every 30 seconds -->
    <script>
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
