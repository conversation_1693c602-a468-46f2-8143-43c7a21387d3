<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgroPro - Login</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-image: linear-gradient(rgba(44, 90, 39, 0.4), rgba(74, 124, 89, 0.4)), 
                              url('/static/images/Background5 (2).jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        /* Clean and minimal design */

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 25px;
            width: 100%;
            max-width: 320px;
        }

        .header-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .app-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c5a27;
            margin-bottom: 5px;
        }

        .app-subtitle {
            color: #666;
            font-size: 0.8rem;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
            font-size: 0.8rem;
        }

        .input-wrapper {
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 10px 14px 10px 35px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.85rem;
            transition: border-color 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            outline: none;
        }

        .form-control:focus {
            border-color: #2c5a27;
            background: white;
            box-shadow: 0 0 0 2px rgba(44, 90, 39, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 0.9rem;
            z-index: 2;
        }

        .btn-login {
            width: 100%;
            padding: 10px;
            background: #2c5a27;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .btn-login:hover {
            background: #1e3f1b;
        }

        .form-footer {
            text-align: center;
            margin-top: 15px;
        }

        .signup-link {
            color: #2c5a27;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.8rem;
            transition: color 0.3s ease;
        }

        .signup-link:hover {
            color: #1e3f1b;
            text-decoration: underline;
        }

        .alert {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 12px;
            border-left: 3px solid;
            font-size: 0.8rem;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        /* Removed floating shapes - keeping it clean and simple */

        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .login-container {
                max-width: 300px;
                padding: 20px 18px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .login-container {
                margin: 0;
                padding: 18px 15px;
                max-width: 100%;
            }

            .app-title {
                font-size: 1.2rem;
            }

            .form-control {
                padding: 8px 12px 8px 30px;
                font-size: 0.8rem;
            }

            .input-icon {
                left: 10px;
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<<body>
    <div class="login-container">
        <div class="header-section">
            <h1 class="app-title">AgroPro</h1>
            <p class="app-subtitle">Smart Agriculture Solution</p>
        </div>

        {% if msg %}
        <div class="alert alert-{{ 'danger' if error_type == 'danger' else 'success' }}">
            <i class="fas fa-{{ 'exclamation-circle' if error_type == 'danger' else 'check-circle' }}"></i>
            {{ msg }}
        </div>
        {% endif %}

        <form method="post" action="{{ url_for('userlog') }}">
            <div class="form-group">
                <label for="username">Username</label>
                <div class="input-wrapper">
                    <i class="fas fa-user input-icon"></i>
                    <input type="text" class="form-control" id="username" name="name" placeholder="Enter username" required>
                </div>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <div class="input-wrapper">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter password" required>
                </div>
            </div>

            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt"></i> Sign In
            </button>
        </form>

        <div class="form-footer">
            <p>Don't have an account? <a href="{{ url_for('signup') }}" class="signup-link">Sign up here</a></p>
        </div>
    </div>

    <script>
        document.querySelector('form').addEventListener('submit', function() {
            const btn = document.querySelector('.btn-login');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
            btn.disabled = true;
        });
    </script>
</body>
</html>
