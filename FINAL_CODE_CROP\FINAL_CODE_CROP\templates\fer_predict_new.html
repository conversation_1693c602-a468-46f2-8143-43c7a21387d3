{% extends 'layout.html' %} {% block body %}

<style>
  html body {
    background-color: rgb(104, 108, 115);
  }
  
  .fertilizer-result-container {
    min-height: 90vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem 0;
  }
  
  .result-title {
    font-weight: bolder;
    text-transform: capitalize;
    color: #cfc547;
    font-size: 2.5rem;
    text-shadow: 5px 5px 15px rgb(81, 67, 21),
      -5px 5px 15px rgb(81, 67, 21),
      -5px -5px 15px rgb(81, 67, 21),
      5px -5px 15px rgb(81, 67, 21);
    margin-bottom: 2rem;
    text-align: center;
  }
  
  .profile-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 400px;
    width: 100%;
    margin: 0 auto;
  }
  
  .profile-card img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid #fff;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    margin-bottom: 1.5rem;
  }
  
  .fertilizer-name {
    font-size: 2rem;
    font-weight: bold;
    color: #fff;
    margin: 1rem 0;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
  }
  
  .fertilizer-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    backdrop-filter: blur(10px);
  }
  
  .fertilizer-info p {
    color: #fff;
    margin: 0.5rem 0;
    font-size: 0.95rem;
  }
  
  .back-button {
    position: absolute;
    top: 100px;
    left: 30px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid #cfc547;
    color: #cfc547;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
  }
  
  .back-button:hover {
    background: #cfc547;
    color: #333;
    transform: translateY(-2px);
  }
  
  @media (max-width: 768px) {
    .result-title {
      font-size: 2rem;
      padding: 0 1rem;
    }
    
    .profile-card {
      margin: 0 1rem;
    }
    
    .profile-card img {
      width: 100px;
      height: 100px;
    }
    
    .fertilizer-name {
      font-size: 1.5rem;
    }
    
    .back-button {
      position: relative;
      top: auto;
      left: auto;
      margin-bottom: 2rem;
    }
  }
</style>

<div class="fertilizer-result-container">
  <a href="{{ url_for('fertilizer') }}" class="back-button">← Back to Form</a>
  
  <div class="container">
    <h1 class="result-title">
      The fertilizer you should use is
    </h1>
    
    <div class="profile-card">
      <img src="../static/images/farmer1.jpg" alt="Farmer" />
      <h1 class="fertilizer-name">{{ res }}</h1>
      
      <div class="fertilizer-info">
        <p><strong>Recommended Fertilizer:</strong> {{ res }}</p>
        <p><small>Apply as per soil test recommendations and crop requirements</small></p>
        <p><small>Consult with local agricultural experts for best results</small></p>
      </div>
    </div>
  </div>
</div>

{% endblock %}
