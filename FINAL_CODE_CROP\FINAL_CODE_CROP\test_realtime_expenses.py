"""
Test script to demonstrate real-time expense management functionality
"""

import socketio
import time
import random
from datetime import datetime, timedelta

# Create a Socket.IO client
sio = socketio.Client()

# Sample expense data for testing
sample_expenses = [
    {
        'date': (datetime.now() - timedelta(days=random.randint(0, 30))).strftime('%Y-%m-%d'),
        'category': 'Seeds',
        'amount': round(random.uniform(500, 3000), 2),
        'description': 'Wheat seeds for field A'
    },
    {
        'date': (datetime.now() - timedelta(days=random.randint(0, 30))).strftime('%Y-%m-%d'),
        'category': 'Fertilizers',
        'amount': round(random.uniform(1000, 5000), 2),
        'description': 'NPK fertilizer - 10 bags'
    },
    {
        'date': (datetime.now() - timedelta(days=random.randint(0, 30))).strftime('%Y-%m-%d'),
        'category': 'Labor',
        'amount': round(random.uniform(2000, 8000), 2),
        'description': 'Field preparation work'
    },
    {
        'date': (datetime.now() - timedelta(days=random.randint(0, 30))).strftime('%Y-%m-%d'),
        'category': 'Irrigation',
        'amount': round(random.uniform(300, 2000), 2),
        'description': 'Water pump maintenance'
    },
    {
        'date': (datetime.now() - timedelta(days=random.randint(0, 30))).strftime('%Y-%m-%d'),
        'category': 'Transport',
        'amount': round(random.uniform(500, 2500), 2),
        'description': 'Crop transportation to market'
    }
]

@sio.event
def connect():
    print('✅ Connected to real-time expense system')
    print('🔄 Starting automated expense additions...')

@sio.event
def disconnect():
    print('❌ Disconnected from server')

@sio.event
def expense_added(data):
    print(f"📊 Real-time update: {data['message']}")
    print(f"💰 Dashboard updated with new totals")

@sio.event
def success(data):
    print(f"✅ Success: {data['message']}")

@sio.event
def error(data):
    print(f"❌ Error: {data['message']}")

def test_realtime_expenses():
    """Test real-time expense functionality"""
    try:
        # Connect to the server
        print("🌐 Connecting to Flask-SocketIO server...")
        sio.connect('http://127.0.0.1:5000')
        
        # Wait a moment for connection
        time.sleep(2)
        
        print("\n🧪 Testing real-time expense additions...")
        print("📱 Open http://127.0.0.1:5000/expenses in your browser to see live updates!")
        print("⏱️  Adding expenses every 5 seconds...\n")
        
        # Add expenses one by one with delays
        for i, expense in enumerate(sample_expenses):
            print(f"📝 Adding expense {i+1}/5: {expense['category']} - ₹{expense['amount']}")
            
            # Emit the expense addition
            sio.emit('add_expense_realtime', expense)
            
            # Wait before next addition
            if i < len(sample_expenses) - 1:
                print("⏳ Waiting 5 seconds before next expense...")
                time.sleep(5)
        
        print("\n🎉 All test expenses added!")
        print("📊 Check your browser - the dashboard should update in real-time!")
        print("💡 You can also test manual additions through the web form")
        
        # Keep connection alive for a bit
        print("\n⏱️  Keeping connection alive for 30 seconds...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
    finally:
        sio.disconnect()
        print("👋 Disconnected from server")

def test_realtime_deletion():
    """Test real-time expense deletion"""
    try:
        print("\n🗑️  Testing real-time deletion...")
        sio.connect('http://127.0.0.1:5000')
        
        # Get a sample expense ID (you'd normally get this from the database)
        # For demo purposes, we'll use a placeholder
        print("💡 To test deletion, use the delete buttons in the web interface")
        print("🌐 Open http://127.0.0.1:5000/view_expenses to test real-time deletion")
        
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error during deletion test: {e}")
    finally:
        sio.disconnect()

if __name__ == "__main__":
    print("🌾 Real-time Expense Management Test")
    print("=" * 50)
    print("📋 This script will demonstrate:")
    print("   1. Real-time expense additions")
    print("   2. Live dashboard updates")
    print("   3. WebSocket communication")
    print("   4. Multi-user synchronization")
    print("\n🚀 Starting test...")
    
    # Test real-time expense additions
    test_realtime_expenses()
    
    print("\n" + "=" * 50)
    print("✅ Real-time testing completed!")
    print("💡 Key features demonstrated:")
    print("   ✓ Instant expense additions")
    print("   ✓ Live dashboard updates")
    print("   ✓ Real-time notifications")
    print("   ✓ Connection status indicators")
    print("   ✓ Multi-browser synchronization")
    print("\n🎯 The expense management system is now fully real-time!")
