<!DOCTYPE html>
<html lang="en">

<head>
	<title>AgroPro - Smart Agricultural Management Platform</title>
	<link rel="shortcut icon" href="/static/images/favicon.ico"/>

	<!-- Meta Tags -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta charset="utf-8">
	<meta name="description" content="AgroPro - Advanced Agricultural Management Platform with AI-powered crop recommendations, satellite monitoring, and precision farming tools">
	<meta name="keywords" content="Agriculture, Smart Farming, Crop Management, AI Agriculture, Precision Farming, Satellite Monitoring, Farm Management">
	<meta name="author" content="AgroPro Team">

	<!-- Bootstrap 5 CSS -->
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
	<!-- Font Awesome -->
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
	<!-- Google Fonts -->
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

	<!-- Custom Styles -->
	<style>
		:root {
			--primary-color: #5D4037;
			--secondary-color: #6D4C41;
			--accent-color: #4E342E;
			--success-color: #28a745;
			--warning-color: #ffc107;
			--danger-color: #dc3545;
			--info-color: #17a2b8;
			--light-color: #f8f9fa;
			--dark-color: #343a40;
			--gradient-primary: linear-gradient(135deg, #5D4037 0%, #6D4C41 100%);
			--gradient-secondary: linear-gradient(135deg, #6D4C41 0%, #8D6E63 100%);
			--shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
			--shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
			--shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
		}

		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			font-family: 'Inter', sans-serif;
			line-height: 1.6;
			color: var(--dark-color);
			background: url('/static/images/Background4.jpg');
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;
			background-attachment: scroll;
			min-height: 100vh;
		}

		h1, h2, h3, h4, h5, h6 {
			font-family: 'Poppins', sans-serif;
			font-weight: 600;
		}

		/* Modern Navigation Styles */
		.navbar-modern {
			background-color: #BDB76B;
			width: 100%;
			height: 70px;
			display: flex;
			align-items: center;
			padding: 0 1rem;
			transition: all 0.3s ease;
			min-height: 70px;
			border-bottom: 1px solid #E0E0E0;
		}

		.navbar-modern.scrolled {
			height: 60px;
			min-height: 60px;
			background-color: #BDB76B;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		}

		.navbar-brand-modern {
			font-family: 'Poppins', sans-serif;
			font-weight: 700;
			font-size: 1.5rem;
			color: #5D4037 !important;
			text-decoration: none;
			display: flex;
			align-items: center;
			gap: 0.5rem;
		}

		.navbar-brand-modern:hover {
			color: #4E342E !important;
		}

		.navbar-nav-modern .nav-link {
			color: #5D4037 !important;
			font-weight: 500;
			padding: 0.5rem 1rem !important;
			margin: 0 0.1rem;
			border-radius: 6px;
			transition: all 0.3s ease;
			font-size: 0.9rem;
		}

		.navbar-nav-modern .nav-link:hover {
			background-color: rgba(189, 183, 107, 0.3);
			color: #4E342E !important;
		}

		.navbar-nav-modern .nav-link.active {
			background-color: rgba(189, 183, 107, 0.4);
			color: #4E342E !important;
		}

		/* Dropdown Styles */
		.dropdown-menu-modern {
			background: #FFFFFF;
			border: 1px solid #E0E0E0;
			border-radius: 8px;
			padding: 0.5rem;
			margin-top: 0.25rem;
			min-width: 200px;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
		}

		.dropdown-header {
			font-weight: 600;
			font-size: 0.8rem;
			padding: 0.25rem 0.75rem;
			color: #5D4037;
		}

		.dropdown-item-modern {
			padding: 0.5rem 0.75rem;
			border-radius: 4px;
			transition: background-color 0.2s ease;
			display: flex;
			align-items: center;
			gap: 0.5rem;
			font-size: 0.85rem;
			color: #333;
		}

		.dropdown-item-modern:hover {
			background-color: #BDB76B;
			color: #5D4037;
		}

		/* Mobile Navigation */
		.navbar-toggler-modern {
			border: none;
			padding: 0.25rem 0.5rem;
		}

		.navbar-toggler-modern:focus {
			box-shadow: none;
		}

		.navbar-toggler-icon-modern {
			width: 24px;
			height: 2px;
			background-color: #5D4037;
			border-radius: 2px;
			position: relative;
		}

		.navbar-toggler-icon-modern::before,
		.navbar-toggler-icon-modern::after {
			content: '';
			position: absolute;
			width: 24px;
			height: 2px;
			background-color: #5D4037;
			border-radius: 2px;
		}

		.navbar-toggler-icon-modern::before {
			top: -8px;
		}

		.navbar-toggler-icon-modern::after {
			bottom: -8px;
		}		/* Responsive Design */
		@media (min-width: 992px) {
			/* Desktop navbar - 70px height */
			.navbar-modern {
				height: 70px;
				min-height: 70px;
			}
			
			.navbar-modern.scrolled {
				height: 60px;
				min-height: 60px;
			}
		}
		
		@media (max-width: 991.98px) {
			/* Mobile/Tablet navbar */
			.navbar-modern {
				height: 60px;
				min-height: 60px;
			}
			
			.navbar-nav-modern {
				background: rgba(255, 255, 255, 0.98);
				border-radius: 8px;
				padding: 1rem;
				margin-top: 0.5rem;
			}
			
			.navbar-nav-modern .nav-link {
				color: var(--dark-color) !important;
				margin: 0.1rem 0;
				padding: 0.75rem 1rem !important;
			}
			
			.navbar-nav-modern .nav-link:hover {
				background-color: var(--light-color);
				color: var(--primary-color) !important;
			}
			
			.dropdown-menu-modern {
				position: static !important;
				border: 1px solid #e9ecef;
				margin: 0.5rem 0;
			}
			
			.navbar-brand-modern {
				font-size: 1.3rem;
			}
		}
	</style>
	<!-- Bootstrap 5 JS -->
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
	<!-- jQuery -->
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<!-- Chart.js for analytics -->
	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
	<!-- Custom Scripts -->
	<script type="text/JavaScript" src="/static/scripts/cities.js"></script>

	<!-- Additional Styles -->
	<style>
		/* Content Area Styles */
		.content-wrapper {
			min-height: calc(100vh - 70px);
			padding-top: 90px;
			padding-left: 15px;
			padding-right: 15px;
		}

		/* Card Styles */
		.card-modern {
			border: none;
			border-radius: 16px;
			box-shadow: var(--shadow-sm);
			transition: all 0.3s ease;
			overflow: hidden;
		}

		.card-modern:hover {
			box-shadow: var(--shadow-md);
			transform: translateY(-4px);
		}

		.card-header-modern {
			background: var(--gradient-primary);
			color: #ffffff;
			border: none;
			padding: 1.5rem;
			font-weight: 600;
		}

		/* Button Styles */
		.btn-modern {
			border-radius: 12px;
			padding: 0.75rem 1.5rem;
			font-weight: 500;
			transition: all 0.3s ease;
			border: none;
		}

		.btn-primary-modern {
			background: var(--gradient-primary);
			color: #ffffff;
		}

		.btn-primary-modern:hover {
			background: var(--gradient-secondary);
			transform: translateY(-2px);
			box-shadow: var(--shadow-md);
		}

		/* Form Styles */
		.form-control-modern {
			border-radius: 12px;
			border: 2px solid #e9ecef;
			padding: 0.75rem 1rem;
			transition: all 0.3s ease;
		}

		.form-control-modern:focus {
			border-color: var(--primary-color);
			box-shadow: 0 0 0 0.2rem rgba(45, 90, 39, 0.25);
		}

		/* Animation Classes */
		.fade-in {
			animation: fadeIn 0.6s ease-in;
		}

		@keyframes fadeIn {
			from { opacity: 0; transform: translateY(20px); }
			to { opacity: 1; transform: translateY(0); }
		}

		.slide-in {
			animation: slideIn 0.6s ease-out;
		}

		@keyframes slideIn {
			from { opacity: 0; transform: translateX(-30px); }
			to { opacity: 1; transform: translateX(0); }
		}
	</style>
</head>

<body>
	<!-- Modern Navigation -->
	<nav class="navbar navbar-expand-lg navbar-modern fixed-top" id="mainNavbar">
		<div class="container-fluid px-4">
			<a class="navbar-brand-modern" href="/index.html">
				<i class="fas fa-seedling me-2"></i>
				<span class="fw-bold">AgroPro</span>
				<span class="text-light opacity-75 ms-1">Platform</span>
			</a>

			<button class="navbar-toggler navbar-toggler-modern" type="button" 
					data-bs-toggle="collapse" data-bs-target="#navbarNav" 
					aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
				<span class="navbar-toggler-icon-modern"></span>
			</button>

			<div class="collapse navbar-collapse" id="navbarNav">
				<ul class="navbar-nav navbar-nav-modern ms-auto align-items-center">
					
					<!-- Home -->
					<li class="nav-item">
						<a class="nav-link active" href="/index.html">
							<i class="fas fa-home me-2"></i>Home
						</a>
					</li>

					<!-- Crop Management Dropdown -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="cropDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-seedling me-2"></i>Crop Management
						</a>
						<ul class="dropdown-menu dropdown-menu-modern shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									<i class="fas fa-brain me-2"></i>AI-Powered Tools
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="/crop-recommend">
								<i class="fas fa-seedling me-2 text-success"></i>Crop Recommendation</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/disease_prediction">
								<i class="fas fa-bug me-2 text-warning"></i>Disease Detection</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/crop_fer">
								<i class="fas fa-flask me-2 text-info"></i>Fertilizer Advisor</a></li>
							<li><hr class="dropdown-divider"></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/pest_management">
								<i class="fas fa-shield-alt me-2 text-danger"></i>Pest Management</a></li>
						</ul>
					</li>

					<!-- Farm Tools Dropdown -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="farmDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-tractor me-2"></i>Farm Tools
						</a>
						<ul class="dropdown-menu dropdown-menu-modern shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									<i class="fas fa-tools me-2"></i>Management Tools
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="/irrigation">
								<i class="fas fa-tint me-2 text-primary"></i>Irrigation Management</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/expenses">
								<i class="fas fa-calculator me-2 text-success"></i>Expense Management</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/farm_calendar">
								<i class="fas fa-calendar-alt me-2 text-warning"></i>Farm Calendar</a></li>
							<li><hr class="dropdown-divider"></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/market_linkage">
								<i class="fas fa-handshake me-2 text-info"></i>Market Linkage</a></li>
						</ul>
					</li>

					<!-- Analytics Dropdown -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="analyticsDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-chart-line me-2"></i>Analytics
						</a>
						<ul class="dropdown-menu dropdown-menu-modern shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									<i class="fas fa-chart-bar me-2"></i>Data & Insights
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="/analytics">
								<i class="fas fa-chart-bar me-2 text-success"></i>Yield Analytics</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/yield">
								<i class="fas fa-chart-line me-2 text-primary"></i>Yield Prediction</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/crop_price">
								<i class="fas fa-rupee-sign me-2 text-warning"></i>Price Prediction</a></li>
							<li><hr class="dropdown-divider"></li>
							<li>
								<h6 class="dropdown-header text-secondary">
									<i class="fas fa-satellite me-2"></i>Advanced Analytics
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="/satellite_health">
								<i class="fas fa-satellite me-2 text-info"></i>Satellite Health</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/satellite_data">
								<i class="fas fa-globe me-2 text-success"></i>Precision Agriculture</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/market_prices">
								<i class="fas fa-coins me-2 text-warning"></i>Market Prices</a></li>
						</ul>
					</li>

					<!-- Resources Dropdown -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="resourcesDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-book me-2"></i>Resources
						</a>
						<ul class="dropdown-menu dropdown-menu-modern shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									<i class="fas fa-info-circle me-2"></i>Knowledge Base
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="/sustainable_tips">
								<i class="fas fa-leaf me-2 text-success"></i>Sustainable Tips</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/government_schemes">
								<i class="fas fa-university me-2 text-primary"></i>Government Schemes</a></li>
							<li><hr class="dropdown-divider"></li>
							<li>
								<h6 class="dropdown-header text-secondary">
									<i class="fas fa-users me-2"></i>Community
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="/community_forum">
								<i class="fas fa-users me-2 text-info"></i>Community Forum</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/notifications">
								<i class="fas fa-bell me-2 text-warning"></i>Notifications</a></li>
						</ul>
					</li>

					<!-- Weather -->
					<li class="nav-item">
						<a class="nav-link" href="/weather">
							<i class="fas fa-cloud-sun me-2"></i>Weather
						</a>
					</li>

					<!-- User Actions -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-user-circle me-2"></i>Account
						</a>
						<ul class="dropdown-menu dropdown-menu-modern dropdown-menu-end shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									<i class="fas fa-user me-2"></i>User Options
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="#">
								<i class="fas fa-user-edit me-2 text-primary"></i>Profile Settings</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="/index.html">
								<i class="fas fa-tachometer-alt me-2 text-success"></i>Dashboard</a></li>
							<li><hr class="dropdown-divider"></li>
							<li><a class="dropdown-item dropdown-item-modern text-danger" href="/logout">
								<i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
						</ul>
					</li>
				</ul>
			</div>
		</div>
	</nav>

	<!-- Content Wrapper -->
	<div class="content-wrapper">
		
<style>
  body {
    background-image: linear-gradient(rgba(44, 90, 39, 0.2), rgba(74, 124, 89, 0.2)), 
                      url('/static/images/Background4.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    min-height: 100vh;
  }
  
  .result-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }
  
  .page-header {
    background: rgba(44, 90, 39, 0.95);
    color: white;
    padding: 20px 30px;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(44, 90, 39, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .page-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 5px;
  }
  
  .page-subtitle {
    font-size: 0.95rem;
    opacity: 0.9;
  }
  
  .result-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
  }
  
  .recommendation-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border-left: 5px solid #2c5a27;
  }
  
  .crop-image-container {
    width: 200px;
    height: 150px;
    margin: 0 auto 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #4a7c59 0%, #2c5a27 100%);
    position: relative;
  }
  
  .crop-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .crop-image:hover {
    transform: scale(1.05);
  }
  
  .crop-image-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
    background: linear-gradient(135deg, #4a7c59 0%, #2c5a27 100%);
  }
  
  .crop-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin: 20px 0;
    text-align: left;
  }
  
  .crop-info-item {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    border-left: 3px solid #2c5a27;
  }
  
  .crop-info-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 3px;
  }
  
  .crop-info-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
  }
  
  .crop-icon {
    font-size: 4rem;
    margin-bottom: 15px;
    display: block;
  }
  
  .crop-name {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2c5a27;
    margin-bottom: 20px;
    text-transform: capitalize;
  }
  
  .confidence-section {
    margin: 25px 0;
  }
  
  .confidence-label {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 10px;
    font-weight: 500;
  }
  
  .confidence-bar-container {
    background: #f8f9fa;
    height: 35px;
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid #e9ecef;
    position: relative;
  }
  
  .confidence-bar {
    height: 100%;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    transition: width 2s ease-in-out;
    position: relative;
    overflow: hidden;
  }
  
  .confidence-high { background: linear-gradient(90deg, #28a745, #20c997); }
  .confidence-medium { background: linear-gradient(90deg, #ffc107, #fd7e14); }
  .confidence-low { background: linear-gradient(90deg, #dc3545, #e74c3c); }
  
  .recommendation-status {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 600;
    margin-top: 15px;
  }
  
  .status-high { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
  .status-medium { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
  .status-low { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
  
  .analysis-panel {
    background: rgba(248, 249, 250, 0.95);
    border-radius: 12px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  .panel-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 25px;
  }
  
  .panel-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
  }
  
  .panel-icon {
    color: #2c5a27;
    font-size: 1.5rem;
  }
  
  .conditions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  .condition-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #2c5a27;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
  }
  
  .condition-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .condition-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .condition-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
  }
  
  .condition-unit {
    font-size: 1rem;
    color: #6c757d;
    font-weight: 400;
  }
  
  .actions-section {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    margin-top: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
  }
  
  .actions-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 25px;
  }
  
  .btn-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .btn {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-width: 160px;
    justify-content: center;
  }
  
  .btn-primary {
    background: #2c5a27;
    color: white;
    box-shadow: 0 4px 12px rgba(44, 90, 39, 0.3);
  }
  
  .btn-primary:hover {
    background: #1e3f1b;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(44, 90, 39, 0.4);
    color: white;
    text-decoration: none;
  }
  
  .btn-secondary {
    background: #6c757d;
    color: white;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
  }
  
  .btn-secondary:hover {
    background: #545b62;
    color: white;
    text-decoration: none;
  }
  
  .btn-outline {
    background: transparent;
    color: #2c5a27;
    border: 2px solid #2c5a27;
  }
  
  .btn-outline:hover {
    background: #2c5a27;
    color: white;
    text-decoration: none;
  }
  
  .analysis-date {
    text-align: center;
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
  }
  
  @media (max-width: 768px) {
    .result-layout {
      grid-template-columns: 1fr;
    }
    .conditions-grid {
      grid-template-columns: 1fr;
    }
    .btn-group {
      flex-direction: column;
      align-items: center;
    }
    .page-title {
      font-size: 1.5rem;
    }
    .crop-name {
      font-size: 1.6rem;
    }
    .page-header {
      padding: 15px 20px;
      margin-bottom: 15px;
    }
  }
</style>

<div class="result-container">
  <div class="page-header">
    <h1 class="page-title">Crop Recommendation Analysis</h1>
    <p class="page-subtitle">AI-Powered Agricultural Decision Support System</p>
  </div>

  <div class="result-layout">
    <!-- Recommendation Card -->
    <div class="recommendation-card">
      <!-- Crop Image -->
      <div class="crop-image-container">
        
          <img src="/static/images/Rice.jpg" alt="Rice" class="crop-image"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
          <div class="crop-image-fallback" style="display: none;">
            🌾
          </div>
        
      </div>
      
      <h2 class="crop-name">Rice</h2>
      
      
      <p style="color: #6c757d; font-size: 0.95rem; margin: 15px 0; line-height: 1.5;">
        Rice is one of the most important staple crops worldwide, requiring abundant water and warm climate.
      </p>
      
      
      <!-- Crop Information Grid -->
      
      <div class="crop-info-grid">
        <div class="crop-info-item">
          <div class="crop-info-label">Season</div>
          <div class="crop-info-value">Kharif (Monsoon)</div>
        </div>
        <div class="crop-info-item">
          <div class="crop-info-label">Duration</div>
          <div class="crop-info-value">3-6 months</div>
        </div>
        <div class="crop-info-item">
          <div class="crop-info-label">Ideal Temp</div>
          <div class="crop-info-value">20-37°C</div>
        </div>
        <div class="crop-info-item">
          <div class="crop-info-label">Water Req</div>
          <div class="crop-info-value">High</div>
        </div>
      </div>
      
      
      
      <div class="confidence-section">
        <div class="confidence-label">Recommendation Confidence</div>
        <div class="confidence-bar-container">
          <div class="confidence-bar confidence-high" 
               style="width: 95.0%;">
            95.0%
          </div>
        </div>
        <div class="recommendation-status status-high">
          
            ✅ Highly Recommended - Excellent match for your conditions
          
        </div>
        
        
        
      </div>
      
    </div>

    <!-- Analysis Panel -->
    <div class="analysis-panel">
      <div class="panel-header">
        <i class="fas fa-chart-bar panel-icon"></i>
        <h3 class="panel-title">Soil Analysis Report</h3>
      </div>
      
      <div class="conditions-grid">
        <div class="condition-item">
          <div class="condition-label">Nitrogen (N)</div>
          <div class="condition-value">90.0 <span class="condition-unit">kg/ha</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Phosphorous (P)</div>
          <div class="condition-value">42.0 <span class="condition-unit">kg/ha</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Potassium (K)</div>
          <div class="condition-value">43.0 <span class="condition-unit">kg/ha</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">pH Level</div>
          <div class="condition-value">6.5</div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Temperature</div>
          <div class="condition-value">20.9 <span class="condition-unit">°C</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Humidity</div>
          <div class="condition-value">82.0 <span class="condition-unit">%</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Rainfall</div>
          <div class="condition-value">202.9 <span class="condition-unit">mm</span></div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Analysis Date</div>
          <div class="condition-value" style="font-size: 1.1rem;">Today</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions Section -->
  <div class="actions-section">
    <h3 class="actions-title">Available Actions</h3>
    <div class="btn-group">
      <a href="/crop-recommend" class="btn btn-primary">
        <i class="fas fa-redo"></i> New Analysis
      </a>
      <a href="/index.html" class="btn btn-secondary">
        <i class="fas fa-home"></i> Dashboard
      </a>
      <a href="#" class="btn btn-outline" onclick="window.print()">
        <i class="fas fa-print"></i> Print Report
      </a>
      <a href="#" class="btn btn-outline" onclick="downloadReport()">
        <i class="fas fa-download"></i> Download PDF
      </a>
    </div>
    
    <div class="analysis-date">
      Report generated on Today by AgroPro AI System
    </div>
  </div>
</div>

<script>
// Animate confidence bar on page load
document.addEventListener('DOMContentLoaded', function() {
  const confidenceBar = document.querySelector('.confidence-bar');
  if (confidenceBar) {
    const targetWidth = confidenceBar.style.width;
    confidenceBar.style.width = '0%';
    setTimeout(() => {
      confidenceBar.style.width = targetWidth;
    }, 500);
  }
});

function downloadReport() {
  // Simulate PDF download
  alert('PDF download feature coming soon! For now, you can use the print option.');
}
</script>


		
	</div>

	<!-- Modern Footer -->
	<footer class="py-4 mt-5" style="background-color: #BDB76B;">
		<div class="container">
			<div class="row">
				<div class="col-md-6">
					<h5 class="text-dark">
						<i class="fas fa-seedling me-2"></i>AgroPro Platform
					</h5>
					<p class="mb-0 text-dark">Empowering farmers with AI-driven agricultural solutions</p>
				</div>
				<div class="col-md-6 text-md-end">
					<p class="mb-0 text-dark">&copy; 2024 AgroPro. All rights reserved.</p>
					<small class="text-dark opacity-75">Smart Agriculture • Precision Farming • Sustainable Growth</small>
				</div>
			</div>
		</div>
	</footer>

	<!-- Custom JavaScript -->
	<script>
		// Navbar scroll effect
		window.addEventListener('scroll', function() {
			const navbar = document.getElementById('mainNavbar');
			if (window.scrollY > 50) {
				navbar.classList.add('scrolled');
			} else {
				navbar.classList.remove('scrolled');
			}
		});

		// Add fade-in animation to content
		document.addEventListener('DOMContentLoaded', function() {
			const contentWrapper = document.querySelector('.content-wrapper');
			if (contentWrapper) {
				contentWrapper.classList.add('fade-in');
			}
		});

		// Smooth scrolling for anchor links
		document.querySelectorAll('a[href^="#"]').forEach(anchor => {
			anchor.addEventListener('click', function (e) {
				e.preventDefault();
				const target = document.querySelector(this.getAttribute('href'));
				if (target) {
					target.scrollIntoView({
						behavior: 'smooth',
						block: 'start'
					});
				}
			});
		});
	</script>
</body>

</html>