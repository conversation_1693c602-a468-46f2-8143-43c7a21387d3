#!/usr/bin/env python3
"""
Test if the trained irrigation models actually work and give real predictions
"""

import pandas as pd
import numpy as np
import joblib
from irrigation_model import IrrigationPredictor
from irrigation_preprocessing import IrrigationDataPreprocessor

def test_trained_models():
    """Test if trained models give real predictions"""
    print("🔍 TESTING REAL TRAINED IRRIGATION MODELS")
    print("=" * 50)
    
    try:
        # Method 1: Direct model loading
        print("\n📊 Method 1: Direct Model Loading")
        classifier = joblib.load('models/irrigation_model_classifier.pkl')
        regressor = joblib.load('models/irrigation_model_regressor.pkl')
        preprocessor = joblib.load('models/irrigation_preprocessor.pkl')
        
        print(f"✅ Classifier loaded: {type(classifier)}")
        print(f"✅ Regressor loaded: {type(regressor)}")
        print(f"✅ Preprocessor loaded: {type(preprocessor)}")
        
        # Test with sample data
        sample_data = pd.DataFrame([{
            'crop_type': 'soybean',
            'soil_type': 'sandy',
            'climate_zone': 'temperate',
            'temperature': 35.0,
            'humidity': 35.0,
            'rainfall': 3.0,
            'wind_speed': 2.0,
            'solar_radiation': 20.0,
            'soil_moisture': 20.0,
            'soil_ph': 6.5,
            'soil_ec': 1.0,
            'crop_days': 30,
            'growth_stage': 1,
            'crop_coefficient': 0.8,
            'et0': 4.0,
            'etc': 3.2,
            'effective_rainfall': 2.4,
            'moisture_stress': 0.5,
            'field_capacity': 35.0,
            'wilting_point': 15.0
        }])
        
        print(f"\n📋 Sample data shape: {sample_data.shape}")
        print(f"📋 Sample data:\n{sample_data.iloc[0].to_dict()}")
        
        # Check if preprocessor works
        if hasattr(preprocessor, 'transform'):
            try:
                X_processed = preprocessor.transform(sample_data)
                print(f"✅ Preprocessing successful: {X_processed.shape}")
                
                # Make predictions
                irrigation_needed = classifier.predict(X_processed)[0]
                irrigation_proba = classifier.predict_proba(X_processed)[0]
                irrigation_amount = regressor.predict(X_processed)[0]
                
                print(f"\n🎯 REAL MODEL PREDICTIONS:")
                print(f"   Irrigation needed: {irrigation_needed}")
                print(f"   Irrigation probabilities: {irrigation_proba}")
                print(f"   Irrigation amount: {irrigation_amount:.2f} mm")
                
                return True
                
            except Exception as pred_error:
                print(f"❌ Prediction failed: {pred_error}")
                return False
        else:
            print(f"❌ Preprocessor doesn't have transform method")
            print(f"📋 Available methods: {[m for m in dir(preprocessor) if not m.startswith('_')]}")
            return False
            
    except Exception as e:
        print(f"❌ Direct model loading failed: {e}")
    
    try:
        # Method 2: Using IrrigationPredictor class
        print("\n📊 Method 2: IrrigationPredictor Class")
        predictor = IrrigationPredictor()
        
        # Load models
        if predictor.load_models():
            print("✅ Models loaded via IrrigationPredictor")
            
            # Create preprocessor
            preprocessor = IrrigationDataPreprocessor()
            
            # Test data
            test_data = pd.DataFrame([{
                'crop_type': 'soybean',
                'soil_type': 'sandy',
                'climate_zone': 'temperate',
                'temperature': 35.0,
                'humidity': 35.0,
                'rainfall': 3.0,
                'wind_speed': 2.0,
                'solar_radiation': 20.0,
                'soil_moisture': 20.0,
                'soil_ph': 6.5,
                'soil_ec': 1.0,
                'crop_days': 30,
                'growth_stage': 1,
                'crop_coefficient': 0.8,
                'et0': 4.0,
                'etc': 3.2,
                'effective_rainfall': 2.4,
                'moisture_stress': 0.5,
                'field_capacity': 35.0,
                'wilting_point': 15.0
            }])
            
            # Preprocess
            X_processed = preprocessor.fit_transform(test_data, None)
            
            # Predict
            result = predictor.predict_irrigation(X_processed)
            
            print(f"\n🎯 IRRIGATION PREDICTOR RESULTS:")
            print(f"   Irrigation needed: {result['irrigation_needed']}")
            print(f"   Irrigation probability: {result['irrigation_probability']}")
            print(f"   Irrigation amount: {result['irrigation_amount_mm']} mm")
            
            return True
        else:
            print("❌ Failed to load models via IrrigationPredictor")
            return False
            
    except Exception as e:
        print(f"❌ IrrigationPredictor method failed: {e}")
        return False

def test_dataset_samples():
    """Test with actual dataset samples"""
    print("\n🔍 TESTING WITH DATASET SAMPLES")
    print("=" * 50)
    
    try:
        # Load the actual dataset
        dataset = pd.read_csv('datasets/comprehensive_irrigation_dataset.csv')
        print(f"✅ Dataset loaded: {dataset.shape}")
        print(f"📋 Columns: {list(dataset.columns)}")
        
        # Take a few samples
        samples = dataset.head(3)
        print(f"\n📊 Sample data from dataset:")
        for i, row in samples.iterrows():
            print(f"Sample {i+1}:")
            print(f"  Crop: {row['crop_type']}, Soil: {row['soil_type']}")
            print(f"  Temp: {row['temperature']}°C, Humidity: {row['humidity']}%")
            print(f"  Rainfall: {row['rainfall']}mm, Soil Moisture: {row['soil_moisture']}%")
            print(f"  Expected - Irrigation: {row['irrigation_needed']}, Amount: {row['irrigation_amount_mm']}mm")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Dataset loading failed: {e}")
        return False

if __name__ == '__main__':
    print("🚀 TESTING REAL IRRIGATION MODEL PREDICTIONS")
    print("=" * 60)
    
    # Test trained models
    models_work = test_trained_models()
    
    # Test dataset samples
    dataset_works = test_dataset_samples()
    
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS:")
    print(f"  Trained Models: {'✅ WORKING' if models_work else '❌ NOT WORKING'}")
    print(f"  Dataset Access: {'✅ WORKING' if dataset_works else '❌ NOT WORKING'}")
    
    if models_work:
        print("\n🎉 CONCLUSION: Trained models ARE working and giving real predictions!")
        print("💡 The issue is in the app.py integration, not the models themselves.")
    else:
        print("\n⚠️ CONCLUSION: Trained models are NOT working properly.")
        print("💡 Need to fix model loading or preprocessing issues.")
