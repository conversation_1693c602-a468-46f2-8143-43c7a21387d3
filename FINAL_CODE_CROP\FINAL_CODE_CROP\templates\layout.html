<!DOCTYPE html>
<html lang="en">

<head>
	<title>AgroPro - Smart Agricultural Management Platform</title>
	<link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}"/>

	<!-- Meta Tags -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta charset="utf-8">
	<meta name="description" content="AgroPro - Advanced Agricultural Management Platform with AI-powered crop recommendations, satellite monitoring, and precision farming tools">
	<meta name="keywords" content="Agriculture, Smart Farming, Crop Management, AI Agriculture, Precision Farming, Satellite Monitoring, Farm Management">
	<meta name="author" content="AgroPro Team">

	<!-- Bootstrap 5 CSS -->
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
	<!-- Font Awesome -->
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
	<!-- Google Fonts -->
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

	<!-- Custom Styles -->
	<style>
		:root {
			--primary-color: #5D4037;
			--secondary-color: #6D4C41;
			--accent-color: #4E342E;
			--success-color: #28a745;
			--warning-color: #ffc107;
			--danger-color: #dc3545;
			--info-color: #17a2b8;
			--light-color: #f8f9fa;
			--dark-color: #343a40;
			--gradient-primary: linear-gradient(135deg, #5D4037 0%, #6D4C41 100%);
			--gradient-secondary: linear-gradient(135deg, #6D4C41 0%, #8D6E63 100%);
			--shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
			--shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
			--shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
		}

		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			font-family: 'Inter', sans-serif;
			line-height: 1.6;
			color: var(--dark-color);
			background: #f8f9fa;
			min-height: 100vh;
		}

		/* Background image container */
		body::after {
			content: '';
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: url('{{ url_for("static", filename="images/Background4.jpg") }}') center center/cover no-repeat;
			z-index: -2;
		}

		/* Fallback background if image doesn't load */
		body::before {
			content: '';
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 50%, #e8f5e8 100%);
			z-index: -3;
		}

		h1, h2, h3, h4, h5, h6 {
			font-family: 'Poppins', sans-serif;
			font-weight: 600;
		}

		/* Modern Navigation Styles */
		.navbar-modern {
			background-color: #BDB76B;
			width: 100%;
			height: 70px;
			display: flex;
			align-items: center;
			padding: 0 1rem;
			transition: all 0.3s ease;
			min-height: 70px;
			border-bottom: 1px solid #E0E0E0;
		}

		.navbar-modern.scrolled {
			height: 60px;
			min-height: 60px;
			background-color: #BDB76B;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		}

		.navbar-brand-modern {
			font-family: 'Poppins', sans-serif;
			font-weight: 700;
			font-size: 1.5rem;
			color: #5D4037 !important;
			text-decoration: none;
			display: flex;
			align-items: center;
			gap: 0.5rem;
		}

		.navbar-brand-modern:hover {
			color: #4E342E !important;
		}

		.navbar-nav-modern .nav-link {
			color: #5D4037 !important;
			font-weight: 500;
			padding: 0.5rem 1rem !important;
			margin: 0 0.1rem;
			border-radius: 6px;
			transition: all 0.3s ease;
			font-size: 0.9rem;
		}

		.navbar-nav-modern .nav-link:hover {
			background-color: rgba(189, 183, 107, 0.3);
			color: #4E342E !important;
		}

		.navbar-nav-modern .nav-link.active {
			background-color: rgba(189, 183, 107, 0.4);
			color: #4E342E !important;
		}

		/* Dropdown Styles */
		.dropdown-menu-modern {
			background: #FFFFFF;
			border: 1px solid #E0E0E0;
			border-radius: 8px;
			padding: 0.5rem;
			margin-top: 0.25rem;
			min-width: 200px;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
		}

		.dropdown-header {
			font-weight: 600;
			font-size: 0.8rem;
			padding: 0.25rem 0.75rem;
			color: #5D4037;
		}

		.dropdown-item-modern {
			padding: 0.5rem 0.75rem;
			border-radius: 4px;
			transition: background-color 0.2s ease;
			display: flex;
			align-items: center;
			gap: 0.5rem;
			font-size: 0.85rem;
			color: #333;
		}

		.dropdown-item-modern:hover {
			background-color: #BDB76B;
			color: #5D4037;
		}

		/* Mobile Navigation */
		.navbar-toggler-modern {
			border: none;
			padding: 0.25rem 0.5rem;
		}

		.navbar-toggler-modern:focus {
			box-shadow: none;
		}

		.navbar-toggler-icon-modern {
			width: 24px;
			height: 2px;
			background-color: #5D4037;
			border-radius: 2px;
			position: relative;
		}

		.navbar-toggler-icon-modern::before,
		.navbar-toggler-icon-modern::after {
			content: '';
			position: absolute;
			width: 24px;
			height: 2px;
			background-color: #5D4037;
			border-radius: 2px;
		}

		.navbar-toggler-icon-modern::before {
			top: -8px;
		}

		.navbar-toggler-icon-modern::after {
			bottom: -8px;
		}		/* Responsive Design */
		@media (min-width: 992px) {
			/* Desktop navbar - 70px height */
			.navbar-modern {
				height: 70px;
				min-height: 70px;
			}
			
			.navbar-modern.scrolled {
				height: 60px;
				min-height: 60px;
			}
		}
		
		@media (max-width: 991.98px) {
			/* Mobile/Tablet navbar */
			.navbar-modern {
				height: 60px;
				min-height: 60px;
			}
			
			.navbar-nav-modern {
				background: rgba(255, 255, 255, 0.98);
				border-radius: 8px;
				padding: 1rem;
				margin-top: 0.5rem;
			}
			
			.navbar-nav-modern .nav-link {
				color: var(--dark-color) !important;
				margin: 0.1rem 0;
				padding: 0.75rem 1rem !important;
			}
			
			.navbar-nav-modern .nav-link:hover {
				background-color: var(--light-color);
				color: var(--primary-color) !important;
			}
			
			.dropdown-menu-modern {
				position: static !important;
				border: 1px solid #e9ecef;
				margin: 0.5rem 0;
			}
			
			.navbar-brand-modern {
				font-size: 1.3rem;
			}
		}

		/* Emergency visibility styles */
		.navbar, .btn, .form-control, 
		.card-body, .card-header, .nav-link, .dropdown-menu {
			position: relative;
			z-index: 1;
		}

		/* Ensure text is always visible with better container styling */
		.container {
			background: transparent;
			border-radius: 15px;
			margin-top: 20px;
			margin-bottom: 20px;
			padding: 25px;
		}

		/* Override any potential black backgrounds */
		.navbar-modern, .card, .btn {
			color: #333 !important;
		}

		/* Content Area Styles */
		.content-wrapper {
			min-height: calc(100vh - 70px);
			padding-top: 90px;
			padding-left: 15px;
			padding-right: 15px;
		}

		/* Card Styles */
		.card-modern {
			border: none;
			border-radius: 16px;
			box-shadow: var(--shadow-sm);
			transition: all 0.3s ease;
			overflow: hidden;
		}

		.card-modern:hover {
			box-shadow: var(--shadow-md);
			transform: translateY(-4px);
		}

		.card-header-modern {
			background: var(--gradient-primary);
			color: #ffffff;
			border: none;
			padding: 1.5rem;
			font-weight: 600;
		}

		/* Button Styles */
		.btn-modern {
			border-radius: 12px;
			padding: 0.75rem 1.5rem;
			font-weight: 500;
			transition: all 0.3s ease;
			border: none;
		}

		.btn-primary-modern {
			background: var(--gradient-primary);
			color: #ffffff;
		}

		.btn-primary-modern:hover {
			background: var(--gradient-secondary);
			transform: translateY(-2px);
			box-shadow: var(--shadow-md);
		}

		/* Form Styles */
		.form-control-modern {
			border-radius: 12px;
			border: 2px solid #e9ecef;
			padding: 0.75rem 1rem;
			transition: all 0.3s ease;
		}

		.form-control-modern:focus {
			border-color: var(--primary-color);
			box-shadow: 0 0 0 0.2rem rgba(45, 90, 39, 0.25);
		}

		/* Animation Classes */
		.fade-in {
			animation: fadeIn 0.6s ease-in;
		}

		@keyframes fadeIn {
			from { opacity: 0; transform: translateY(20px); }
			to { opacity: 1; transform: translateY(0); }
		}

		.slide-in {
			animation: slideIn 0.6s ease-out;
		}

		@keyframes slideIn {
			from { opacity: 0; transform: translateX(-30px); }
			to { opacity: 1; transform: translateX(0); }
		}
	</style>
	<!-- Bootstrap 5 JS -->
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
	<!-- jQuery -->
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<!-- Chart.js for analytics -->
	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
	<!-- Custom Scripts -->
	<script type="text/JavaScript" src="{{ url_for('static', filename='scripts/cities.js') }}"></script>

	<!-- Additional Styles -->
	<style>
		/* Content Area Styles */
		.content-wrapper {
			min-height: calc(100vh - 70px);
			padding-top: 90px;
			padding-left: 15px;
			padding-right: 15px;
		}

		/* Card Styles */
		.card-modern {
			border: none;
			border-radius: 16px;
			box-shadow: var(--shadow-sm);
			transition: all 0.3s ease;
			overflow: hidden;
		}

		.card-modern:hover {
			box-shadow: var(--shadow-md);
			transform: translateY(-4px);
		}

		.card-header-modern {
			background: var(--gradient-primary);
			color: #ffffff;
			border: none;
			padding: 1.5rem;
			font-weight: 600;
		}

		/* Button Styles */
		.btn-modern {
			border-radius: 12px;
			padding: 0.75rem 1.5rem;
			font-weight: 500;
			transition: all 0.3s ease;
			border: none;
		}

		.btn-primary-modern {
			background: var(--gradient-primary);
			color: #ffffff;
		}

		.btn-primary-modern:hover {
			background: var(--gradient-secondary);
			transform: translateY(-2px);
			box-shadow: var(--shadow-md);
		}

		/* Form Styles */
		.form-control-modern {
			border-radius: 12px;
			border: 2px solid #e9ecef;
			padding: 0.75rem 1rem;
			transition: all 0.3s ease;
		}

		.form-control-modern:focus {
			border-color: var(--primary-color);
			box-shadow: 0 0 0 0.2rem rgba(45, 90, 39, 0.25);
		}

		/* Animation Classes */
		.fade-in {
			animation: fadeIn 0.6s ease-in;
		}

		@keyframes fadeIn {
			from { opacity: 0; transform: translateY(20px); }
			to { opacity: 1; transform: translateY(0); }
		}

		.slide-in {
			animation: slideIn 0.6s ease-out;
		}

		@keyframes slideIn {
			from { opacity: 0; transform: translateX(-30px); }
			to { opacity: 1; transform: translateX(0); }
		}
	</style>
</head>

<body>
	<!-- Modern Navigation -->
	<nav class="navbar navbar-expand-lg navbar-modern fixed-top" id="mainNavbar">
		<div class="container-fluid px-4">
			<div class="d-flex align-items-center">
				<a class="navbar-brand-modern" href="{{ url_for('home') }}">
					<span class="fw-bold">AgroPro</span>
					<span class="text-light opacity-75 ms-1">Platform</span>
				</a>
			</div>

			<button class="navbar-toggler navbar-toggler-modern" type="button" 
					data-bs-toggle="collapse" data-bs-target="#navbarNav" 
					aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
				<span class="navbar-toggler-icon-modern"></span>
			</button>

			<div class="collapse navbar-collapse" id="navbarNav">
				<ul class="navbar-nav navbar-nav-modern ms-auto align-items-center">
					
					<!-- Home for all screens -->
					<li class="nav-item">
						<a class="nav-link active" href="{{ url_for('home') }}">
							Home
						</a>
					</li>

					<!-- Weather for mobile only -->
					<li class="nav-item d-lg-none">
						<a class="nav-link" href="{{ url_for('weather') }}">
							Weather
						</a>
					</li>

					<!-- Crop Management Dropdown -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="cropDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							Crop Management
						</a>
						<ul class="dropdown-menu dropdown-menu-modern shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									AI-Powered Tools
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('crop_recommend') }}">
								Crop Recommendation</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('disease_prediction') }}">
								Disease Detection</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('crop_fer') }}">
								Fertilizer Advisor</a></li>
							<li><hr class="dropdown-divider"></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('pest_management') }}">
								Pest Management</a></li>
						</ul>
					</li>

					<!-- Farm Tools Dropdown -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="farmDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							Farm Tools
						</a>
						<ul class="dropdown-menu dropdown-menu-modern shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									Management Tools
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('irrigation_management') }}">
								Irrigation Management</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('farm_expenses') }}">
								Expense Management</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('farm_calendar') }}">
								Farm Calendar</a></li>
							<li><hr class="dropdown-divider"></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('market_linkage') }}">
								Market Linkage</a></li>
						</ul>
					</li>

					<!-- Analytics Dropdown -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="analyticsDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							Analytics
						</a>
						<ul class="dropdown-menu dropdown-menu-modern shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									Data & Insights
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('yield_analytics') }}">
								Yield Analytics</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('yield_page') }}">
								Yield Prediction</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('crop_price') }}">
								Price Prediction</a></li>
							<li><hr class="dropdown-divider"></li>
							<li>
								<h6 class="dropdown-header text-secondary">
									Advanced Analytics
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('satellite_health_viewer') }}">
								Satellite Health</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('satellite_data') }}">
								Precision Agriculture</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('live_market_prices') }}">
								Market Prices</a></li>
						</ul>
					</li>

					<!-- Resources Dropdown -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="resourcesDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							Resources
						</a>
						<ul class="dropdown-menu dropdown-menu-modern shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									Knowledge Base
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('sustainable_tips') }}">
								Sustainable Tips</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('government_schemes') }}">
								Government Schemes</a></li>
							<li><hr class="dropdown-divider"></li>
							<li>
								<h6 class="dropdown-header text-secondary">
									Community
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('community_forum') }}">
								Community Forum</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('notifications') }}">
								Notifications</a></li>
						</ul>
					</li>

					<!-- Weather -->
					<li class="nav-item">
						<a class="nav-link" href="{{ url_for('weather') }}">
							Weather
						</a>
					</li>

					<!-- User Actions -->
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							Account
						</a>
						<ul class="dropdown-menu dropdown-menu-modern dropdown-menu-end shadow-lg">
							<li>
								<h6 class="dropdown-header text-primary">
									User Options
								</h6>
							</li>
							<li><a class="dropdown-item dropdown-item-modern" href="#">
								Profile Settings</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('home') }}">
								Dashboard</a></li>
							<li><hr class="dropdown-divider"></li>
							<li><a class="dropdown-item dropdown-item-modern text-danger" href="{{ url_for('logout') }}">
								Logout</a></li>
						</ul>
					</li>
				</ul>
			</div>
		</div>
	</nav>

	<!-- Content Wrapper -->
	<div class="content-wrapper">
		{% block content %}{% endblock %}
		{% block body %}{% endblock %}
	</div>

	<!-- Modern Footer -->
	<footer class="py-4 mt-5" style="background-color: #BDB76B;">
		<div class="container">
			<div class="row">
				<div class="col-md-6">
					<h5 class="text-dark">
						AgroPro Platform
					</h5>
					<p class="mb-0 text-dark">Empowering farmers with AI-driven agricultural solutions</p>
				</div>
				<div class="col-md-6 text-md-end">
					<p class="mb-0 text-dark">&copy; 2024 AgroPro. All rights reserved.</p>
					<small class="text-dark opacity-75">Smart Agriculture • Precision Farming • Sustainable Growth</small>
				</div>
			</div>
		</div>
	</footer>

	<!-- Custom JavaScript -->
	<script>
		// Navbar scroll effect
		window.addEventListener('scroll', function() {
			const navbar = document.getElementById('mainNavbar');
			if (window.scrollY > 50) {
				navbar.classList.add('scrolled');
			} else {
				navbar.classList.remove('scrolled');
			}
		});

		// Add fade-in animation to content
		document.addEventListener('DOMContentLoaded', function() {
			const contentWrapper = document.querySelector('.content-wrapper');
			if (contentWrapper) {
				contentWrapper.classList.add('fade-in');
			}
		});

		// Smooth scrolling for anchor links
		document.querySelectorAll('a[href^="#"]').forEach(anchor => {
			anchor.addEventListener('click', function (e) {
				e.preventDefault();
				const target = document.querySelector(this.getAttribute('href'));
				if (target) {
					target.scrollIntoView({
						behavior: 'smooth',
						block: 'start'
					});
				}
			});
		});
	</script>
</body>

</html>