<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('home') }}">
                <i class="fas fa-seedling text-success me-2" style="font-size: 24px;"></i>
                <span class="fw-bold" style="font-size: 22px;">AgroPro</span>
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-12">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-5 fw-bold text-primary">
                        <i class="fas fa-bell me-3"></i>Notifications & Alerts
                    </h1>
                    <p class="lead text-muted">Stay updated with important farming alerts and reminders</p>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="row">
                    <!-- Create Alert Form -->
                    <div class="col-lg-4 mb-4">
                        <div class="card border-0 shadow-lg">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Create Custom Alert</h5>
                            </div>
                            <div class="card-body">
                                <form action="{{ url_for('create_notification') }}" method="POST">
                                    <div class="mb-3">
                                        <label for="title" class="form-label fw-bold">
                                            <i class="fas fa-heading text-primary me-1"></i>Alert Title
                                        </label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               placeholder="e.g., Irrigation Reminder" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="type" class="form-label fw-bold">
                                            <i class="fas fa-tags text-success me-1"></i>Alert Type
                                        </label>
                                        <select class="form-select" id="type" name="type" required>
                                            <option value="">Select Type</option>
                                            <option value="weather">Weather Alert</option>
                                            <option value="irrigation">Irrigation Reminder</option>
                                            <option value="fertilizer">Fertilizer Application</option>
                                            <option value="pest">Pest Control</option>
                                            <option value="harvest">Harvest Reminder</option>
                                            <option value="market">Market Price Alert</option>
                                            <option value="general">General Reminder</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="message" class="form-label fw-bold">
                                            <i class="fas fa-comment text-info me-1"></i>Message
                                        </label>
                                        <textarea class="form-control" id="message" name="message" rows="4" 
                                                  placeholder="Enter your alert message..." required></textarea>
                                    </div>

                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-bell me-2"></i>Create Alert
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Alert Settings -->
                        <div class="card mt-4 border-0 shadow-sm">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-cog me-2"></i>Notification Settings</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="weatherAlerts" checked>
                                    <label class="form-check-label" for="weatherAlerts">
                                        Weather Alerts
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="priceAlerts" checked>
                                    <label class="form-check-label" for="priceAlerts">
                                        Price Alerts
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="farmingTips">
                                    <label class="form-check-label" for="farmingTips">
                                        Farming Tips
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="governmentSchemes" checked>
                                    <label class="form-check-label" for="governmentSchemes">
                                        Government Schemes
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications List -->
                    <div class="col-lg-8 mb-4">
                        <div class="card border-0 shadow-lg">
                            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Recent Notifications</h5>
                                <button class="btn btn-light btn-sm">
                                    <i class="fas fa-check-double me-1"></i>Mark All Read
                                </button>
                            </div>
                            <div class="card-body">
                                {% if notifications %}
                                    {% for notification in notifications %}
                                    <div class="alert alert-{{ 'light' if notification[4] else 'primary' }} border-start border-4 border-{{ 'secondary' if notification[4] else 'primary' }} mb-3">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <div class="d-flex align-items-center mb-2">
                                                    {% if notification[2] == 'weather' %}
                                                        <i class="fas fa-cloud-rain text-primary me-2"></i>
                                                    {% elif notification[2] == 'irrigation' %}
                                                        <i class="fas fa-tint text-info me-2"></i>
                                                    {% elif notification[2] == 'pest' %}
                                                        <i class="fas fa-bug text-danger me-2"></i>
                                                    {% elif notification[2] == 'market' %}
                                                        <i class="fas fa-chart-line text-success me-2"></i>
                                                    {% else %}
                                                        <i class="fas fa-bell text-warning me-2"></i>
                                                    {% endif %}
                                                    <h6 class="mb-0">{{ notification[0] }}</h6>
                                                    {% if not notification[4] %}
                                                        <span class="badge bg-danger ms-2">New</span>
                                                    {% endif %}
                                                </div>
                                                <p class="mb-2">{{ notification[1] }}</p>
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>{{ notification[3] }}
                                                </small>
                                            </div>
                                            <div class="ms-3">
                                                <button class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-check me-1"></i>Mark Read
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="text-center py-5">
                                        <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                                        <h5 class="text-muted">No notifications yet</h5>
                                        <p class="text-muted">You'll receive important farming alerts and reminders here.</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Automated Alerts -->
                <div class="card mt-4 border-0 shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-robot me-2"></i>Automated Alert System</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 bg-light h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-cloud-rain fa-3x text-primary mb-3"></i>
                                        <h6>Weather Alerts</h6>
                                        <p class="text-muted small">Automatic notifications for extreme weather conditions, rainfall predictions, and temperature changes.</p>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 bg-light h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                                        <h6>Price Alerts</h6>
                                        <p class="text-muted small">Get notified when crop prices reach your target levels or show significant changes.</p>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 bg-light h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-calendar-check fa-3x text-info mb-3"></i>
                                        <h6>Activity Reminders</h6>
                                        <p class="text-muted small">Automated reminders for scheduled farm activities like irrigation, fertilization, and harvesting.</p>
                                        <span class="badge bg-warning">Setup Required</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Emergency Alerts -->
                <div class="card mt-4 border-0 shadow-sm">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Emergency Alert System</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">Current Alerts</h6>
                                <div class="alert alert-warning" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Heavy Rainfall Warning:</strong> Expected in next 48 hours. Secure crops and equipment.
                                </div>
                                <div class="alert alert-info" role="alert">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Pest Alert:</strong> Increased aphid activity reported in nearby areas.
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">Emergency Contacts</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-phone text-primary me-2"></i>
                                        <strong>Agricultural Helpline:</strong> 1800-180-1551
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-hospital text-danger me-2"></i>
                                        <strong>Emergency Services:</strong> 108
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-cloud-rain text-info me-2"></i>
                                        <strong>Weather Helpline:</strong> 1800-180-1717
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification History -->
                <div class="card mt-4 border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="fas fa-history me-2"></i>Notification Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-primary">{{ notifications|length }}</h4>
                                    <small>Total Notifications</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-success">15</h4>
                                    <small>This Week</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-warning">8</h4>
                                    <small>Weather Alerts</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-info">5</h4>
                                    <small>Price Alerts</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SMS/Email Preferences -->
                <div class="card mt-4 border-0 shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-mobile-alt me-2"></i>Delivery Preferences</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>SMS Notifications</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="smsWeather" checked>
                                    <label class="form-check-label" for="smsWeather">Weather alerts</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="smsPrice" checked>
                                    <label class="form-check-label" for="smsPrice">Price alerts</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="smsEmergency" checked>
                                    <label class="form-check-label" for="smsEmergency">Emergency alerts</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Email Notifications</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="emailWeekly" checked>
                                    <label class="form-check-label" for="emailWeekly">Weekly summary</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="emailTips">
                                    <label class="form-check-label" for="emailTips">Farming tips</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="emailSchemes" checked>
                                    <label class="form-check-label" for="emailSchemes">Government schemes</label>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Preferences
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
