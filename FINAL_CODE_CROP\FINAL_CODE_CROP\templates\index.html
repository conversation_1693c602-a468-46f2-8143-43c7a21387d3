{% extends 'layout.html' %}

{% block content %}

<!-- Project Description Section -->
<section class="project-description py-4">
	<div class="container">
		<div class="row justify-content-center">
			<div class="col-lg-8 col-md-10 text-center">
				<div class="description-card p-4 rounded shadow-sm" style="background-color: rgba(245, 245, 220, 0.95); backdrop-filter: blur(10px);">
					<h1 class="h3 fw-bold mb-3" style="color: #2c5530;">AgroPro - Smart Agriculture Platform</h1>
					<p class="lead mb-4" style="color: #2c5530; font-size: 1.1rem;">
						Empowering farmers with AI-driven agricultural insights
					</p>
					
					<!-- Key Features Points -->
					<div class="row text-start">
						<div class="col-md-6 mb-3">
							<h5 class="fw-bold mb-2" style="color: #2c5530; font-size: 1rem;">
								AI-Powered Solutions
							</h5>
							<ul class="list-unstyled ms-3" style="color: #2c5530; font-size: 0.9rem;">
								<li class="mb-1">• Intelligent crop recommendations</li>
								<li class="mb-1">• Disease detection through image analysis</li>
								<li class="mb-1">• Personalized fertilizer advice</li>
							</ul>
						</div>
						
						<div class="col-md-6 mb-3">
							<h5 class="fw-bold mb-2" style="color: #2c5530; font-size: 1rem;">
								Data-Driven Benefits
							</h5>
							<ul class="list-unstyled ms-3" style="color: #2c5530; font-size: 0.9rem;">
								<li class="mb-1">• Optimize crop yields</li>
								<li class="mb-1">• Reduce agricultural losses</li>
								<li class="mb-1">• Promote sustainable farming</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Feature Cards Section -->
<section class="features-section py-5">
	<div class="container">
		<div class="row justify-content-center mb-4">
			<div class="col-lg-8 text-center">
				<h2 class="h3 fw-bold mb-3" style="color: #2c5530;">Platform Features</h2>
				<p class="lead" style="color: #2c5530;">Discover the powerful tools that make AgroPro your complete agricultural companion</p>
			</div>
		</div>
		
		<div class="row g-4">
			<!-- Crop Recommendation Card -->
			<div class="col-lg-4 col-md-6">
				<div class="card h-100 shadow-sm border-0" style="background-color: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
					<div class="card-body p-4 text-center">
						<h5 class="card-title fw-bold mb-3" style="color: #2c5530;">Smart Crop Recommendation</h5>
						<p class="card-text" style="color: #555;">
							Get AI-powered crop suggestions based on soil conditions, climate data, and regional factors for optimal yields.
						</p>
						<a href="{{ url_for('crop_recommend') }}" class="btn btn-success btn-sm">Try Now</a>
					</div>
				</div>
			</div>

			<!-- Disease Detection Card -->
			<div class="col-lg-4 col-md-6">
				<div class="card h-100 shadow-sm border-0" style="background-color: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
					<div class="card-body p-4 text-center">
						<h5 class="card-title fw-bold mb-3" style="color: #2c5530;">Disease Detection</h5>
						<p class="card-text" style="color: #555;">
							Upload plant images for instant disease identification and receive treatment recommendations using advanced AI analysis.
						</p>
						<a href="{{ url_for('disease_prediction') }}" class="btn btn-danger btn-sm">Diagnose</a>
					</div>
				</div>
			</div>

			<!-- Fertilizer Advisor Card -->
			<div class="col-lg-4 col-md-6">
				<div class="card h-100 shadow-sm border-0" style="background-color: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
					<div class="card-body p-4 text-center">
						<h5 class="card-title fw-bold mb-3" style="color: #2c5530;">Fertilizer Advisor</h5>
						<p class="card-text" style="color: #555;">
							Get personalized fertilizer recommendations based on soil composition and crop requirements for maximum nutrition.
						</p>
						<a href="{{ url_for('crop_fer') }}" class="btn btn-warning btn-sm">Get Advice</a>
					</div>
				</div>
			</div>

			<!-- Weather Analytics Card -->
			<div class="col-lg-4 col-md-6">
				<div class="card h-100 shadow-sm border-0" style="background-color: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
					<div class="card-body p-4 text-center">
						<h5 class="card-title fw-bold mb-3" style="color: #2c5530;">Weather Insights</h5>
						<p class="card-text" style="color: #555;">
							Access real-time weather data and forecasts to make informed decisions about planting, irrigation, and harvesting.
						</p>
						<a href="{{ url_for('weather') }}" class="btn btn-info btn-sm">View Weather</a>
					</div>
				</div>
			</div>

			<!-- Yield Prediction Card -->
			<div class="col-lg-4 col-md-6">
				<div class="card h-100 shadow-sm border-0" style="background-color: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
					<div class="card-body p-4 text-center">
						<h5 class="card-title fw-bold mb-3" style="color: #2c5530;">Yield Prediction</h5>
						<p class="card-text" style="color: #555;">
							Predict crop yields using machine learning models to help with planning, resource allocation, and market strategies.
						</p>
						<a href="{{ url_for('yield_page') }}" class="btn btn-primary btn-sm">Predict Yield</a>
					</div>
				</div>
			</div>

			<!-- Market Prices Card -->
			<div class="col-lg-4 col-md-6">
				<div class="card h-100 shadow-sm border-0" style="background-color: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
					<div class="card-body p-4 text-center">
						<h5 class="card-title fw-bold mb-3" style="color: #2c5530;">Market Intelligence</h5>
						<p class="card-text" style="color: #555;">
							Stay updated with real-time market prices and trends to make profitable decisions about when and where to sell.
						</p>
						<a href="{{ url_for('live_market_prices') }}" class="btn btn-warning btn-sm">View Prices</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Explore Section -->
<section class="explore-section py-4">
	<div class="container">
		<div class="row text-center mb-3">
			<div class="col-12">
				<a href="{{ url_for('crop_recommend') }}" class="btn btn-success btn-lg px-4 py-3" style="text-decoration: none;">
					<h2 class="h4 fw-bold mb-0" style="color: white;">Explore</h2>
				</a>
			</div>
		</div>
	</div>
</section>

<style>
@keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		transform: translateY(0);
	}
	40% {
		transform: translateY(-10px);
	}
	60% {
		transform: translateY(-5px);
	}
}

/* Feature Card Hover Effects */
.features-section .card {
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	border-radius: 15px;
}

.features-section .card:hover {
	transform: translateY(-10px);
	box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.features-section .btn {
	transition: all 0.3s ease;
	border-radius: 25px;
	padding: 8px 20px;
	font-weight: 500;
}

.features-section .btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.features-section .card-body {
		padding: 2rem 1.5rem;
	}
}
</style>

{% endblock %}
