import requests

# Test data that should trigger low confidence (based on server logs)
low_confidence_data = {
    'nitrogen': 50,
    'phosphorous': 30,
    'pottasium': 30,
    'temp': 15.0,
    'hum': 60.0,
    'ph': 7.0,
    'rainfall': 50.0
}

print("🧪 Testing improved low confidence display...")
print(f"📊 Using data that should give ~30% confidence: {low_confidence_data}")

try:
    response = requests.post('http://127.0.0.1:5000/crop_predict', 
                           data=low_confidence_data, 
                           timeout=10)
    
    if response.status_code == 200:
        # Save the response to see the improved UI
        with open('low_confidence_result.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("✅ Response saved to low_confidence_result.html")
        
        # Check for new elements
        if 'Low Confidence Explanation' in response.text:
            print("💡 Low confidence explanation found!")
        
        if 'Alternative Crop Suggestions' in response.text:
            print("🌱 Alternative suggestions found!")
            
        if 'Alternative Recommendation' in response.text:
            print("✨ Improved confidence message found!")
            
    else:
        print(f"❌ Request failed: {response.status_code}")

except Exception as e:
    print(f"❌ Error: {e}")

print("\n📄 You can open 'low_confidence_result.html' to see the improved UI!")
