{"model": {"architecture": "efficientnet_b0", "num_classes": 10, "pretrained": true}, "training": {"batch_size": 32, "epochs": 50, "learning_rate": 0.001, "optimizer": "adam", "scheduler": "step_lr", "step_size": 10, "gamma": 0.1}, "data": {"image_size": [224, 224], "train_split": 0.7, "val_split": 0.15, "test_split": 0.15, "augmentation": true}, "paths": {"dataset": "datasets", "models": "models", "logs": "logs"}}