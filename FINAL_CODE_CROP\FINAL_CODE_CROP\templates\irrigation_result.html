<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('home') }}">
                <i class="fas fa-seedling text-success me-2" style="font-size: 24px;"></i>
                <span class="fw-bold" style="font-size: 22px;">AgroPro</span>
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-5 fw-bold text-primary">
                        <i class="fas fa-tint me-3"></i>Irrigation Recommendation
                    </h1>
                    <p class="lead text-muted">Personalized irrigation plan for your {{ crop_type }} crop</p>
                </div>

                <!-- Crop Information -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i>Crop & Soil Information</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong><i class="fas fa-seedling text-success me-1"></i>Crop:</strong> {{ crop_type.title() }}
                            </div>
                            <div class="col-md-4">
                                <strong><i class="fas fa-mountain text-brown me-1"></i>Soil Type:</strong> {{ soil_type.title() }}
                            </div>
                            <div class="col-md-4">
                                <strong><i class="fas fa-map-marker-alt text-danger me-1"></i>Location:</strong> {{ location }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Water Requirements -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-lg">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-droplet me-2"></i>Water Requirements</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-calendar-day text-primary me-1"></i>Daily Water Need:</span>
                                        <span class="badge bg-primary fs-6">{{ recommendation.daily_water_mm }} mm</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-calendar-week text-success me-1"></i>Weekly Water Need:</span>
                                        <span class="badge bg-success fs-6">{{ recommendation.weekly_water_mm }} mm</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-clock text-warning me-1"></i>Irrigation Frequency:</span>
                                        <span class="badge bg-warning text-dark fs-6">{{ recommendation.irrigation_frequency }}</span>
                                    </div>
                                </div>
                                <div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-sun text-orange me-1"></i>Best Time:</span>
                                        <span class="badge bg-info fs-6">{{ recommendation.best_time }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-lg">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Irrigation Tips</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Monitor soil moisture regularly
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Avoid irrigation during peak sun hours
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Use drip irrigation for water efficiency
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Adjust frequency based on weather
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Check for proper drainage
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Irrigation Schedule -->
                <div class="card mb-4 border-0 shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>7-Day Irrigation Schedule</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Day</th>
                                        <th>Date</th>
                                        <th>Irrigation</th>
                                        <th>Amount (mm)</th>
                                        <th>Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for i in range(7) %}
                                    <tr>
                                        <td>Day {{ i + 1 }}</td>
                                        <td>{{ (moment().add(i, 'days').format('MMM DD') if moment else 'Date') }}</td>
                                        <td>
                                            {% if i % 2 == 0 %}
                                                <span class="badge bg-success">Yes</span>
                                            {% else %}
                                                <span class="badge bg-secondary">No</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if i % 2 == 0 %}
                                                {{ recommendation.daily_water_mm }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ recommendation.best_time }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center mb-4">
                    <a href="{{ url_for('irrigation_management') }}" class="btn btn-outline-primary me-3">
                        <i class="fas fa-arrow-left me-1"></i>New Analysis
                    </a>
                    <a href="{{ url_for('home') }}" class="btn btn-primary">
                        <i class="fas fa-home me-1"></i>Back to Dashboard
                    </a>
                </div>

                <!-- Additional Information -->
                <div class="alert alert-info" role="alert">
                    <h6><i class="fas fa-info-circle me-2"></i>Important Notes:</h6>
                    <ul class="mb-0">
                        <li>These recommendations are based on general crop requirements and soil characteristics.</li>
                        <li>Adjust irrigation based on local weather conditions and rainfall.</li>
                        <li>Consider installing soil moisture sensors for more precise irrigation management.</li>
                        <li>Consult with local agricultural experts for region-specific advice.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
