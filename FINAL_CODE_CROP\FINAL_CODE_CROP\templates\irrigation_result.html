<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('home') }}">
                <i class="fas fa-seedling text-success me-2" style="font-size: 24px;"></i>
                <span class="fw-bold" style="font-size: 22px;">AgroPro</span>
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-5 fw-bold text-primary">
                        <i class="fas fa-tint me-3"></i>Irrigation Recommendation
                    </h1>
                    <p class="lead text-muted">Personalized irrigation plan for your {{ crop_type }} crop</p>
                </div>

                <!-- Crop Information -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i>Crop & Soil Information</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong><i class="fas fa-seedling text-success me-1"></i>Crop:</strong> {{ crop_type.title() }}
                            </div>
                            <div class="col-md-4">
                                <strong><i class="fas fa-mountain text-brown me-1"></i>Soil Type:</strong> {{ soil_type.title() }}
                            </div>
                            <div class="col-md-4">
                                <strong><i class="fas fa-map-marker-alt text-danger me-1"></i>Location:</strong> {{ location }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Irrigation Decision -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-0 shadow-lg">
                            <div class="card-header {% if recommendation.irrigation_needed %}bg-warning text-dark{% else %}bg-success text-white{% endif %}">
                                <h4 class="mb-0">
                                    <i class="fas {% if recommendation.irrigation_needed %}fa-exclamation-triangle{% else %}fa-check-circle{% endif %} me-2"></i>
                                    {% if recommendation.irrigation_needed %}
                                        Irrigation Recommended
                                    {% else %}
                                        No Irrigation Needed
                                    {% endif %}
                                    {% if recommendation.get('ml_powered') %}
                                        <span class="badge bg-primary ms-2">AI-Powered</span>
                                    {% endif %}
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="{% if recommendation.irrigation_needed %}text-warning{% else %}text-success{% endif %}">
                                                {{ "%.1f"|format(recommendation.irrigation_probability * 100) }}%
                                            </h3>
                                            <small class="text-muted">Confidence</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-primary">{{ "%.1f"|format(recommendation.irrigation_amount_mm) }} mm</h3>
                                            <small class="text-muted">Water Amount</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-info">{{ recommendation.get('duration_minutes', 0) }} min</h3>
                                            <small class="text-muted">Duration</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <span class="badge bg-{{ recommendation.confidence }} fs-6">{{ recommendation.confidence.title() }}</span>
                                            <br><small class="text-muted">Confidence Level</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Water Requirements -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-lg">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-droplet me-2"></i>Irrigation Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-calendar-day text-primary me-1"></i>Schedule:</span>
                                        <span class="badge bg-primary">{{ recommendation.get('schedule', 'N/A') }}</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-tint text-info me-1"></i>Method:</span>
                                        <span class="badge bg-info">{{ recommendation.get('method', 'Drip irrigation') }}</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-clock text-warning me-1"></i>Best Time:</span>
                                        <span class="badge bg-warning text-dark">{{ recommendation.get('best_time', 'Early morning') }}</span>
                                    </div>
                                </div>
                                {% if recommendation.get('daily_water_mm') %}
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-chart-line text-success me-1"></i>Daily Need:</span>
                                        <span class="badge bg-success">{{ "%.1f"|format(recommendation.daily_water_mm) }} mm</span>
                                    </div>
                                </div>
                                {% endif %}
                                {% if recommendation.get('weather_score') %}
                                <div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-cloud-sun text-info me-1"></i>Weather Score:</span>
                                        <span class="badge bg-info">{{ "%.0f"|format(recommendation.weather_score) }}/100</span>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-lg">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>AI Insights & Tips</h5>
                            </div>
                            <div class="card-body">
                                {% if recommendation.get('explanations') and recommendation.explanations %}
                                <h6 class="text-primary"><i class="fas fa-brain me-1"></i>Why this recommendation?</h6>
                                <ul class="list-unstyled mb-3">
                                    {% for explanation in recommendation.explanations %}
                                    <li class="mb-2">
                                        <i class="fas fa-arrow-right text-primary me-2"></i>
                                        {{ explanation }}
                                    </li>
                                    {% endfor %}
                                </ul>
                                {% endif %}

                                {% if recommendation.get('warnings') and recommendation.warnings %}
                                <h6 class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Warnings</h6>
                                <ul class="list-unstyled mb-3">
                                    {% for warning in recommendation.warnings %}
                                    <li class="mb-2">
                                        <i class="fas fa-exclamation-circle text-warning me-2"></i>
                                        {{ warning }}
                                    </li>
                                    {% endfor %}
                                </ul>
                                {% endif %}

                                <h6 class="text-success"><i class="fas fa-tips me-1"></i>General Tips</h6>
                                <ul class="list-unstyled">
                                    {% if recommendation.get('tips') and recommendation.tips %}
                                        {% for tip in recommendation.tips %}
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            {{ tip }}
                                        </li>
                                        {% endfor %}
                                    {% else %}
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            Monitor soil moisture regularly
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            Avoid irrigation during peak sun hours
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            Use drip irrigation for water efficiency
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            Adjust frequency based on weather conditions
                                        </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Irrigation Schedule -->
                <div class="card mb-4 border-0 shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>7-Day Irrigation Schedule</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Day</th>
                                        <th>Date</th>
                                        <th>Irrigation</th>
                                        <th>Amount (mm)</th>
                                        <th>Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for i in range(7) %}
                                    <tr>
                                        <td>Day {{ i + 1 }}</td>
                                        <td>{{ (moment().add(i, 'days').format('MMM DD') if moment else 'Date') }}</td>
                                        <td>
                                            {% if i % 2 == 0 %}
                                                <span class="badge bg-success">Yes</span>
                                            {% else %}
                                                <span class="badge bg-secondary">No</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if i % 2 == 0 %}
                                                {{ recommendation.daily_water_mm }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ recommendation.best_time }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center mb-4">
                    <a href="{{ url_for('irrigation_management') }}" class="btn btn-outline-primary me-3">
                        <i class="fas fa-arrow-left me-1"></i>New Analysis
                    </a>
                    <a href="{{ url_for('home') }}" class="btn btn-primary">
                        <i class="fas fa-home me-1"></i>Back to Dashboard
                    </a>
                </div>

                <!-- Additional Information -->
                <div class="alert alert-info" role="alert">
                    <h6><i class="fas fa-info-circle me-2"></i>Important Notes:</h6>
                    <ul class="mb-0">
                        <li>These recommendations are based on general crop requirements and soil characteristics.</li>
                        <li>Adjust irrigation based on local weather conditions and rainfall.</li>
                        <li>Consider installing soil moisture sensors for more precise irrigation management.</li>
                        <li>Consult with local agricultural experts for region-specific advice.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
