#!/usr/bin/env python3
"""
Debug registration test script
"""

import requests
import sqlite3

def debug_registration():
    """Debug registration functionality"""
    print("🔍 Debugging Registration Functionality")
    print("=" * 40)
    
    test_user = {
        'name': 'debuguser123',
        'email': '<EMAIL>',
        'phone': '9876543210',
        'password': 'testpass123'
    }
    
    print(f"Testing registration with: {test_user}")
    
    try:
        session = requests.Session()
        
        # Test registration
        response = session.post('http://127.0.0.1:5000/userreg', 
                               data=test_user, 
                               timeout=10)
        
        print(f"Registration response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        # Check response content for error messages
        response_text = response.text
        
        if 'Successfully Registered' in response_text:
            print("✅ Registration successful!")
            return True
        elif 'already exists' in response_text:
            print("⚠️ User already exists")
            return True
        elif 'Please fill in all fields' in response_text:
            print("❌ Missing fields error")
        elif 'Password must be at least' in response_text:
            print("❌ Password validation error")
        elif 'valid email' in response_text:
            print("❌ Email validation error")
        elif 'valid 10-digit' in response_text:
            print("❌ Phone validation error")
        elif 'Database error' in response_text:
            print("❌ Database error")
        else:
            print("❌ Unknown error")
            # Look for specific error patterns
            if 'error' in response_text.lower():
                print("Found error keywords in response")
            
            # Check if it's just returning the form without an error message
            if 'User Regestration' in response_text:
                print("❌ Form returned without success message - likely a validation or database error")
        
        print("Response content preview:", response_text[:800])
        return False
            
    except Exception as e:
        print(f"❌ Error during registration test: {e}")
        return False

if __name__ == "__main__":
    success = debug_registration()
