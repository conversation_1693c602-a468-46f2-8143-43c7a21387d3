import os

# List of all crop image paths from get_crop_info function
crop_images = [
    '/static/images/Rice.jpg',
    '/static/images/Wheat.webp', 
    '/static/images/Maize.jpg',
    '/static/images/Cotton.jpg',
    '/static/images/crops/sugarcane.svg',
    '/static/images/Jute.jpg',
    '/static/images/crops/default.svg',
    '/static/images/Apple.jpg',
    '/static/images/Banana.jpg',
    '/static/images/Grapes.jpg',
    '/static/images/Mango.jpg',
    "/static/images/Orange'.jfif",
    '/static/images/Papaya.jpg',
    '/static/images/Pomegranate.jpg',
    '/static/images/Coffee.jpg',
    '/static/images/Blackgram.jpg',
    '/static/images/Chickpea.jpg',
    '/static/images/Kidneybeans.jpg',
    '/static/images/Lentil.jpeg',
    '/static/images/Mothbeans.jpg',
    '/static/images/Mungbean.jpg',
    '/static/images/Pigeonpeas.jpg',
    '/static/images/Muskmelon.jpg',
    '/static/images/Watermelon.jpg'
]

base_path = "c:\\Users\\<USER>\\Desktop\\main_project\\FINAL_CODE_CROP\\FINAL_CODE_CROP"

print("🔍 Checking crop image files...")
print("=" * 60)

missing_images = []
existing_images = []

for img_path in crop_images:
    # Convert web path to file system path
    file_path = img_path.replace('/static/', 'static\\')
    full_path = os.path.join(base_path, file_path)
    
    if os.path.exists(full_path):
        existing_images.append(img_path)
        print(f"✅ {img_path}")
    else:
        missing_images.append(img_path)
        print(f"❌ {img_path} -> {full_path}")

print("\n" + "=" * 60)
print(f"📊 Summary:")
print(f"✅ Existing images: {len(existing_images)}")
print(f"❌ Missing images: {len(missing_images)}")

if missing_images:
    print(f"\n🚨 Missing image files:")
    for img in missing_images:
        print(f"   - {img}")
else:
    print(f"\n🎉 All crop images are available!")
