<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('home') }}">
                <i class="fas fa-seedling text-success me-2" style="font-size: 24px;"></i>
                <span class="fw-bold" style="font-size: 22px;">AgroPro</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('home') }}">
                            <i class="fas fa-home me-1"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('crop_recommend') }}">
                            <i class="fas fa-leaf me-1"></i> Crop Recommendation
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('irrigation_management') }}">
                            <i class="fas fa-tint me-1"></i> Irrigation
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-5 fw-bold text-primary">
                        <i class="fas fa-tint me-3"></i>Irrigation Management Advisor
                    </h1>
                    <p class="lead text-muted">Get personalized irrigation recommendations based on your crop, soil, and location</p>
                </div>

                <!-- Irrigation Form -->
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-droplet me-2"></i>Irrigation Analysis</h4>
                    </div>
                    <div class="card-body p-4">
                        <form action="{{ url_for('irrigation_predict') }}" method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="crop_type" class="form-label fw-bold">
                                        <i class="fas fa-seedling text-success me-1"></i>Crop Type
                                    </label>
                                    <select class="form-select" id="crop_type" name="crop_type" required>
                                        <option value="">Select Crop</option>
                                        <option value="rice">Rice</option>
                                        <option value="wheat">Wheat</option>
                                        <option value="corn">Corn/Maize</option>
                                        <option value="cotton">Cotton</option>
                                        <option value="sugarcane">Sugarcane</option>
                                        <option value="tomato">Tomato</option>
                                        <option value="potato">Potato</option>
                                        <option value="onion">Onion</option>
                                        <option value="soybean">Soybean</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="soil_type" class="form-label fw-bold">
                                        <i class="fas fa-mountain text-brown me-1"></i>Soil Type
                                    </label>
                                    <select class="form-select" id="soil_type" name="soil_type" required>
                                        <option value="">Select Soil Type</option>
                                        <option value="clay">Clay</option>
                                        <option value="loam">Loam</option>
                                        <option value="sandy">Sandy</option>
                                        <option value="silt">Silt</option>
                                        <option value="sandy_loam">Sandy Loam</option>
                                        <option value="clay_loam">Clay Loam</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="location" class="form-label fw-bold">
                                        <i class="fas fa-map-marker-alt text-danger me-1"></i>Location/State
                                    </label>
                                    <input type="text" class="form-control" id="location" name="location" 
                                           placeholder="e.g., Punjab, Maharashtra" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="growth_stage" class="form-label fw-bold">
                                        <i class="fas fa-chart-line text-info me-1"></i>Growth Stage
                                    </label>
                                    <select class="form-select" id="growth_stage" name="growth_stage">
                                        <option value="initial">Initial</option>
                                        <option value="development">Development</option>
                                        <option value="mid" selected>Mid Season</option>
                                        <option value="late">Late Season</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="farm_size" class="form-label fw-bold">
                                        <i class="fas fa-expand-arrows-alt text-warning me-1"></i>Farm Size (acres)
                                    </label>
                                    <input type="number" class="form-control" id="farm_size" name="farm_size" 
                                           placeholder="e.g., 5" step="0.1" min="0.1">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="current_moisture" class="form-label fw-bold">
                                        <i class="fas fa-thermometer-half text-primary me-1"></i>Current Soil Moisture (%)
                                    </label>
                                    <input type="number" class="form-control" id="current_moisture" name="current_moisture" 
                                           placeholder="e.g., 25" min="0" max="100">
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-calculator me-2"></i>Get Irrigation Recommendation
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Information Cards -->
                <div class="row mt-5">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-water fa-3x text-primary mb-3"></i>
                                <h5>Water Efficiency</h5>
                                <p class="text-muted">Optimize water usage based on crop requirements and soil conditions</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-3x text-success mb-3"></i>
                                <h5>Smart Scheduling</h5>
                                <p class="text-muted">Get optimal irrigation timing and frequency recommendations</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-bar fa-3x text-warning mb-3"></i>
                                <h5>Yield Optimization</h5>
                                <p class="text-muted">Improve crop yield through precise irrigation management</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
