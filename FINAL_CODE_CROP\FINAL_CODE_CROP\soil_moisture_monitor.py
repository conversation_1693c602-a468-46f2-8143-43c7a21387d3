"""
Soil Moisture Monitoring System
Tracks soil moisture levels, stores historical data, and provides analytics
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import random
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from flask import Flask, render_template, request, jsonify

class SoilMoistureMonitor:
    def __init__(self, db_path="irrigation_data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize soil moisture monitoring database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check if table exists and add missing columns
        cursor.execute("PRAGMA table_info(soil_moisture_readings)")
        existing_columns = [column[1] for column in cursor.fetchall()]

        # Enhanced soil moisture readings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS soil_moisture_readings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location TEXT NOT NULL,
                sensor_id TEXT,
                moisture_level REAL NOT NULL,
                temperature REAL,
                ph_level REAL,
                ec_level REAL,
                reading_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Add missing columns if they don't exist
        columns_to_add = [
            ('field_id', 'TEXT'),
            ('crop_type', 'TEXT'),
            ('depth_cm', 'INTEGER DEFAULT 30'),
            ('sensor_type', 'TEXT DEFAULT "capacitive"'),
            ('battery_level', 'REAL'),
            ('signal_strength', 'REAL'),
            ('created_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        ]

        for column_name, column_type in columns_to_add:
            if column_name not in existing_columns:
                try:
                    cursor.execute(f'ALTER TABLE soil_moisture_readings ADD COLUMN {column_name} {column_type}')
                    print(f"✅ Added column {column_name} to soil_moisture_readings")
                except sqlite3.OperationalError:
                    pass  # Column might already exist
        
        # Sensor configuration table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sensor_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sensor_id TEXT UNIQUE NOT NULL,
                location TEXT NOT NULL,
                field_id TEXT,
                crop_type TEXT,
                installation_date DATE,
                depth_cm INTEGER DEFAULT 30,
                sensor_type TEXT DEFAULT 'capacitive',
                calibration_offset REAL DEFAULT 0.0,
                is_active BOOLEAN DEFAULT TRUE,
                last_maintenance DATE,
                notes TEXT
            )
        ''')
        
        # Moisture alerts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS moisture_alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sensor_id TEXT NOT NULL,
                location TEXT NOT NULL,
                alert_type TEXT NOT NULL,
                moisture_level REAL,
                threshold_value REAL,
                message TEXT,
                is_resolved BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                resolved_at TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Soil moisture monitoring database initialized")
    
    def add_sensor(self, sensor_id, location, field_id=None, crop_type=None, 
                   depth_cm=30, sensor_type='capacitive'):
        """Add a new soil moisture sensor"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO sensor_config 
                (sensor_id, location, field_id, crop_type, depth_cm, sensor_type, installation_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (sensor_id, location, field_id, crop_type, depth_cm, sensor_type, datetime.now().date()))
            
            conn.commit()
            print(f"✅ Added sensor {sensor_id} at {location}")
            return True
        except sqlite3.IntegrityError:
            print(f"❌ Sensor {sensor_id} already exists")
            return False
        finally:
            conn.close()
    
    def record_reading(self, sensor_id, moisture_level, temperature=None, 
                      ph_level=None, ec_level=None, battery_level=None, signal_strength=None):
        """Record a soil moisture reading"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get sensor configuration
        sensor_config = cursor.execute('''
            SELECT location, field_id, crop_type, depth_cm, sensor_type, calibration_offset
            FROM sensor_config WHERE sensor_id = ? AND is_active = TRUE
        ''', (sensor_id,)).fetchone()
        
        if not sensor_config:
            print(f"❌ Sensor {sensor_id} not found or inactive")
            conn.close()
            return False
        
        location, field_id, crop_type, depth_cm, sensor_type, calibration_offset = sensor_config
        
        # Apply calibration offset
        calibrated_moisture = moisture_level + (calibration_offset or 0)
        
        # Insert reading
        cursor.execute('''
            INSERT INTO soil_moisture_readings 
            (location, sensor_id, field_id, crop_type, moisture_level, temperature, 
             ph_level, ec_level, depth_cm, sensor_type, battery_level, signal_strength)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (location, sensor_id, field_id, crop_type, calibrated_moisture, 
              temperature, ph_level, ec_level, depth_cm, sensor_type, 
              battery_level, signal_strength))
        
        conn.commit()
        
        # Check for alerts
        self.check_moisture_alerts(sensor_id, calibrated_moisture, location)
        
        conn.close()
        return True
    
    def check_moisture_alerts(self, sensor_id, moisture_level, location):
        """Check if moisture level triggers any alerts"""
        alerts = []
        
        # Define thresholds
        if moisture_level < 20:
            alert_type = "LOW_MOISTURE"
            message = f"Critical: Soil moisture at {location} is {moisture_level:.1f}% - Immediate irrigation needed"
            threshold = 20
        elif moisture_level < 30:
            alert_type = "MODERATE_LOW"
            message = f"Warning: Soil moisture at {location} is {moisture_level:.1f}% - Consider irrigation"
            threshold = 30
        elif moisture_level > 80:
            alert_type = "HIGH_MOISTURE"
            message = f"Alert: Soil moisture at {location} is {moisture_level:.1f}% - Risk of waterlogging"
            threshold = 80
        else:
            return  # No alert needed
        
        # Store alert
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO moisture_alerts 
            (sensor_id, location, alert_type, moisture_level, threshold_value, message)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (sensor_id, location, alert_type, moisture_level, threshold, message))
        
        conn.commit()
        conn.close()
        
        print(f"🚨 Alert: {message}")
    
    def get_current_readings(self, location=None, sensor_id=None):
        """Get current moisture readings"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT r.sensor_id, r.location, r.field_id, r.crop_type, 
                   r.moisture_level, r.temperature, r.ph_level, r.ec_level,
                   r.depth_cm, r.reading_timestamp, s.sensor_type
            FROM soil_moisture_readings r
            JOIN sensor_config s ON r.sensor_id = s.sensor_id
            WHERE r.reading_timestamp >= datetime('now', '-1 hour')
        '''
        
        params = []
        if location:
            query += ' AND r.location = ?'
            params.append(location)
        if sensor_id:
            query += ' AND r.sensor_id = ?'
            params.append(sensor_id)
        
        query += ' ORDER BY r.reading_timestamp DESC'
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        return df
    
    def get_historical_data(self, sensor_id=None, location=None, days=7):
        """Get historical moisture data"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT sensor_id, location, field_id, crop_type, moisture_level, 
                   temperature, ph_level, ec_level, reading_timestamp
            FROM soil_moisture_readings
            WHERE reading_timestamp >= datetime('now', '-{} days')
        '''.format(days)
        
        params = []
        if sensor_id:
            query += ' AND sensor_id = ?'
            params.append(sensor_id)
        if location:
            query += ' AND location = ?'
            params.append(location)
        
        query += ' ORDER BY reading_timestamp ASC'
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        if not df.empty:
            df['reading_timestamp'] = pd.to_datetime(df['reading_timestamp'])
        
        return df
    
    def get_moisture_trends(self, sensor_id=None, location=None, days=7):
        """Analyze moisture trends"""
        df = self.get_historical_data(sensor_id, location, days)
        
        if df.empty:
            return None
        
        # Calculate trends
        trends = {}
        
        for sensor in df['sensor_id'].unique():
            sensor_data = df[df['sensor_id'] == sensor].copy()
            sensor_data = sensor_data.sort_values('reading_timestamp')
            
            if len(sensor_data) < 2:
                continue
            
            # Calculate trend slope
            x = np.arange(len(sensor_data))
            y = sensor_data['moisture_level'].values
            slope = np.polyfit(x, y, 1)[0]
            
            # Calculate statistics
            current_level = sensor_data['moisture_level'].iloc[-1]
            avg_level = sensor_data['moisture_level'].mean()
            min_level = sensor_data['moisture_level'].min()
            max_level = sensor_data['moisture_level'].max()
            
            # Determine trend direction
            if slope > 0.5:
                trend_direction = "increasing"
            elif slope < -0.5:
                trend_direction = "decreasing"
            else:
                trend_direction = "stable"
            
            trends[sensor] = {
                'current_level': current_level,
                'average_level': avg_level,
                'min_level': min_level,
                'max_level': max_level,
                'trend_slope': slope,
                'trend_direction': trend_direction,
                'readings_count': len(sensor_data),
                'location': sensor_data['location'].iloc[0]
            }
        
        return trends
    
    def get_active_alerts(self, resolved=False):
        """Get active moisture alerts"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT id, sensor_id, location, alert_type, moisture_level, 
                   threshold_value, message, created_at
            FROM moisture_alerts
            WHERE is_resolved = ?
            ORDER BY created_at DESC
        '''
        
        df = pd.read_sql_query(query, conn, params=[resolved])
        conn.close()
        
        return df
    
    def resolve_alert(self, alert_id):
        """Mark an alert as resolved"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE moisture_alerts 
            SET is_resolved = TRUE, resolved_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (alert_id,))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Alert {alert_id} resolved")
    
    def generate_sample_data(self, num_sensors=5, days=7):
        """Generate sample soil moisture data for testing"""
        print(f"🔄 Generating sample data for {num_sensors} sensors over {days} days...")
        
        # Sample locations and sensors
        locations = ['Field_A', 'Field_B', 'Field_C', 'Greenhouse_1', 'Greenhouse_2']
        crops = ['wheat', 'corn', 'tomato', 'potato', 'cotton']
        
        for i in range(num_sensors):
            sensor_id = f"SENSOR_{i+1:03d}"
            location = locations[i % len(locations)]
            crop = crops[i % len(crops)]
            
            # Add sensor
            self.add_sensor(sensor_id, location, f"FIELD_{i+1}", crop)
            
            # Generate readings
            base_moisture = random.uniform(25, 45)
            
            for day in range(days):
                for hour in range(0, 24, 4):  # Every 4 hours
                    timestamp = datetime.now() - timedelta(days=days-day, hours=hour)
                    
                    # Simulate moisture variation
                    moisture = base_moisture + random.uniform(-10, 10)
                    moisture = max(5, min(95, moisture))  # Keep within realistic bounds
                    
                    # Simulate other parameters
                    temperature = random.uniform(18, 35)
                    ph = random.uniform(6.0, 7.5)
                    ec = random.uniform(0.5, 2.5)
                    battery = random.uniform(70, 100)
                    signal = random.uniform(80, 100)
                    
                    # Record reading with timestamp
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT INTO soil_moisture_readings 
                        (location, sensor_id, field_id, crop_type, moisture_level, 
                         temperature, ph_level, ec_level, depth_cm, sensor_type, 
                         battery_level, signal_strength, reading_timestamp)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (location, sensor_id, f"FIELD_{i+1}", crop, moisture,
                          temperature, ph, ec, 30, 'capacitive', battery, signal, timestamp))
                    
                    conn.commit()
                    conn.close()
        
        print(f"✅ Generated sample data for {num_sensors} sensors")
    
    def get_dashboard_data(self):
        """Get data for moisture monitoring dashboard"""
        # Current readings
        current_readings = self.get_current_readings()
        
        # Active alerts
        active_alerts = self.get_active_alerts(resolved=False)
        
        # Trends
        trends = self.get_moisture_trends(days=7)
        
        # Summary statistics
        conn = sqlite3.connect(self.db_path)
        
        # Total sensors
        total_sensors = conn.execute('SELECT COUNT(*) FROM sensor_config WHERE is_active = TRUE').fetchone()[0]
        
        # Readings today
        readings_today = conn.execute('''
            SELECT COUNT(*) FROM soil_moisture_readings 
            WHERE DATE(reading_timestamp) = DATE('now')
        ''').fetchone()[0]
        
        # Average moisture by location
        avg_moisture_query = '''
            SELECT location, AVG(moisture_level) as avg_moisture, COUNT(*) as reading_count
            FROM soil_moisture_readings 
            WHERE reading_timestamp >= datetime('now', '-24 hours')
            GROUP BY location
        '''
        avg_moisture_df = pd.read_sql_query(avg_moisture_query, conn)
        
        conn.close()
        
        return {
            'current_readings': current_readings.to_dict('records') if not current_readings.empty else [],
            'active_alerts': active_alerts.to_dict('records') if not active_alerts.empty else [],
            'trends': trends or {},
            'summary': {
                'total_sensors': total_sensors,
                'readings_today': readings_today,
                'active_alerts_count': len(active_alerts)
            },
            'avg_moisture_by_location': avg_moisture_df.to_dict('records') if not avg_moisture_df.empty else []
        }

# Initialize the soil moisture monitor
soil_monitor = SoilMoistureMonitor()

def main():
    """Test the soil moisture monitoring system"""
    print("🌱 Soil Moisture Monitoring System")
    print("=" * 50)
    
    # Generate sample data
    soil_monitor.generate_sample_data(num_sensors=3, days=5)
    
    # Get dashboard data
    dashboard_data = soil_monitor.get_dashboard_data()
    
    print(f"\n📊 Dashboard Summary:")
    print(f"Total Sensors: {dashboard_data['summary']['total_sensors']}")
    print(f"Readings Today: {dashboard_data['summary']['readings_today']}")
    print(f"Active Alerts: {dashboard_data['summary']['active_alerts_count']}")
    
    print(f"\n📈 Current Readings:")
    for reading in dashboard_data['current_readings'][:5]:
        print(f"  {reading['sensor_id']} ({reading['location']}): {reading['moisture_level']:.1f}%")
    
    print(f"\n🚨 Active Alerts:")
    for alert in dashboard_data['active_alerts'][:3]:
        print(f"  {alert['alert_type']}: {alert['message']}")
    
    print("\n✅ Soil moisture monitoring system test completed!")

if __name__ == "__main__":
    main()
