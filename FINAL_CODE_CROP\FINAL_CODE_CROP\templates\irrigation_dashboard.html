<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Irrigation Management Dashboard - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-good { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-critical { background-color: #dc3545; }
        .weather-widget {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }
        .soil-widget {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-seedling me-2"></i>AgroPro - Irrigation Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/irrigation"><i class="fas fa-tint me-1"></i>New Analysis</a>
                <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Home</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card dashboard-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-tint fa-2x mb-2"></i>
                        <h3 id="active-sensors">{{ dashboard_data.summary.total_sensors or 0 }}</h3>
                        <p class="mb-0">Active Sensors</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <h3 id="readings-today">{{ dashboard_data.summary.readings_today or 0 }}</h3>
                        <p class="mb-0">Readings Today</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h3 id="active-alerts">{{ dashboard_data.summary.active_alerts_count or 0 }}</h3>
                        <p class="mb-0">Active Alerts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-droplet fa-2x mb-2"></i>
                        <h3 id="avg-moisture">{{ "%.1f"|format(dashboard_data.avg_moisture or 0) }}%</h3>
                        <p class="mb-0">Avg Moisture</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Current Soil Moisture Status -->
            <div class="col-lg-6 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-thermometer-half me-2"></i>Current Soil Moisture</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Location</th>
                                        <th>Moisture</th>
                                        <th>Status</th>
                                        <th>Last Reading</th>
                                    </tr>
                                </thead>
                                <tbody id="moisture-readings">
                                    {% for reading in dashboard_data.current_readings %}
                                    <tr>
                                        <td>{{ reading.location }}</td>
                                        <td>{{ "%.1f"|format(reading.moisture_level) }}%</td>
                                        <td>
                                            {% if reading.moisture_level < 25 %}
                                                <span class="status-indicator status-critical"></span>Critical
                                            {% elif reading.moisture_level < 35 %}
                                                <span class="status-indicator status-warning"></span>Low
                                            {% else %}
                                                <span class="status-indicator status-good"></span>Good
                                            {% endif %}
                                        </td>
                                        <td>{{ reading.reading_timestamp }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Weather Information -->
            <div class="col-lg-6 mb-4">
                <div class="card dashboard-card weather-widget">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cloud-sun me-2"></i>Weather Conditions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <i class="fas fa-thermometer-half fa-2x mb-2"></i>
                                <h4 id="current-temp">{{ "%.1f"|format(weather_data.temperature or 25) }}°C</h4>
                                <small>Temperature</small>
                            </div>
                            <div class="col-6">
                                <i class="fas fa-tint fa-2x mb-2"></i>
                                <h4 id="current-humidity">{{ "%.1f"|format(weather_data.humidity or 65) }}%</h4>
                                <small>Humidity</small>
                            </div>
                        </div>
                        <hr class="my-3">
                        <div class="row text-center">
                            <div class="col-6">
                                <i class="fas fa-wind fa-2x mb-2"></i>
                                <h4 id="wind-speed">{{ "%.1f"|format(weather_data.wind_speed or 10) }} km/h</h4>
                                <small>Wind Speed</small>
                            </div>
                            <div class="col-6">
                                <i class="fas fa-cloud-rain fa-2x mb-2"></i>
                                <h4 id="rainfall">{{ "%.1f"|format(weather_data.rainfall or 0) }} mm</h4>
                                <small>Rainfall</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Moisture Trends Chart -->
            <div class="col-lg-8 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Soil Moisture Trends (7 Days)</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="moistureTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Alerts -->
            <div class="col-lg-4 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Active Alerts</h5>
                    </div>
                    <div class="card-body">
                        <div id="alerts-container">
                            {% for alert in dashboard_data.active_alerts %}
                            <div class="alert alert-{{ 'danger' if alert.alert_type == 'LOW_MOISTURE' else 'warning' }} alert-sm">
                                <strong>{{ alert.location }}</strong><br>
                                <small>{{ alert.message }}</small>
                                <button class="btn btn-sm btn-outline-secondary float-end" onclick="resolveAlert({{ alert.id }})">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                            {% else %}
                            <div class="text-center text-muted">
                                <i class="fas fa-check-circle fa-3x mb-2"></i>
                                <p>No active alerts</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Irrigation Recommendations -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Smart Irrigation Recommendations</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for location, data in dashboard_data.recommendations.items() %}
                            <div class="col-md-4 mb-3">
                                <div class="card border-{{ 'success' if not data.irrigation_needed else 'warning' }}">
                                    <div class="card-body">
                                        <h6 class="card-title">{{ location }}</h6>
                                        <p class="card-text">
                                            <strong>Status:</strong> 
                                            {% if data.irrigation_needed %}
                                                <span class="text-warning">Irrigation Needed</span>
                                            {% else %}
                                                <span class="text-success">No Irrigation Needed</span>
                                            {% endif %}
                                        </p>
                                        {% if data.irrigation_needed %}
                                        <p class="card-text">
                                            <small class="text-muted">
                                                Amount: {{ "%.1f"|format(data.irrigation_amount_mm) }} mm<br>
                                                Schedule: {{ data.schedule }}
                                            </small>
                                        </p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize moisture trend chart
        const ctx = document.getElementById('moistureTrendChart').getContext('2d');
        const moistureTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ trend_labels | tojson }},
                datasets: [
                    {
                        label: 'Field A',
                        data: {{ trend_data_a | tojson }},
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    },
                    {
                        label: 'Field B',
                        data: {{ trend_data_b | tojson }},
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Soil Moisture (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });

        // Auto-refresh dashboard every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);

        // Resolve alert function
        function resolveAlert(alertId) {
            fetch('/api/resolve_alert', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({alert_id: alertId})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        }

        // Real-time updates (if WebSocket is available)
        function updateDashboard() {
            fetch('/api/dashboard_data')
                .then(response => response.json())
                .then(data => {
                    // Update summary cards
                    document.getElementById('active-sensors').textContent = data.summary.total_sensors;
                    document.getElementById('readings-today').textContent = data.summary.readings_today;
                    document.getElementById('active-alerts').textContent = data.summary.active_alerts_count;
                    
                    // Update moisture readings table
                    updateMoistureTable(data.current_readings);
                });
        }

        function updateMoistureTable(readings) {
            const tbody = document.getElementById('moisture-readings');
            tbody.innerHTML = '';
            
            readings.forEach(reading => {
                const row = document.createElement('tr');
                const status = reading.moisture_level < 25 ? 'critical' : 
                              reading.moisture_level < 35 ? 'warning' : 'good';
                const statusText = reading.moisture_level < 25 ? 'Critical' : 
                                  reading.moisture_level < 35 ? 'Low' : 'Good';
                
                row.innerHTML = `
                    <td>${reading.location}</td>
                    <td>${reading.moisture_level.toFixed(1)}%</td>
                    <td><span class="status-indicator status-${status}"></span>${statusText}</td>
                    <td>${reading.reading_timestamp}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // Update dashboard every 30 seconds
        setInterval(updateDashboard, 30000);
    </script>
</body>
</html>
