"""
Advanced Irrigation Prediction Model
Uses ensemble methods and neural networks for irrigation scheduling
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor, GradientBoostingClassifier, GradientBoostingRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.svm import SVC, SVR
from sklearn.neural_network import MLPClassifier, MLPRegressor
from sklearn.metrics import classification_report, confusion_matrix, mean_squared_error, r2_score, accuracy_score
from sklearn.model_selection import cross_val_score, GridSearchCV
import joblib
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from irrigation_preprocessing import IrrigationDataPreprocessor
import warnings
warnings.filterwarnings('ignore')

class IrrigationPredictor:
    def __init__(self):
        self.classification_models = {}
        self.regression_models = {}
        self.best_classifier = None
        self.best_regressor = None
        self.feature_importance = None
        self.is_trained = False
        
    def initialize_models(self):
        """Initialize different ML models for comparison"""
        print("🤖 Initializing ML models...")
        
        # Classification models (irrigation needed: yes/no)
        self.classification_models = {
            'random_forest': RandomForestClassifier(
                n_estimators=100, 
                max_depth=10, 
                random_state=42,
                class_weight='balanced'
            ),
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            ),
            'neural_network': MLPClassifier(
                hidden_layer_sizes=(100, 50),
                max_iter=500,
                random_state=42,
                early_stopping=True
            ),
            'logistic_regression': LogisticRegression(
                random_state=42,
                class_weight='balanced',
                max_iter=1000
            )
        }
        
        # Regression models (irrigation amount in mm)
        self.regression_models = {
            'random_forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            ),
            'neural_network': MLPRegressor(
                hidden_layer_sizes=(100, 50),
                max_iter=500,
                random_state=42,
                early_stopping=True
            ),
            'linear_regression': LinearRegression()
        }
        
        print(f"✅ Initialized {len(self.classification_models)} classification and {len(self.regression_models)} regression models")
    
    def train_classification_models(self, X_train, y_train, X_test, y_test):
        """Train and evaluate classification models"""
        print("\n🎯 Training Classification Models (Irrigation Needed)...")
        
        classification_results = {}
        
        for name, model in self.classification_models.items():
            print(f"\n📊 Training {name}...")
            
            # Train model
            model.fit(X_train, y_train)
            
            # Predictions
            y_pred_train = model.predict(X_train)
            y_pred_test = model.predict(X_test)
            
            # Evaluate
            train_accuracy = accuracy_score(y_train, y_pred_train)
            test_accuracy = accuracy_score(y_test, y_pred_test)
            
            # Cross-validation
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
            
            classification_results[name] = {
                'model': model,
                'train_accuracy': train_accuracy,
                'test_accuracy': test_accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'predictions': y_pred_test
            }
            
            print(f"  Train Accuracy: {train_accuracy:.4f}")
            print(f"  Test Accuracy: {test_accuracy:.4f}")
            print(f"  CV Score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        
        # Find best model
        best_model_name = max(classification_results.keys(), 
                            key=lambda x: classification_results[x]['test_accuracy'])
        self.best_classifier = classification_results[best_model_name]['model']
        
        print(f"\n🏆 Best Classification Model: {best_model_name}")
        print(f"Test Accuracy: {classification_results[best_model_name]['test_accuracy']:.4f}")
        
        return classification_results
    
    def train_regression_models(self, X_train, y_train, X_test, y_test):
        """Train and evaluate regression models"""
        print("\n💧 Training Regression Models (Irrigation Amount)...")
        
        regression_results = {}
        
        for name, model in self.regression_models.items():
            print(f"\n📊 Training {name}...")
            
            # Train model
            model.fit(X_train, y_train)
            
            # Predictions
            y_pred_train = model.predict(X_train)
            y_pred_test = model.predict(X_test)
            
            # Evaluate
            train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
            test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
            train_r2 = r2_score(y_train, y_pred_train)
            test_r2 = r2_score(y_test, y_pred_test)
            
            # Cross-validation
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='neg_mean_squared_error')
            cv_rmse = np.sqrt(-cv_scores)
            
            regression_results[name] = {
                'model': model,
                'train_rmse': train_rmse,
                'test_rmse': test_rmse,
                'train_r2': train_r2,
                'test_r2': test_r2,
                'cv_rmse_mean': cv_rmse.mean(),
                'cv_rmse_std': cv_rmse.std(),
                'predictions': y_pred_test
            }
            
            print(f"  Train RMSE: {train_rmse:.4f}")
            print(f"  Test RMSE: {test_rmse:.4f}")
            print(f"  Test R²: {test_r2:.4f}")
            print(f"  CV RMSE: {cv_rmse.mean():.4f} (+/- {cv_rmse.std() * 2:.4f})")
        
        # Find best model
        best_model_name = max(regression_results.keys(), 
                            key=lambda x: regression_results[x]['test_r2'])
        self.best_regressor = regression_results[best_model_name]['model']
        
        print(f"\n🏆 Best Regression Model: {best_model_name}")
        print(f"Test R²: {regression_results[best_model_name]['test_r2']:.4f}")
        print(f"Test RMSE: {regression_results[best_model_name]['test_rmse']:.4f}")
        
        return regression_results
    
    def analyze_feature_importance(self, X_train):
        """Analyze feature importance from the best models"""
        print("\n🔍 Analyzing Feature Importance...")
        
        feature_importance = {}
        
        # Get feature importance from Random Forest (if it's the best model)
        if hasattr(self.best_classifier, 'feature_importances_'):
            importance_scores = self.best_classifier.feature_importances_
            feature_names = X_train.columns
            
            feature_importance = dict(zip(feature_names, importance_scores))
            
            # Sort by importance
            sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
            
            print("🔝 Top 10 Most Important Features:")
            for i, (feature, importance) in enumerate(sorted_features[:10]):
                print(f"{i+1:2d}. {feature}: {importance:.4f}")
            
            self.feature_importance = feature_importance
        
        return feature_importance
    
    def predict_irrigation(self, X):
        """Make irrigation predictions using the best models"""
        if not self.is_trained:
            raise ValueError("Models must be trained before making predictions")
        
        # Predict irrigation need (classification)
        irrigation_needed = self.best_classifier.predict(X)
        irrigation_probability = self.best_classifier.predict_proba(X)[:, 1]
        
        # Predict irrigation amount (regression)
        irrigation_amount = self.best_regressor.predict(X)
        
        # Ensure non-negative irrigation amounts
        irrigation_amount = np.maximum(0, irrigation_amount)
        
        # If irrigation is not needed, set amount to 0
        irrigation_amount = np.where(irrigation_needed == 0, 0, irrigation_amount)
        
        return {
            'irrigation_needed': irrigation_needed,
            'irrigation_probability': irrigation_probability,
            'irrigation_amount_mm': irrigation_amount
        }
    
    def save_models(self, filepath_prefix="models/irrigation_model"):
        """Save the trained models"""
        Path(filepath_prefix).parent.mkdir(parents=True, exist_ok=True)
        
        # Save best models
        joblib.dump(self.best_classifier, f"{filepath_prefix}_classifier.pkl")
        joblib.dump(self.best_regressor, f"{filepath_prefix}_regressor.pkl")
        
        # Save feature importance
        if self.feature_importance:
            joblib.dump(self.feature_importance, f"{filepath_prefix}_feature_importance.pkl")
        
        print(f"✅ Models saved with prefix: {filepath_prefix}")
    
    def load_models(self, filepath_prefix="models/irrigation_model"):
        """Load trained models"""
        try:
            self.best_classifier = joblib.load(f"{filepath_prefix}_classifier.pkl")
            self.best_regressor = joblib.load(f"{filepath_prefix}_regressor.pkl")
            
            # Load feature importance if available
            try:
                self.feature_importance = joblib.load(f"{filepath_prefix}_feature_importance.pkl")
            except:
                pass
            
            self.is_trained = True
            print(f"✅ Models loaded from: {filepath_prefix}")
            return True
        except Exception as e:
            print(f"❌ Error loading models: {e}")
            return False
    
    def train_complete_pipeline(self, X_train, X_test, y_class_train, y_class_test, y_reg_train, y_reg_test):
        """Train the complete irrigation prediction pipeline"""
        print("🚀 Training Complete Irrigation Prediction Pipeline")
        print("=" * 60)
        
        # Initialize models
        self.initialize_models()
        
        # Train classification models
        classification_results = self.train_classification_models(
            X_train, y_class_train, X_test, y_class_test
        )
        
        # Train regression models
        regression_results = self.train_regression_models(
            X_train, y_reg_train, X_test, y_reg_test
        )
        
        # Analyze feature importance
        self.analyze_feature_importance(X_train)
        
        # Mark as trained
        self.is_trained = True
        
        # Save models
        self.save_models()
        
        print("\n✅ Training pipeline completed successfully!")
        
        return classification_results, regression_results

def main():
    """Main function to train irrigation prediction models"""
    print("🌾 Advanced Irrigation Prediction Model Training")
    print("=" * 60)
    
    # Initialize preprocessor and load data
    preprocessor = IrrigationDataPreprocessor()
    df = preprocessor.load_data()
    
    if df is None:
        print("❌ Failed to load dataset")
        return
    
    # Preprocess data
    X, y_class, y_reg = preprocessor.fit_transform(df)
    
    # Split data
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_class_train, y_class_test, y_reg_train, y_reg_test = train_test_split(
        X, y_class, y_reg, test_size=0.2, random_state=42, stratify=y_class
    )
    
    # Initialize and train models
    predictor = IrrigationPredictor()
    classification_results, regression_results = predictor.train_complete_pipeline(
        X_train, X_test, y_class_train, y_class_test, y_reg_train, y_reg_test
    )
    
    # Test predictions on a sample
    print("\n🧪 Testing Predictions on Sample Data:")
    sample_predictions = predictor.predict_irrigation(X_test[:5])
    
    for i in range(5):
        print(f"Sample {i+1}:")
        print(f"  Irrigation Needed: {sample_predictions['irrigation_needed'][i]}")
        print(f"  Irrigation Probability: {sample_predictions['irrigation_probability'][i]:.3f}")
        print(f"  Irrigation Amount: {sample_predictions['irrigation_amount_mm'][i]:.2f} mm")
        print(f"  Actual Need: {y_class_test.iloc[i]}")
        print(f"  Actual Amount: {y_reg_test.iloc[i]:.2f} mm")
        print()
    
    print("🎉 Model training and testing completed successfully!")

if __name__ == "__main__":
    main()
