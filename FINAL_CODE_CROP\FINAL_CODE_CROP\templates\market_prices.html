<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('home') }}">
                <i class="fas fa-seedling text-success me-2" style="font-size: 24px;"></i>
                <span class="fw-bold" style="font-size: 22px;">AgroPro</span>
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-12">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-5 fw-bold text-success">
                        <i class="fas fa-chart-line me-3"></i>Live Crop Market Prices
                    </h1>
                    <p class="lead text-muted">Real-time market prices from major agricultural markets across India</p>
                    <div class="badge bg-success fs-6">
                        <i class="fas fa-sync-alt me-1"></i>Last Updated: <span id="lastUpdate">{{ moment().format('DD MMM YYYY, HH:mm') if moment else 'Just now' }}</span>
                    </div>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Market Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-seedling fa-2x mb-2"></i>
                                <h5>Total Crops</h5>
                                <h3 id="totalCrops">{{ prices|length if prices else 0 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-up fa-2x mb-2"></i>
                                <h5>Price Gainers</h5>
                                <h3 id="priceGainers">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-down fa-2x mb-2"></i>
                                <h5>Price Decliners</h5>
                                <h3 id="priceDecliners">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-store fa-2x mb-2"></i>
                                <h5>Active Markets</h5>
                                <h3>25+</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Filters and Price Alerts -->
                    <div class="col-lg-4 mb-4">
                        <!-- Filters -->
                        <div class="card border-0 shadow-lg">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters & Search</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="cropFilter" class="form-label fw-bold">
                                        <i class="fas fa-seedling text-success me-1"></i>Crop
                                    </label>
                                    <select class="form-select" id="cropFilter" onchange="filterPrices()">
                                        <option value="all">All Crops</option>
                                        <option value="rice">Rice</option>
                                        <option value="wheat">Wheat</option>
                                        <option value="corn">Corn</option>
                                        <option value="cotton">Cotton</option>
                                        <option value="sugarcane">Sugarcane</option>
                                        <option value="tomato">Tomato</option>
                                        <option value="potato">Potato</option>
                                        <option value="onion">Onion</option>
                                        <option value="soybean">Soybean</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="stateFilter" class="form-label fw-bold">
                                        <i class="fas fa-map-marker-alt text-danger me-1"></i>State
                                    </label>
                                    <select class="form-select" id="stateFilter" onchange="filterPrices()">
                                        <option value="all">All States</option>
                                        <option value="punjab">Punjab</option>
                                        <option value="haryana">Haryana</option>
                                        <option value="uttar pradesh">Uttar Pradesh</option>
                                        <option value="maharashtra">Maharashtra</option>
                                        <option value="karnataka">Karnataka</option>
                                        <option value="tamil nadu">Tamil Nadu</option>
                                        <option value="gujarat">Gujarat</option>
                                    </select>
                                </div>

                                <button class="btn btn-success w-100 mb-3" onclick="refreshPrices()">
                                    <i class="fas fa-sync-alt me-2"></i>Refresh Prices
                                </button>
                            </div>
                        </div>

                        <!-- Price Alerts -->
                        <div class="card mt-4 border-0 shadow-lg">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Set Price Alert</h5>
                            </div>
                            <div class="card-body">
                                <form action="{{ url_for('create_price_alert') }}" method="POST">
                                    <div class="mb-3">
                                        <label for="alertCrop" class="form-label fw-bold">Crop</label>
                                        <select class="form-select" id="alertCrop" name="crop" required>
                                            <option value="">Select Crop</option>
                                            <option value="Rice">Rice</option>
                                            <option value="Wheat">Wheat</option>
                                            <option value="Corn">Corn</option>
                                            <option value="Cotton">Cotton</option>
                                            <option value="Tomato">Tomato</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="targetPrice" class="form-label fw-bold">Target Price (₹/Quintal)</label>
                                        <input type="number" class="form-control" id="targetPrice" name="target_price" 
                                               placeholder="e.g., 2000" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="alertType" class="form-label fw-bold">Alert When Price</label>
                                        <select class="form-select" id="alertType" name="alert_type" required>
                                            <option value="above">Goes Above Target</option>
                                            <option value="below">Goes Below Target</option>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn btn-warning w-100">
                                        <i class="fas fa-plus me-2"></i>Create Alert
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Top Movers -->
                        <div class="card mt-4 border-0 shadow-sm">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Movers</h6>
                            </div>
                            <div class="card-body">
                                <div id="topMovers">
                                    <!-- Will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Live Prices Table -->
                    <div class="col-lg-8 mb-4">
                        <div class="card border-0 shadow-lg">
                            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Live Market Prices</h5>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-light text-dark me-2">Auto-refresh: 30s</span>
                                    <div class="spinner-border spinner-border-sm text-light" role="status" id="loadingSpinner" style="display: none;">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0" id="pricesTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th><i class="fas fa-seedling me-1"></i>Crop</th>
                                                <th><i class="fas fa-map-marker-alt me-1"></i>State</th>
                                                <th><i class="fas fa-store me-1"></i>Market</th>
                                                <th><i class="fas fa-rupee-sign me-1"></i>Current Price</th>
                                                <th><i class="fas fa-chart-line me-1"></i>Change</th>
                                                <th><i class="fas fa-clock me-1"></i>Updated</th>
                                            </tr>
                                        </thead>
                                        <tbody id="pricesTableBody">
                                            {% for price in prices %}
                                            <tr>
                                                <td>
                                                    <strong>{{ price.crop }}</strong>
                                                </td>
                                                <td>{{ price.state }}</td>
                                                <td>{{ price.market }}</td>
                                                <td class="fw-bold text-primary">₹{{ price.current_price }}</td>
                                                <td>
                                                    {% if price.change > 0 %}
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-arrow-up me-1"></i>+₹{{ price.change }} (+{{ price.change_percent }}%)
                                                        </span>
                                                    {% elif price.change < 0 %}
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-arrow-down me-1"></i>₹{{ price.change }} ({{ price.change_percent }}%)
                                                        </span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">
                                                            <i class="fas fa-minus me-1"></i>No Change
                                                        </span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <small class="text-muted">{{ price.last_updated }}</small>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Price Trends Chart -->
                <div class="card mt-4 border-0 shadow-lg">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Price Trends (Last 7 Days)</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="priceTrendsChart" width="800" height="300"></canvas>
                    </div>
                </div>

                <!-- Market Information -->
                <div class="card mt-4 border-0 shadow-sm">
                    <div class="card-header bg-dark text-white">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Market Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>Data Sources</h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success me-1"></i> Agmarknet (Government of India)</li>
                                    <li><i class="fas fa-check text-success me-1"></i> State Agricultural Marketing Boards</li>
                                    <li><i class="fas fa-check text-success me-1"></i> Major Wholesale Markets</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>Update Frequency</h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-clock text-primary me-1"></i> Real-time during market hours</li>
                                    <li><i class="fas fa-clock text-info me-1"></i> Daily price updates</li>
                                    <li><i class="fas fa-clock text-warning me-1"></i> Historical data available</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>Price Units</h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-weight text-success me-1"></i> Prices shown per Quintal (100 kg)</li>
                                    <li><i class="fas fa-rupee-sign text-primary me-1"></i> All prices in Indian Rupees (₹)</li>
                                    <li><i class="fas fa-info text-warning me-1"></i> Prices may vary by quality grade</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        let pricesData = {{ prices | tojson }};
        let priceTrendsChart;

        // Initialize price trends chart
        function initPriceTrendsChart() {
            const ctx = document.getElementById('priceTrendsChart').getContext('2d');
            
            // Sample trend data for demonstration
            const labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'];
            
            priceTrendsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Rice',
                        data: [1850, 1860, 1840, 1870, 1880, 1875, 1890],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Wheat',
                        data: [2100, 2110, 2090, 2120, 2130, 2125, 2140],
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Cotton',
                        data: [5200, 5180, 5220, 5250, 5240, 5260, 5280],
                        borderColor: '#17a2b8',
                        backgroundColor: 'rgba(23, 162, 184, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Price (₹/Quintal)'
                            }
                        }
                    }
                }
            });
        }

        // Filter prices based on selected criteria
        function filterPrices() {
            const cropFilter = document.getElementById('cropFilter').value;
            const stateFilter = document.getElementById('stateFilter').value;
            
            // Show loading spinner
            document.getElementById('loadingSpinner').style.display = 'inline-block';
            
            // Simulate API call delay
            setTimeout(() => {
                fetch(`/api/market_prices?crop=${cropFilter}&state=${stateFilter}`)
                    .then(response => response.json())
                    .then(data => {
                        updatePricesTable(data);
                        updateSummaryCards(data);
                        updateTopMovers(data);
                        document.getElementById('loadingSpinner').style.display = 'none';
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('loadingSpinner').style.display = 'none';
                    });
            }, 500);
        }

        // Refresh prices
        function refreshPrices() {
            filterPrices();
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
        }

        // Update prices table
        function updatePricesTable(prices) {
            const tbody = document.getElementById('pricesTableBody');
            tbody.innerHTML = '';
            
            prices.forEach(price => {
                const row = document.createElement('tr');
                
                let changeHtml = '';
                if (price.change > 0) {
                    changeHtml = `<span class="badge bg-success"><i class="fas fa-arrow-up me-1"></i>+₹${price.change} (+${price.change_percent}%)</span>`;
                } else if (price.change < 0) {
                    changeHtml = `<span class="badge bg-danger"><i class="fas fa-arrow-down me-1"></i>₹${price.change} (${price.change_percent}%)</span>`;
                } else {
                    changeHtml = `<span class="badge bg-secondary"><i class="fas fa-minus me-1"></i>No Change</span>`;
                }
                
                row.innerHTML = `
                    <td><strong>${price.crop}</strong></td>
                    <td>${price.state}</td>
                    <td>${price.market}</td>
                    <td class="fw-bold text-primary">₹${price.current_price}</td>
                    <td>${changeHtml}</td>
                    <td><small class="text-muted">${price.last_updated}</small></td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // Update summary cards
        function updateSummaryCards(prices) {
            document.getElementById('totalCrops').textContent = prices.length;
            
            const gainers = prices.filter(p => p.change > 0).length;
            const decliners = prices.filter(p => p.change < 0).length;
            
            document.getElementById('priceGainers').textContent = gainers;
            document.getElementById('priceDecliners').textContent = decliners;
        }

        // Update top movers
        function updateTopMovers(prices) {
            const topMovers = document.getElementById('topMovers');
            
            // Sort by absolute change percentage
            const sortedPrices = prices.sort((a, b) => Math.abs(b.change_percent) - Math.abs(a.change_percent));
            const topThree = sortedPrices.slice(0, 3);
            
            topMovers.innerHTML = '';
            topThree.forEach((price, index) => {
                const color = price.change > 0 ? 'success' : price.change < 0 ? 'danger' : 'secondary';
                const icon = price.change > 0 ? 'arrow-up' : price.change < 0 ? 'arrow-down' : 'minus';
                
                topMovers.innerHTML += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>${price.crop}</strong><br>
                            <small class="text-muted">${price.state}</small>
                        </div>
                        <span class="badge bg-${color}">
                            <i class="fas fa-${icon} me-1"></i>${price.change_percent}%
                        </span>
                    </div>
                `;
            });
        }

        // Auto-refresh prices every 30 seconds
        setInterval(refreshPrices, 30000);

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initPriceTrendsChart();
            updateSummaryCards(pricesData);
            updateTopMovers(pricesData);
        });
    </script>
</body>
</html>
