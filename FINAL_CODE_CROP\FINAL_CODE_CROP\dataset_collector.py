"""
Pest and Weed Dataset Collection System
This module handles downloading, organizing, and preprocessing datasets for training
"""

import os
import requests
import zipfile
import pandas as pd
import numpy as np
from PIL import Image
import shutil
import json
from urllib.parse import urlparse
import time
import random
from pathlib import Path

class PestDatasetCollector:
    def __init__(self, base_path="datasets"):
        self.base_path = Path(base_path)
        self.setup_directories()
        
    def setup_directories(self):
        """Create the dataset directory structure"""
        directories = [
            "pests/aphids",
            "pests/whiteflies", 
            "pests/spider_mites",
            "pests/thrips",
            "pests/caterpillars",
            "pests/beetles",
            "pests/leafhoppers",
            "pests/scale_insects",
            "weeds/dandelion",
            "weeds/crabgrass",
            "weeds/pigweed",
            "weeds/lambsquarters",
            "weeds/chickweed",
            "weeds/purslane",
            "weeds/bindweed",
            "weeds/plantain",
            "healthy/crops",
            "raw_downloads",
            "processed",
            "annotations"
        ]
        
        for directory in directories:
            (self.base_path / directory).mkdir(parents=True, exist_ok=True)
            
    def download_plantvillage_dataset(self):
        """Download the PlantVillage dataset"""
        print("Downloading PlantVillage dataset...")
        
        # PlantVillage dataset URL (example - replace with actual URL)
        url = "https://github.com/spMohanty/PlantVillage-Dataset/archive/master.zip"
        
        try:
            response = requests.get(url, stream=True)
            zip_path = self.base_path / "raw_downloads" / "plantvillage.zip"
            
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    
            # Extract the zip file
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.base_path / "raw_downloads")
                
            print("PlantVillage dataset downloaded successfully!")
            return True
            
        except Exception as e:
            print(f"Error downloading PlantVillage dataset: {e}")
            return False
    
    def download_ip102_dataset(self):
        """Download the IP102 insect pest dataset"""
        print("IP102 dataset requires manual download from:")
        print("https://github.com/xpwu95/IP102")
        print("Please download and place in raw_downloads/IP102/")
        
        # Check if already downloaded
        ip102_path = self.base_path / "raw_downloads" / "IP102"
        if ip102_path.exists():
            print("IP102 dataset found!")
            return True
        return False
    
    def create_sample_dataset(self):
        """Create a sample dataset structure with placeholder data"""
        print("Creating sample dataset structure...")
        
        pest_categories = {
            "aphids": {
                "description": "Small, soft-bodied insects that feed on plant sap",
                "treatment": "Neem oil spray or insecticidal soap",
                "severity_levels": ["low", "medium", "high"]
            },
            "whiteflies": {
                "description": "Small white flying insects that damage leaves",
                "treatment": "Yellow sticky traps and neem oil application",
                "severity_levels": ["low", "medium", "high"]
            },
            "spider_mites": {
                "description": "Tiny mites that cause stippling on leaves",
                "treatment": "Increase humidity and use miticide spray",
                "severity_levels": ["low", "medium", "high"]
            },
            "thrips": {
                "description": "Small, slender insects that cause silver streaks",
                "treatment": "Blue sticky traps and predatory mites",
                "severity_levels": ["low", "medium", "high"]
            }
        }
        
        weed_categories = {
            "dandelion": {
                "description": "Perennial weed with yellow flowers",
                "treatment": "Hand pulling or selective herbicide",
                "growth_stages": ["seedling", "rosette", "flowering"]
            },
            "crabgrass": {
                "description": "Annual grass weed that spreads rapidly",
                "treatment": "Pre-emergent herbicide in spring",
                "growth_stages": ["seedling", "tillering", "mature"]
            }
        }
        
        # Save metadata
        metadata = {
            "pests": pest_categories,
            "weeds": weed_categories,
            "created_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_categories": len(pest_categories) + len(weed_categories)
        }
        
        with open(self.base_path / "annotations" / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
            
        print("Sample dataset structure created!")
        return True
    
    def preprocess_images(self, source_dir, target_dir, image_size=(224, 224)):
        """Preprocess images for training"""
        print(f"Preprocessing images from {source_dir} to {target_dir}...")
        
        source_path = Path(source_dir)
        target_path = Path(target_dir)
        target_path.mkdir(parents=True, exist_ok=True)
        
        processed_count = 0
        
        for image_file in source_path.glob("**/*.jpg"):
            try:
                with Image.open(image_file) as img:
                    # Convert to RGB if needed
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Resize image
                    img = img.resize(image_size, Image.Resampling.LANCZOS)
                    
                    # Save processed image
                    output_path = target_path / f"processed_{processed_count:05d}.jpg"
                    img.save(output_path, 'JPEG', quality=95)
                    
                    processed_count += 1
                    
            except Exception as e:
                print(f"Error processing {image_file}: {e}")
                
        print(f"Processed {processed_count} images")
        return processed_count
    
    def create_training_splits(self, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
        """Create training, validation, and test splits"""
        print("Creating training splits...")
        
        splits = {
            "train": train_ratio,
            "validation": val_ratio,
            "test": test_ratio
        }
        
        # Create split directories
        for split in splits.keys():
            for category in ["pests", "weeds", "healthy"]:
                (self.base_path / "processed" / split / category).mkdir(parents=True, exist_ok=True)
        
        # Process each category
        for category_type in ["pests", "weeds"]:
            category_path = self.base_path / category_type
            
            for pest_type in category_path.iterdir():
                if pest_type.is_dir():
                    images = list(pest_type.glob("*.jpg"))
                    random.shuffle(images)
                    
                    n_total = len(images)
                    n_train = int(n_total * train_ratio)
                    n_val = int(n_total * val_ratio)
                    
                    # Split images
                    train_images = images[:n_train]
                    val_images = images[n_train:n_train + n_val]
                    test_images = images[n_train + n_val:]
                    
                    # Copy images to respective directories
                    for split, image_list in zip(["train", "validation", "test"], 
                                               [train_images, val_images, test_images]):
                        split_dir = self.base_path / "processed" / split / category_type / pest_type.name
                        split_dir.mkdir(parents=True, exist_ok=True)
                        
                        for img in image_list:
                            shutil.copy2(img, split_dir / img.name)
        
        print("Training splits created successfully!")
        return True
    
    def generate_annotation_file(self):
        """Generate annotation file for training"""
        print("Generating annotation file...")
        
        annotations = []
        class_mapping = {}
        class_id = 0
        
        for category_type in ["pests", "weeds"]:
            category_path = self.base_path / "processed" / "train" / category_type
            
            if category_path.exists():
                for pest_dir in category_path.iterdir():
                    if pest_dir.is_dir():
                        class_name = f"{category_type}_{pest_dir.name}"
                        class_mapping[class_name] = class_id
                        
                        for img_file in pest_dir.glob("*.jpg"):
                            annotations.append({
                                "image_path": str(img_file.relative_to(self.base_path)),
                                "class_name": class_name,
                                "class_id": class_id,
                                "category": category_type
                            })
                        
                        class_id += 1
        
        # Save annotations
        with open(self.base_path / "annotations" / "train_annotations.json", 'w') as f:
            json.dump(annotations, f, indent=2)
            
        with open(self.base_path / "annotations" / "class_mapping.json", 'w') as f:
            json.dump(class_mapping, f, indent=2)
            
        print(f"Generated annotations for {len(annotations)} images and {len(class_mapping)} classes")
        return annotations, class_mapping
    
    def download_sample_images(self):
        """Download sample images for demonstration"""
        print("Note: This is a demonstration function.")
        print("In a real implementation, you would:")
        print("1. Use APIs from iNaturalist, PlantNet, or other sources")
        print("2. Implement web scraping with proper rate limiting")
        print("3. Use academic dataset APIs")
        print("4. Partner with agricultural institutions")
        
        # Create some sample placeholder files
        sample_data = {
            "pests/aphids": 50,
            "pests/whiteflies": 50,
            "pests/spider_mites": 50,
            "weeds/dandelion": 50,
            "weeds/crabgrass": 50,
            "healthy/crops": 50
        }
        
        for path, count in sample_data.items():
            directory = self.base_path / path
            directory.mkdir(parents=True, exist_ok=True)
            
            # Create placeholder text files (in real implementation, these would be actual images)
            for i in range(count):
                placeholder_file = directory / f"sample_{i:03d}.txt"
                with open(placeholder_file, 'w') as f:
                    f.write(f"Placeholder for {path} image {i}")
        
        print("Sample dataset structure created!")
        return True

def main():
    """Main function to run the dataset collection process"""
    print("Starting Pest and Weed Dataset Collection System")
    print("=" * 50)
    
    collector = PestDatasetCollector()
    
    # Step 1: Create sample dataset structure
    collector.create_sample_dataset()
    
    # Step 2: Download sample images (placeholder)
    collector.download_sample_images()
    
    # Step 3: Create training splits
    # collector.create_training_splits()  # Uncomment when you have actual images
    
    # Step 4: Generate annotations
    # collector.generate_annotation_file()  # Uncomment when you have actual images
    
    print("\nDataset collection setup complete!")
    print("Next steps:")
    print("1. Download real datasets (PlantVillage, IP102)")
    print("2. Run preprocessing pipeline")
    print("3. Train the ML model")
    print("4. Integrate with your Flask app")

if __name__ == "__main__":
    main()
