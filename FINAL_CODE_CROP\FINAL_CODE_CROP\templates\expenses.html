{% extends "layout.html" %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-success text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="mb-0">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Farm Expense Management
                            </h3>
                            <p class="mb-0 opacity-8">Track and manage your farm expenses efficiently</p>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                                <i class="fas fa-plus me-2"></i>Add Expense
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Expense Summary -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>₹{{ total_expense or 0 }}</h4>
                                    <p class="mb-0">Total This Month</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>{{ expenses|length }}</h4>
                                    <p class="mb-0">Total Entries</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>₹{{ (total_expense / 30) | round(2) if total_expense else 0 }}</h4>
                                    <p class="mb-0">Daily Average</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Expenses -->
                    <h5 class="mb-3">Recent Expenses</h5>
                    {% if expenses %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Category</th>
                                    <th>Description</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for expense in expenses %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ expense[0] }}</span>
                                    </td>
                                    <td>{{ expense[1] }}</td>
                                    <td class="text-success fw-bold">₹{{ expense[2] }}</td>
                                    <td>{{ expense[3] }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No expenses recorded yet</h5>
                        <p class="text-muted">Start tracking your farm expenses by adding your first entry</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Expense Modal -->
<div class="modal fade" id="addExpenseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">Add New Expense</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_expense') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Category</label>
                        <select class="form-select" name="category" required>
                            <option value="">Select Category</option>
                            <option value="Seeds">Seeds</option>
                            <option value="Fertilizers">Fertilizers</option>
                            <option value="Pesticides">Pesticides</option>
                            <option value="Equipment">Equipment</option>
                            <option value="Labor">Labor</option>
                            <option value="Fuel">Fuel</option>
                            <option value="Irrigation">Irrigation</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <input type="text" class="form-control" name="description" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Amount (₹)</label>
                        <input type="number" class="form-control" name="amount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Date</label>
                        <input type="date" class="form-control" name="expense_date" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Add Expense</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 15px;
}
.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}
.table-hover tbody tr:hover {
    background-color: rgba(40, 167, 69, 0.1);
}
</style>
{% endblock %}
