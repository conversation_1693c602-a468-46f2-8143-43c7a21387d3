<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title>AgroPro - Login & Signup</title>
        <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.css"></link> -->
        <!-- Favicon-->
        <!-- Font Awesome icons (free version)-->
        <script src="https://use.fontawesome.com/releases/v5.15.3/js/all.js" crossorigin="anonymous"></script>
        <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700" rel="stylesheet" type="text/css" />
        <link href="https://fonts.googleapis.com/css?family=Roboto+Slab:400,100,300,700" rel="stylesheet" type="text/css" />
        <!-- Core theme CSS (includes Bootstrap)-->
        <link href="http://127.0.0.1:5000/static/css/styles.css" rel="stylesheet" />
        <style>
            body {
                background-image: linear-gradient(rgba(44, 90, 39, 0.4), rgba(74, 124, 89, 0.4)), 
                                  url('/static/images/Background5 (2).jpg');
                background-size: cover;
                background-position: center;
                background-attachment: fixed;
                background-repeat: no-repeat;
                min-height: 100vh;
            }
            
           #form1, #form2{
                display: none;
                width: 50%;
                margin: auto;
                /* border: 1px solid #ddd; */
                padding: 25px;
                background-color: rgba(0, 0, 0, 0.70);

            }
            #form1.active, #form2.active{
                    display: block;
            }
        </style>
    </head>
    <body id="page-top">
        <!-- Navigation-->
        <nav class="navbar navbar-expand-lg navbar-dark fixed-top" id="mainNav">
            <div class="container">
                <!-- Navigation-->
                <a class="navbar-brand h1" href="/">AgroPro</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
                    Menu
                    <i class="fas fa-bars ms-1"></i>
                </button>
                <div class="collapse navbar-collapse" id="navbarResponsive">
                    <ul class="navbar-nav text-uppercase ms-auto py-4 py-lg-0">
                        <li><a data-value="#form1" onclick="toggleform(event)" class="nav-link">Signin</a></li>
                        <li><a data-value="#form2" onclick="toggleform(event)" class="nav-link">Signup</a></li>
                    </ul>
                </div>
            </div>
        </nav>
        {% if msg %}
        <div class="container mt-3">
          <div class="alert alert-{{ error_type or 'info' }} alert-dismissible fade show" role="alert">
            {{ msg }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        </div>
        {% endif %}
        <!-- Masthead-->
        <header class="masthead">
            <div class="container">
                <!-- Tab buttons -->
                <div class="text-center mb-4">
                    <button type="button" class="btn btn-primary me-2" data-value="#form1" onclick="toggleform(event)">Login</button>
                    <button type="button" class="btn btn-secondary" data-value="#form2" onclick="toggleform(event)">Signup</button>
                </div>

                <form  method="post" action="{{ url_for('userlog')}}" id="form1" class="active">
                    <h2>User Login</h2>
                    <hr>
                    <div class="form-group">
                      <label for="username">Username:</label>
                      <input type="text" class="form-control" id="username" placeholder="Enter username" name="name">
                    </div><br>
                    <div class="form-group">
                      <label for="password">Password:</label>
                      <input type="password" class="form-control" id="password" placeholder="Enter password" name="password">
                    </div><br>
                    <button type="submit" class="btn btn-success">Submit</button></br></br>
                  </form>
              
                  
                  <form method="post" action="{{ url_for('userreg')}}" id="form2">
                    <h2>User Registration</h2>
                    <hr>
                    <div class="form-group">
                      <label for="username">Username:</label>
                      <input type="text" class="form-control" id="username" placeholder="Enter username" name="name">
                    </div><br>
                    <div class="form-group">
                      <label for="email">Email:</label>
                      <input type="email" class="form-control" id="email" placeholder="Enter email" name="email">
                    </div><br>
                    <div class="form-group">
                      <label for="phone">Mobile No.:</label>
                      <input type="tel" class="form-control" id="phone" placeholder="Enter mobile number" name="phone">
                    </div><br>
                    <div class="form-group">
                      <label for="password">Password:</label>
                      <input type="password" class="form-control" id="password" placeholder="Enter password" name="password">
                    </div><br>
                    <button type="submit" class="btn btn-success">Submit</button></br></br>
                  </form>
            </div>
        </header>
        <!-- Bootstrap core JS-->
        <script src="https://cdn.startbootstrap.com/sb-forms-latest.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.0.3/css/font-awesome.css"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.bundle.min.js"></script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.bundle.min.js"></script>
        <!-- Core theme JS-->
        <script src="http://127.0.0.1:5000/static/js/scripts.js"></script>
        <script>
            function toggleform(e) {
            var Id =(e.target.getAttribute('data-value'))
            let Items= ['#form1', '#form2'];
            Items.map(function(item) {
                if(Id === item ) {
                    $(item).addClass("active");
                }
                else {
                $(item).removeClass("active");
                }
            })
        }
        
        </script>
    </body>
</html>
