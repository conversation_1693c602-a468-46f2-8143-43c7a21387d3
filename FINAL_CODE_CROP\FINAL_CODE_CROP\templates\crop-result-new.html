{% extends 'layout.html' %} {% block body %}

<style>
  /* Override layout wrapper constraints */
  .content-wrapper {
    min-height: 100vh !important;
    padding-top: 0 !important;
    margin: 0 !important;
  }
  
  html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-family: 'Arial', sans-serif;
  }
  
  .fullscreen-container {
    min-height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
    margin-left: -15px;
    margin-right: -15px;
    margin-top: -2rem;
  }
  
  .fullscreen-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(46, 204, 113, 0.05) 0%, transparent 25%),
      radial-gradient(circle at 75% 75%, rgba(52, 152, 219, 0.05) 0%, transparent 25%),
      radial-gradient(circle at 50% 10%, rgba(155, 89, 182, 0.03) 0%, transparent 25%);
    background-size: 300px 300px, 400px 400px, 200px 200px;
  }
  
  .result-card {
    background: #ffffff;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 900px;
    width: 100%;
    position: relative;
    z-index: 2;
    border: 1px solid #e9ecef;
  }
  
  .crop-title {
    font-size: 3rem;
    font-weight: bold;
    color: #2c5a27;
    text-transform: uppercase;
    margin: 20px 0;
    font-family: 'Poppins', sans-serif;
  }
  
  .confidence-badge {
    display: inline-block;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1.2rem;
    font-weight: bold;
    margin: 20px 0;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
  }
  
  .high-confidence {
    background: #28a745;
    color: white;
  }
  
  .medium-confidence {
    background: #ffc107;
    color: #212529;
  }
  
  .low-confidence {
    background: #dc3545;
    color: white;
  }
  
  .conditions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
  }
  
  .condition-item {
    background: #2c5a27;
    color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
  }
  
  .condition-item:hover {
    transform: translateY(-5px);
  }
  
  .condition-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
  }
  
  .condition-value {
    font-size: 1.4rem;
    font-weight: bold;
  }
  
  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 30px 0;
    flex-wrap: wrap;
  }
  
  .btn-modern {
    padding: 15px 40px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 10px;
  }
  
  .btn-primary {
    background: #2c5a27;
    color: white;
  }
  
  .btn-secondary {
    background: #6c757d;
    color: white;
  }
  
  .btn-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
  }
  
  .header-text {
    color: #2c5a27;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 30px;
    font-family: 'Poppins', sans-serif;
  }
  
  @media (max-width: 768px) {
    .crop-title {
      font-size: 2.5rem;
    }
    .header-text {
      font-size: 1.8rem;
    }
    .conditions-grid {
      grid-template-columns: 1fr;
    }
    .action-buttons {
      flex-direction: column;
      align-items: center;
    }
  }
</style>

<div class="fullscreen-container">
  <h1 class="header-text">🌾 AI CROP RECOMMENDATION RESULT</h1>
  
  <div class="result-card">
    <div class="crop-title">
      🌱 {{ prediction.title() }}
    </div>
    
    {% if confidence %}
    <div class="confidence-badge {% if confidence >= 80 %}high-confidence{% elif confidence >= 60 %}medium-confidence{% else %}low-confidence{% endif %}">
      <i class="fas fa-chart-line"></i> AI Confidence: {{ confidence }}%
      {% if confidence >= 80 %}
        - Highly Recommended
      {% elif confidence >= 60 %}
        - Good Match
      {% else %}
        - Consider Alternatives
      {% endif %}
    </div>
    {% endif %}

    <div style="margin: 30px 0;">
      <h3 style="color: #2c3e50; margin-bottom: 20px; font-size: 1.5rem;">
        <i class="fas fa-seedling"></i> Your Farm Conditions Analysis
      </h3>
      
      <div class="conditions-grid">
        <div class="condition-item">
          <div class="condition-label">Nitrogen (N)</div>
          <div class="condition-value">{{ nitrogen }} kg/ha</div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Phosphorous (P)</div>
          <div class="condition-value">{{ phosphorous }} kg/ha</div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Potassium (K)</div>
          <div class="condition-value">{{ potassium }} kg/ha</div>
        </div>
        <div class="condition-item">
          <div class="condition-label">pH Level</div>
          <div class="condition-value">{{ ph_level }}</div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Temperature</div>
          <div class="condition-value">{{ temperature }}°C</div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Humidity</div>
          <div class="condition-value">{{ humidity }}%</div>
        </div>
        <div class="condition-item">
          <div class="condition-label">Rainfall</div>
          <div class="condition-value">{{ rainfall }} mm</div>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <a href="/crop-recommend" class="btn-modern btn-primary">
        <i class="fas fa-redo"></i> Try Another Prediction
      </a>
      <a href="/index.html" class="btn-modern btn-secondary">
        <i class="fas fa-home"></i> Back to Dashboard
      </a>
    </div>
  </div>
</div>

{% endblock %}
