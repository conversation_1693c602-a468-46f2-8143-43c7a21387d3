#!/usr/bin/env python3
"""
Quick login test script
"""

import requests
import sqlite3

def test_login():
    """Test login functionality"""
    print("🔐 Testing Login Functionality")
    print("=" * 40)
    
    # Get a real user from database
    conn = sqlite3.connect('user_data.db')
    cursor = conn.cursor()
    cursor.execute('SELECT name, password FROM user LIMIT 1;')
    user_data = cursor.fetchone()
    conn.close()
    
    if not user_data:
        print("❌ No users found in database")
        return False
    
    username, password = user_data
    print(f"Testing with user: '{username}'")
    print(f"Testing with password: '{password}'")
    
    # Test login
    login_data = {
        'name': username,
        'password': password
    }
    
    try:
        # First, let's test the form submission directly
        session = requests.Session()
        
        # Get the login page first
        get_response = session.get('http://127.0.0.1:5000/userlog')
        print(f"GET /userlog status: {get_response.status_code}")
        
        # Now submit the login
        response = session.post('http://127.0.0.1:5000/userlog', 
                               data=login_data, 
                               timeout=10,
                               allow_redirects=False)  # Don't follow redirects
        
        print(f"POST Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        # Check for specific error messages
        response_text = response.text
        if 'Invalid username or password' in response_text:
            print("❌ Login failed - Invalid credentials message found")
            print("Checking if this is a database query issue...")
            
            # Let's manually check the database query
            conn = sqlite3.connect('user_data.db')
            cursor = conn.cursor()
            cursor.execute("SELECT name, email, mobile FROM user WHERE name = ? AND password = ?", 
                         (username, password))
            result = cursor.fetchone()
            conn.close()
            
            if result:
                print(f"✅ Database query works! Found: {result}")
                print("❌ But Flask app login is still failing")
            else:
                print("❌ Database query also failing")
                
        elif 'Please fill in all fields' in response_text:
            print("❌ Login failed - Missing fields")
        elif response.status_code == 302:  # Redirect means success
            location = response.headers.get('Location', '')
            print(f"✅ Login successful - redirected to: {location}")
            return True
        elif response.status_code == 200:
            print("❌ Login failed - Got 200 but no redirect")
            print("Response content preview:", response_text[:500])
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error during login test: {e}")
        return False
    
    return False

if __name__ == "__main__":
    success = test_login()
    print(f"\nResult: {'PASS' if success else 'FAIL'}")
