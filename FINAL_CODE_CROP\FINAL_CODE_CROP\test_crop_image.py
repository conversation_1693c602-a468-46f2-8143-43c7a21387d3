import requests
import time

# Test data for crop recommendation
test_data = {
    'nitrogen': 90,
    'phosphorous': 42,
    'pottasium': 43,
    'temp': 20.9,
    'hum': 82.0,
    'ph': 6.5,
    'rainfall': 202.9
}

try:
    print("🧪 Testing crop recommendation with sample data...")
    print(f"📊 Test data: {test_data}")
    
    # Submit POST request to crop prediction endpoint
    response = requests.post('http://127.0.0.1:5000/crop_predict', 
                           data=test_data, 
                           timeout=10)
    
    if response.status_code == 200:
        print("✅ Request successful!")
        print(f"📄 Response length: {len(response.text)} characters")
        
        # Check if crop image is in the response
        if 'crop-image' in response.text:
            print("🖼️ Crop image element found in response!")
        
        if '/static/images/' in response.text:
            print("📁 Static image path found in response!")
            
        # Extract crop name from response if possible
        if 'class="crop-name"' in response.text:
            import re
            crop_match = re.search(r'class="crop-name"[^>]*>([^<]+)', response.text)
            if crop_match:
                crop_name = crop_match.group(1).strip()
                print(f"🌾 Predicted crop: {crop_name}")
    else:
        print(f"❌ Request failed with status code: {response.status_code}")
        print(f"📝 Response: {response.text[:500]}...")

except requests.exceptions.RequestException as e:
    print(f"❌ Network error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
