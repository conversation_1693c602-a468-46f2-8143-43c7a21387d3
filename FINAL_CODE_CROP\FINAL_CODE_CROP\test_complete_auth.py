#!/usr/bin/env python3
"""
Complete login/registration test suite
"""

import requests
import sqlite3
import random
import string

def test_complete_auth_system():
    """Test complete authentication system"""
    print("🔐 Complete Authentication System Test")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    # Test 1: Registration
    print("\n📋 Test 1: User Registration")
    print("-" * 30)
    
    random_suffix = ''.join(random.choices(string.digits, k=4))
    test_user = {
        'name': f'testuser{random_suffix}',
        'email': f'test{random_suffix}@example.com',
        'phone': f'98765{random_suffix}',
        'password': 'testpass123'
    }
    
    try:
        response = requests.post('http://127.0.0.1:5000/userreg', 
                               data=test_user, timeout=10)
        
        if response.status_code == 200 and 'Successfully Registered' in response.text:
            print("✅ Registration successful")
            passed += 1
        else:
            print("❌ Registration failed")
            failed += 1
            
    except Exception as e:
        print(f"❌ Registration error: {e}")
        failed += 1
    
    # Test 2: Login with new user
    print("\n📋 Test 2: Login with New User")
    print("-" * 30)
    
    try:
        login_data = {
            'name': test_user['name'],
            'password': test_user['password']
        }
        
        response = requests.post('http://127.0.0.1:5000/userlog', 
                               data=login_data, timeout=10, allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ Login successful - redirected")
            passed += 1
        else:
            print("❌ Login failed")
            failed += 1
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        failed += 1
    
    # Test 3: Login with existing user
    print("\n📋 Test 3: Login with Existing User")
    print("-" * 30)
    
    try:
        # Get existing user from database
        conn = sqlite3.connect('user_data.db')
        cursor = conn.cursor()
        cursor.execute('SELECT name, password FROM user LIMIT 1;')
        existing_user = cursor.fetchone()
        conn.close()
        
        if existing_user:
            login_data = {
                'name': existing_user[0],
                'password': existing_user[1]
            }
            
            response = requests.post('http://127.0.0.1:5000/userlog', 
                                   data=login_data, timeout=10, allow_redirects=False)
            
            if response.status_code == 302:
                print("✅ Existing user login successful")
                passed += 1
            else:
                print("❌ Existing user login failed")
                failed += 1
        else:
            print("❌ No existing users found")
            failed += 1
            
    except Exception as e:
        print(f"❌ Existing user login error: {e}")
        failed += 1
    
    # Test 4: Invalid login
    print("\n📋 Test 4: Invalid Login Credentials")
    print("-" * 30)
    
    try:
        invalid_login = {
            'name': 'nonexistentuser',
            'password': 'wrongpassword'
        }
        
        response = requests.post('http://127.0.0.1:5000/userlog', 
                               data=invalid_login, timeout=10)
        
        if response.status_code == 200 and 'Invalid username or password' in response.text:
            print("✅ Invalid login properly rejected")
            passed += 1
        else:
            print("❌ Invalid login not properly handled")
            failed += 1
            
    except Exception as e:
        print(f"❌ Invalid login test error: {e}")
        failed += 1
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 AUTHENTICATION TEST SUMMARY")
    print("=" * 50)
    total_tests = passed + failed
    success_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 PERFECT! All authentication features working!")
    elif success_rate >= 75:
        print("👍 GOOD! Most authentication features working!")
    else:
        print("⚠️ NEEDS ATTENTION! Authentication issues found")
    
    return success_rate >= 75

if __name__ == "__main__":
    print("🚀 Starting Complete Authentication Test")
    
    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:5000", timeout=5)
        print("✅ Server is accessible")
    except:
        print("❌ Server is not accessible")
        exit(1)
    
    success = test_complete_auth_system()
    print(f"\n🎯 Overall Result: {'PASS' if success else 'FAIL'}")
