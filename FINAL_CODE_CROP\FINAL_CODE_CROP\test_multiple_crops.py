import requests

# Test data for different crops
test_scenarios = [
    {
        'name': 'Scenario 1 - Rice conditions',
        'data': {
            'nitrogen': 90, 'phosphorous': 42, 'pottasium': 43,
            'temp': 20.9, 'hum': 82.0, 'ph': 6.5, 'rainfall': 202.9
        }
    },
    {
        'name': 'Scenario 2 - Wheat conditions', 
        'data': {
            'nitrogen': 50, 'phosphorous': 32, 'pottasium': 30,
            'temp': 15.0, 'hum': 65.0, 'ph': 7.0, 'rainfall': 80.0
        }
    },
    {
        'name': 'Scenario 3 - Cotton conditions',
        'data': {
            'nitrogen': 120, 'phosphorous': 50, 'pottasium': 70,
            'temp': 28.0, 'hum': 75.0, 'ph': 6.8, 'rainfall': 150.0
        }
    },
    {
        'name': 'Scenario 4 - Maize conditions',
        'data': {
            'nitrogen': 100, 'phosphorous': 45, 'pottasium': 60,
            'temp': 25.0, 'hum': 70.0, 'ph': 6.5, 'rainfall': 120.0
        }
    }
]

print("🧪 Testing multiple crop scenarios to verify image display...")
print("=" * 60)

for i, scenario in enumerate(test_scenarios, 1):
    try:
        print(f"\n{scenario['name']}:")
        print(f"📊 Data: N={scenario['data']['nitrogen']}, P={scenario['data']['phosphorous']}, K={scenario['data']['pottasium']}")
        print(f"      T={scenario['data']['temp']}°C, H={scenario['data']['hum']}%, pH={scenario['data']['ph']}, R={scenario['data']['rainfall']}mm")
        
        response = requests.post('http://127.0.0.1:5000/crop_predict', 
                               data=scenario['data'], 
                               timeout=10)
        
        if response.status_code == 200:
            # Extract crop name and image
            import re
            
            crop_match = re.search(r'class="crop-name"[^>]*>([^<]+)', response.text)
            img_matches = re.findall(r'<img[^>]*src="([^"]*)"[^>]*crop-image', response.text)
            
            if crop_match and img_matches:
                crop_name = crop_match.group(1).strip()
                image_path = img_matches[0]
                print(f"✅ Predicted: {crop_name}")
                print(f"🖼️ Image: {image_path}")
            else:
                print("❌ Could not extract crop info")
        else:
            print(f"❌ Request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in scenario {i}: {e}")

print("\n" + "=" * 60)
print("🎯 All scenarios tested! Each crop should display its corresponding image.")
