#!/usr/bin/env python3
"""
Simple test script for the AI Crop Recommendation feature
"""

import sys
import os
import requests
import json

def test_crop_recommendation():
    """Test the crop recommendation feature"""
    
    print("🌾 Testing AI Crop Recommendation Feature")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test data
    test_cases = [
        {
            'name': 'Rice Suitable Conditions',
            'data': {
                'nitrogen': '90',
                'phosphorous': '42',
                'pottasium': '43',
                'ph': '6.5',
                'rainfall': '202.9',
                'temp': '26.5',
                'hum': '80.3'
            },
            'expected': 'rice'
        },
        {
            'name': 'Wheat Suitable Conditions',
            'data': {
                'nitrogen': '50',
                'phosphorous': '30',
                'pottasium': '20',
                'ph': '6.8',
                'rainfall': '100',
                'temp': '20',
                'hum': '60'
            },
            'expected': 'wheat'
        },
        {
            'name': 'Cotton Suitable Conditions',
            'data': {
                'nitrogen': '70',
                'phosphorous': '35',
                'pottasium': '40',
                'ph': '7.0',
                'rainfall': '80',
                'temp': '28',
                'hum': '70'
            },
            'expected': 'cotton'
        }
    ]
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 30)
        
        try:
            # Test the crop recommendation endpoint
            response = requests.post(f"{base_url}/crop_predict", 
                                   data=test_case['data'], 
                                   timeout=10)
            
            if response.status_code == 200:
                print(f"✅ Request successful (Status: {response.status_code})")
                
                # Check if the response contains the prediction
                if 'prediction' in response.text or test_case['expected'].lower() in response.text.lower():
                    print(f"✅ Prediction returned successfully")
                    passed += 1
                else:
                    print(f"❌ No prediction found in response")
                    failed += 1
            else:
                print(f"❌ Request failed (Status: {response.status_code})")
                failed += 1
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Connection error: {e}")
            failed += 1
        except Exception as e:
            print(f"❌ Test error: {e}")
            failed += 1
    
    # Test form validation
    print(f"\n📋 Test {len(test_cases) + 1}: Input Validation")
    print("-" * 30)
    
    invalid_data = {
        'nitrogen': '200',  # Out of range
        'phosphorous': '42',
        'pottasium': '43',
        'ph': '6.5',
        'rainfall': '202.9',
        'temp': '26.5',
        'hum': '80.3'
    }
    
    try:
        response = requests.post(f"{base_url}/crop_predict", 
                               data=invalid_data, 
                               timeout=10)
        
        if 'error' in response.text.lower() or response.status_code != 200:
            print("✅ Input validation working correctly")
            passed += 1
        else:
            print("❌ Input validation not working")
            failed += 1
            
    except Exception as e:
        print(f"❌ Validation test error: {e}")
        failed += 1
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    total_tests = passed + failed
    success_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT! Crop Recommendation feature is working well!")
    elif success_rate >= 60:
        print("👍 GOOD! Most features are working with minor issues")
    else:
        print("⚠️ NEEDS ATTENTION! Several issues found")
    
    return success_rate >= 80

if __name__ == "__main__":
    print("🚀 Starting Crop Recommendation Feature Test")
    
    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:5000", timeout=5)
        print("✅ Server is accessible")
    except:
        print("❌ Server is not accessible at http://127.0.0.1:5000")
        print("Please start your Flask application first!")
        sys.exit(1)
    
    # Run the test
    success = test_crop_recommendation()
    
    if success:
        print("\n🎯 All tests passed! The AI Crop Recommendation feature is working properly.")
    else:
        print("\n⚠️ Some tests failed. Check the issues above.")
    
    sys.exit(0 if success else 1)
