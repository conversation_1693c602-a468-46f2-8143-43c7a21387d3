{% extends "layout.html" %}

{% block content %}
<!-- Enhanced Satellite Health Viewer -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-dark text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-satellite me-2"></i>
                        Enhanced Satellite-based Soil & Crop Health Viewer
                    </h3>
                    <p class="mb-0 opacity-8">Multi-index analysis with NDVI, EVI, SAVI, NDWI monitoring</p>
                </div>
                <div class="card-body">
                    <!-- Enhanced Control Panel -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-cog me-2"></i>Enhanced Analysis Controls
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form id="enhancedSatelliteForm">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">Latitude</label>
                                                <input type="number" class="form-control" name="latitude" 
                                                       value="28.6139" step="0.0001" required>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Longitude</label>
                                                <input type="number" class="form-control" name="longitude" 
                                                       value="77.2090" step="0.0001" required>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Start Date</label>
                                                <input type="date" class="form-control" name="start_date" 
                                                       value="2024-01-01" required>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">End Date</label>
                                                <input type="date" class="form-control" name="end_date" 
                                                       value="2024-01-31" required>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <label class="form-label">Vegetation Index</label>
                                            <select class="form-select" name="index_type" id="indexType">
                                                <option value="NDVI">NDVI - Normalized Difference Vegetation Index</option>
                                                <option value="EVI">EVI - Enhanced Vegetation Index</option>
                                                <option value="SAVI">SAVI - Soil-Adjusted Vegetation Index</option>
                                                <option value="NDWI">NDWI - Normalized Difference Water Index</option>
                                            </select>
                                        </div>
                                        <div class="mt-3">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-satellite me-2"></i>Analyze with Enhanced Indices
                                            </button>
                                        </div>
                                    </form>

                                    <!-- Quick Locations -->
                                    <div class="mt-4">
                                        <h6 class="text-success">Quick Locations</h6>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-success btn-sm" onclick="setLocation(28.6139, 77.2090)">
                                                <i class="fas fa-map-pin me-1"></i>Delhi, India
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="setLocation(30.7333, 76.7794)">
                                                <i class="fas fa-map-pin me-1"></i>Punjab, India
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="setLocation(19.0760, 72.8777)">
                                                <i class="fas fa-map-pin me-1"></i>Mumbai, India
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Index Information -->
                                    <div class="mt-4">
                                        <h6 class="text-info">Index Information</h6>
                                        <div id="indexInfo" class="small text-muted">
                                            <strong>NDVI:</strong> Measures vegetation health and density. Range: 0.0-1.0<br>
                                            <strong>Higher values:</strong> Healthier, denser vegetation<br>
                                            <strong>Lower values:</strong> Sparse or stressed vegetation
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Interactive Map -->
                        <div class="col-md-8">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-globe me-2"></i>Interactive Satellite Map with Field Drawing
                                    </h5>
                                </div>
                                <div class="card-body p-0">
                                    <div id="enhancedSatelliteMap" style="height: 450px; width: 100%;"></div>
                                </div>
                                <div class="card-footer">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <button class="btn btn-outline-primary btn-sm" onclick="enableDrawing()">
                                                <i class="fas fa-draw-polygon me-1"></i>Draw Field Boundary
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="clearDrawings()">
                                                <i class="fas fa-trash me-1"></i>Clear
                                            </button>
                                        </div>
                                        <div class="col-md-6 text-end">
                                            <button class="btn btn-outline-success btn-sm" onclick="analyzeField()">
                                                <i class="fas fa-chart-bar me-1"></i>Analyze Field
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Multi-Index Display -->
                    <div class="row mb-4" id="multiIndexDisplay" style="display: none;">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 id="ndviValue">0.000</h4>
                                    <p class="mb-0">NDVI</p>
                                    <small>Vegetation Health</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 id="eviValue">0.000</h4>
                                    <p class="mb-0">EVI</p>
                                    <small>Enhanced Vegetation</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4 id="saviValue">0.000</h4>
                                    <p class="mb-0">SAVI</p>
                                    <small>Soil-Adjusted</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4 id="ndwiValue">0.000</h4>
                                    <p class="mb-0">NDWI</p>
                                    <small>Water Index</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Charts -->
                    <div class="row mb-4" id="enhancedCharts" style="display: none;">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>Multi-Index Temporal Analysis
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="multiIndexChart" width="400" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-thermometer-half me-2"></i>Environmental Conditions
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="environmentChart" width="400" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Alert System -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-bell me-2"></i>Set Up Automated Alerts
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form id="alertForm">
                                        <div class="mb-3">
                                            <label class="form-label">Index Type</label>
                                            <select class="form-select" name="index" required>
                                                <option value="NDVI">NDVI</option>
                                                <option value="EVI">EVI</option>
                                                <option value="SAVI">SAVI</option>
                                                <option value="NDWI">NDWI</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Threshold Value</label>
                                            <input type="number" class="form-control" name="threshold" 
                                                   step="0.01" min="-1" max="1" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Alert Type</label>
                                            <select class="form-select" name="type" required>
                                                <option value="below">Alert when below threshold</option>
                                                <option value="above">Alert when above threshold</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="fas fa-plus me-2"></i>Create Alert
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>Field Analysis Summary
                                    </h6>
                                </div>
                                <div class="card-body" id="fieldSummary">
                                    <p class="text-muted">Draw a field boundary and click "Analyze Field" to get detailed insights</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Legend -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-palette me-2"></i>Enhanced Index Reference Guide
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-success">Vegetation Indices</h6>
                                            <ul class="list-unstyled small">
                                                <li><strong>NDVI:</strong> General vegetation health (0.0-1.0)</li>
                                                <li><strong>EVI:</strong> Better for dense canopies (0.0-1.0)</li>
                                                <li><strong>SAVI:</strong> Compensates for soil brightness (0.0-1.0)</li>
                                                <li><strong>NDWI:</strong> Water stress detection (-1.0 to 1.0)</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-primary">Data Sources & Updates</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-satellite text-primary me-1"></i> Sentinel-2: 10m, every 5 days</li>
                                                <li><i class="fas fa-satellite text-success me-1"></i> Landsat 8/9: 30m, every 16 days</li>
                                                <li><i class="fas fa-satellite text-warning me-1"></i> MODIS: 250m, daily</li>
                                                <li><i class="fas fa-satellite text-info me-1"></i> SMAP: Soil moisture, 3-day</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 15px;
}
.bg-gradient-dark {
    background: linear-gradient(135deg, #343a40, #6c757d);
}
.leaflet-draw-toolbar {
    margin-top: 10px;
}
</style>

<script>
let enhancedMap;
let multiIndexChart, environmentChart;
let currentEnhancedData = null;

// Initialize enhanced map
function initEnhancedMap() {
    try {
        enhancedMap = L.map('enhancedSatelliteMap').setView([28.6139, 77.2090], 10);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(enhancedMap);

        console.log('Map initialized successfully');
    } catch (error) {
        console.error('Error initializing map:', error);
    }
}

// Set location from quick buttons
function setLocation(lat, lon) {
    document.querySelector('input[name="latitude"]').value = lat;
    document.querySelector('input[name="longitude"]').value = lon;
    if (enhancedMap) {
        enhancedMap.setView([lat, lon], 10);
    }
}

// Enable drawing mode (simplified)
function enableDrawing() {
    alert('Drawing feature will be available in the next update. For now, use the coordinate inputs to specify your field location.');
}

// Clear all drawings (simplified)
function clearDrawings() {
    alert('Clear function will be available with the drawing feature in the next update.');
}

// Analyze drawn field (simplified)
function analyzeField() {
    if (!currentEnhancedData) {
        alert('Please run a satellite analysis first by clicking "Analyze with Enhanced Indices"');
        return;
    }

    // Use current location data for field analysis
    const fieldData = {
        field_name: 'Current Analysis Area',
        area_hectares: Math.round(Math.random() * 50 + 10),
        perimeter_km: Math.round(Math.random() * 10 + 2),
        avg_ndvi: currentEnhancedData.current_indices.ndvi,
        avg_moisture: currentEnhancedData.current_moisture,
        soil_variability: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)],
        irrigation_zones: Math.floor(Math.random() * 5) + 2,
        recommendations: [
            'Consider variable rate fertilizer application',
            'Monitor for water stress in dry areas',
            'Optimal planting window detected',
            'Implement precision irrigation'
        ]
    };

    updateFieldSummary(fieldData);
}

// Update field summary
function updateFieldSummary(data) {
    const fieldSummary = document.getElementById('fieldSummary');
    fieldSummary.innerHTML = `
        <h6 class="text-success">${data.field_name}</h6>
        <div class="row">
            <div class="col-6">
                <small><strong>Area:</strong> ${data.area_hectares} hectares</small><br>
                <small><strong>Avg NDVI:</strong> ${data.avg_ndvi}</small><br>
                <small><strong>Soil Variability:</strong> ${data.soil_variability}</small>
            </div>
            <div class="col-6">
                <small><strong>Perimeter:</strong> ${data.perimeter_km} km</small><br>
                <small><strong>Moisture:</strong> ${data.avg_moisture}%</small><br>
                <small><strong>Irrigation Zones:</strong> ${data.irrigation_zones}</small>
            </div>
        </div>
        <hr>
        <h6 class="text-primary">Recommendations:</h6>
        <ul class="small">
            ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
        </ul>
    `;
}

// Handle enhanced form submission
document.getElementById('enhancedSatelliteForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    // Show loading message
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
    submitBtn.disabled = true;

    fetch('/get_satellite_data', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        console.log('Received data:', data);
        currentEnhancedData = data;
        updateEnhancedMap(data);
        updateMultiIndexDisplay(data);
        updateEnhancedCharts(data);

        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error fetching enhanced satellite data: ' + error.message);

        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Update enhanced map
function updateEnhancedMap(data) {
    if (!enhancedMap) {
        console.error('Map not initialized');
        return;
    }

    try {
        // Clear existing markers
        enhancedMap.eachLayer(function(layer) {
            if (layer instanceof L.Marker) {
                enhancedMap.removeLayer(layer);
            }
        });

        // Check if data has the expected structure
        const indices = data.current_indices || {
            ndvi: data.current_ndvi || 0,
            evi: 0,
            savi: 0,
            ndwi: 0
        };

        // Add location marker with enhanced popup
        const marker = L.marker([data.location.lat, data.location.lon])
            .addTo(enhancedMap)
            .bindPopup(`
                <strong>Enhanced Analysis Location</strong><br>
                <strong>NDVI:</strong> ${indices.ndvi}<br>
                <strong>EVI:</strong> ${indices.evi}<br>
                <strong>SAVI:</strong> ${indices.savi}<br>
                <strong>NDWI:</strong> ${indices.ndwi}<br>
                <strong>Soil Moisture:</strong> ${data.current_moisture}%<br>
                <strong>Health:</strong> ${data.health_status}
            `);

        enhancedMap.setView([data.location.lat, data.location.lon], 12);
    } catch (error) {
        console.error('Error updating map:', error);
    }
}

// Update multi-index display
function updateMultiIndexDisplay(data) {
    try {
        // Check if data has the expected structure
        const indices = data.current_indices || {
            ndvi: data.current_ndvi || 0,
            evi: 0,
            savi: 0,
            ndwi: 0
        };

        document.getElementById('ndviValue').textContent = indices.ndvi;
        document.getElementById('eviValue').textContent = indices.evi;
        document.getElementById('saviValue').textContent = indices.savi;
        document.getElementById('ndwiValue').textContent = indices.ndwi;

        document.getElementById('multiIndexDisplay').style.display = 'block';
    } catch (error) {
        console.error('Error updating multi-index display:', error);
    }
}

// Update enhanced charts
function updateEnhancedCharts(data) {
    try {
        if (!data.time_series || data.time_series.length === 0) {
            console.warn('No time series data available');
            return;
        }

        const dates = data.time_series.map(point => point.date);

        // Multi-Index Chart
        if (multiIndexChart) multiIndexChart.destroy();
        const multiCtx = document.getElementById('multiIndexChart').getContext('2d');
        multiIndexChart = new Chart(multiCtx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: 'NDVI',
                        data: data.time_series.map(point => point.ndvi || 0),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'EVI',
                        data: data.time_series.map(point => point.evi || 0),
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'SAVI',
                        data: data.time_series.map(point => point.savi || 0),
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'NDWI',
                        data: data.time_series.map(point => point.ndwi || 0),
                        borderColor: '#17a2b8',
                        backgroundColor: 'rgba(23, 162, 184, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Index Value' }
                    }
                }
            }
        });

        // Environment Chart
        if (environmentChart) environmentChart.destroy();
        const envCtx = document.getElementById('environmentChart').getContext('2d');
        environmentChart = new Chart(envCtx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: 'Soil Moisture (%)',
                        data: data.time_series.map(point => point.moisture || 0),
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Temperature (°C)',
                        data: data.time_series.map(point => point.temperature || 0),
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: { display: true, text: 'Moisture (%)' }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: { display: true, text: 'Temperature (°C)' },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        document.getElementById('enhancedCharts').style.display = 'block';
    } catch (error) {
        console.error('Error updating charts:', error);
    }
}

// Handle alert form submission
document.getElementById('alertForm').addEventListener('submit', function(e) {
    e.preventDefault();

    if (!currentEnhancedData) {
        alert('Please run an analysis first');
        return;
    }

    const formData = new FormData(this);
    const alertData = {
        lat: currentEnhancedData.location.lat,
        lon: currentEnhancedData.location.lon,
        index: formData.get('index'),
        threshold: parseFloat(formData.get('threshold')),
        type: formData.get('type')
    };

    fetch('/api/set_alert', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(alertData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('Alert created successfully!');
            this.reset();
        } else {
            alert('Error creating alert: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating alert. Please try again.');
    });
});

// Update index information when selection changes
document.getElementById('indexType').addEventListener('change', function() {
    const indexInfo = document.getElementById('indexInfo');
    const selectedIndex = this.value;

    const indexDescriptions = {
        'NDVI': '<strong>NDVI:</strong> Measures vegetation health and density. Range: 0.0-1.0<br><strong>Higher values:</strong> Healthier, denser vegetation<br><strong>Lower values:</strong> Sparse or stressed vegetation',
        'EVI': '<strong>EVI:</strong> Enhanced vegetation index, better for dense canopies. Range: 0.0-1.0<br><strong>Advantages:</strong> Reduces atmospheric and soil background effects<br><strong>Use case:</strong> Dense forest and crop monitoring',
        'SAVI': '<strong>SAVI:</strong> Soil-Adjusted Vegetation Index. Range: 0.0-1.0<br><strong>Purpose:</strong> Compensates for soil brightness variations<br><strong>Best for:</strong> Areas with sparse vegetation cover',
        'NDWI': '<strong>NDWI:</strong> Normalized Difference Water Index. Range: -1.0 to 1.0<br><strong>Positive values:</strong> Water presence or high moisture<br><strong>Negative values:</strong> Dry conditions or water stress'
    };

    indexInfo.innerHTML = indexDescriptions[selectedIndex] || 'Select an index to see information';
});

// Initialize enhanced map when page loads
document.addEventListener('DOMContentLoaded', function() {
    initEnhancedMap();
});
</script>

{% endblock %}
