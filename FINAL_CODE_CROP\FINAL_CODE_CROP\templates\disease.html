{% extends 'layout.html' %}

{% block content %}
<div class="container mt-5">
  <div class="row">
    <!-- Content Section -->
    <div class="col-md-6">
      <div class="card shadow-lg">
        <div class="card-body">
          <h2 class="display-6 fw-bold text-success mb-4">
            Empowering Early Diagnosis with AI-Powered Detection
          </h2>
          <p class="lead text-muted mb-4">
            Early detection is the key to successful treatment and prevention. Our advanced Disease Detection platform leverages Artificial Intelligence and Machine Learning to analyze symptoms, medical images, and environmental factors to provide accurate and fast disease predictions.
          </p>
          
          <!-- Feature Highlights -->
          <div class="features-list">
            <div class="feature-item d-flex align-items-center mb-3">
              <div class="feature-icon bg-success text-white rounded-circle p-2 me-3">
              </div>
              <div>
                <h6 class="mb-1 fw-bold">AI-Powered Analysis</h6>
                <small class="text-muted">Advanced machine learning algorithms for precise diagnosis</small>
              </div>
            </div>
            
            <div class="feature-item d-flex align-items-center mb-3">
              <div class="feature-icon bg-info text-white rounded-circle p-2 me-3">
              </div>
              <div>
                <h6 class="mb-1 fw-bold">Instant Results</h6>
                <small class="text-muted">Get diagnosis in seconds, not days</small>
              </div>
            </div>
            
            <div class="feature-item d-flex align-items-center mb-3">
              <div class="feature-icon bg-warning text-white rounded-circle p-2 me-3">
              </div>
              <div>
                <h6 class="mb-1 fw-bold">High Accuracy</h6>
                <small class="text-muted">98% accuracy rate with continuous learning</small>
              </div>
            </div>
            
            <div class="feature-item d-flex align-items-center mb-3">
              <div class="feature-icon bg-danger text-white rounded-circle p-2 me-3">
              </div>
              <div>
                <h6 class="mb-1 fw-bold">Plant Health Focus</h6>
                <small class="text-muted">Specialized in agricultural disease detection</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Detection Card -->
    <div class="col-md-6">
      <div class="card shadow-lg">
        <div class="card-header bg-success text-white">
          <h3 class="mb-0">
            Plant Disease Detection
          </h3>
          <small>Upload an image for instant AI analysis</small>
        </div>
        <div class="card-body">
          <form method="post" enctype="multipart/form-data">
            <div class="mb-3">
              <label class="form-label fw-bold">Upload Plant Image</label>
              <input type="file" name="file" class="form-control" id="inputfile" onchange="preview_image(event)" accept="image/*" required>
              <div class="form-text">Supported formats: JPG, PNG, GIF (Max 10MB)</div>
            </div>
            <div class="mb-3">
              <img id="output-image" class="img-fluid rounded shadow-sm" style="max-height: 200px; display: none;" />
            </div>
            
            <button class="btn btn-success btn-lg w-100" type="submit">
              Analyze Disease
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.feature-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.feature-item {
  transition: transform 0.2s ease;
}

.feature-item:hover {
  transform: translateX(5px);
}

.card {
  border: none;
  border-radius: 15px;
}

.card-header {
  border-radius: 15px 15px 0 0 !important;
  padding: 1.5rem;
}

.card-body {
  padding: 2rem;
}

@media (max-width: 768px) {
  .hero-content h2 {
    font-size: 1.75rem;
  }
}
</style>

<script>
function preview_image(event) {
    const output = document.getElementById('output-image');
    output.src = URL.createObjectURL(event.target.files[0]);
    output.style.display = 'block';
}
</script>
{% endblock %}