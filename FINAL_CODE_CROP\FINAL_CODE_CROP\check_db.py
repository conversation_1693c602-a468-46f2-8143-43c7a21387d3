import sqlite3

# Connect to database
conn = sqlite3.connect('user_data.db')
cursor = conn.cursor()

# Check tables
print("Tables in database:")
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
for table in tables:
    print(f"- {table[0]}")

# Check user data (try different possible table names)
for table_name in ['users', 'details', 'user_details', 'accounts']:
    try:
        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
        results = cursor.fetchall()
        if results:
            print(f"\nData from {table_name}:")
            for row in results:
                print(row)
            break
    except sqlite3.OperationalError:
        continue

conn.close()
