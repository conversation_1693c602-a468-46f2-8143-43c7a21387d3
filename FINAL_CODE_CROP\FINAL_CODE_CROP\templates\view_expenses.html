<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Expenses - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .filter-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .expense-table {
            border-radius: 10px;
            overflow: hidden;
        }
        .category-badge {
            font-size: 0.85em;
        }
        .amount-cell {
            font-weight: bold;
            color: #dc3545;
        }
        .table-hover tbody tr:hover {
            background-color: rgba(40, 167, 69, 0.1);
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-seedling me-2"></i>AgroPro - View Expenses
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/expenses"><i class="fas fa-dashboard me-1"></i>Dashboard</a>
                <a class="nav-link" href="/add_expense"><i class="fas fa-plus me-1"></i>Add Expense</a>
                <a class="nav-link" href="/expense_analytics"><i class="fas fa-chart-bar me-1"></i>Analytics</a>
                <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Home</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-6 fw-bold text-success">
                    <i class="fas fa-list me-3"></i>Expense History
                </h1>
                <p class="lead text-muted">View and manage all your agricultural expenses</p>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card filter-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Expenses</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="/view_expenses">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">All Categories</option>
                                        {% for cat in categories %}
                                        <option value="{{ cat }}" {{ 'selected' if cat == selected_category }}>
                                            {{ cat }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="{{ date_from }}">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="{{ date_to }}">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>Filter
                                        </button>
                                        <a href="/view_expenses" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expenses Table -->
        <div class="row">
            <div class="col-12">
                <div class="card filter-card">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Expense Records</h5>
                        <span class="badge bg-light text-dark">{{ expenses|length }} records</span>
                    </div>
                    <div class="card-body p-0">
                        {% if expenses %}
                        <div class="table-responsive">
                            <table class="table table-hover expense-table mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date</th>
                                        <th>Category</th>
                                        <th>Description</th>
                                        <th class="text-end">Amount</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for expense in expenses %}
                                    <tr>
                                        <td>
                                            <i class="fas fa-calendar text-muted me-1"></i>
                                            {{ expense[1] }}
                                        </td>
                                        <td>
                                            {% set category_icons = {
                                                'Seeds': 'fas fa-seedling text-success',
                                                'Fertilizers': 'fas fa-leaf text-success',
                                                'Labor': 'fas fa-users text-primary',
                                                'Pesticides': 'fas fa-spray-can text-warning',
                                                'Irrigation': 'fas fa-tint text-info',
                                                'Equipment': 'fas fa-tools text-secondary',
                                                'Transport': 'fas fa-truck text-primary',
                                                'Packaging': 'fas fa-box text-warning',
                                                'Fuel': 'fas fa-gas-pump text-danger',
                                                'Maintenance': 'fas fa-wrench text-secondary',
                                                'Insurance': 'fas fa-shield-alt text-info',
                                                'Others': 'fas fa-ellipsis-h text-muted'
                                            } %}
                                            <span class="badge bg-secondary category-badge">
                                                <i class="{{ category_icons.get(expense[2], 'fas fa-tag') }} me-1"></i>
                                                {{ expense[2] }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if expense[4] %}
                                                {{ expense[4] }}
                                            {% else %}
                                                <em class="text-muted">No description</em>
                                            {% endif %}
                                        </td>
                                        <td class="text-end amount-cell">
                                            ₹{{ "%.2f"|format(expense[3]) }}
                                        </td>
                                        <td class="text-center">
                                            <button class="btn btn-sm btn-outline-danger delete-btn"
                                                    data-expense-id="{{ expense[0] }}"
                                                    onclick="deleteExpenseRealtime({{ expense[0] }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="3" class="text-end">Total:</th>
                                        <th class="text-end amount-cell">
                                            ₹{{ "%.2f"|format(expenses|sum(attribute=3)) }}
                                        </th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">No Expenses Found</h4>
                            <p class="text-muted">
                                {% if selected_category or date_from or date_to %}
                                    No expenses match your current filters. Try adjusting your search criteria.
                                {% else %}
                                    You haven't recorded any expenses yet.
                                {% endif %}
                            </p>
                            <a href="/add_expense" class="btn btn-success btn-lg">
                                <i class="fas fa-plus me-2"></i>Add Your First Expense
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        {% if expenses %}
        <div class="row mt-4">
            <div class="col-12">
                <div class="card filter-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-download me-2"></i>Export Options</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-outline-success w-100" onclick="exportToCSV()">
                                    <i class="fas fa-file-csv fa-2x mb-2"></i><br>
                                    Export to CSV
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-outline-primary w-100" onclick="printTable()">
                                    <i class="fas fa-print fa-2x mb-2"></i><br>
                                    Print Report
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-outline-info w-100" onclick="copyToClipboard()">
                                    <i class="fas fa-copy fa-2x mb-2"></i><br>
                                    Copy Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        // Export to CSV
        function exportToCSV() {
            let csvContent = "Date,Category,Description,Amount\n";
            
            {% for expense in expenses %}
            csvContent += "{{ expense[1] }},{{ expense[2] }},\"{{ expense[4] or '' }}\",{{ expense[3] }}\n";
            {% endfor %}
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.setAttribute('hidden', '');
            a.setAttribute('href', url);
            a.setAttribute('download', 'expenses_' + new Date().toISOString().split('T')[0] + '.csv');
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        // Print table
        function printTable() {
            window.print();
        }

        // Copy to clipboard
        function copyToClipboard() {
            let text = "Date\tCategory\tDescription\tAmount\n";
            
            {% for expense in expenses %}
            text += "{{ expense[1] }}\t{{ expense[2] }}\t{{ expense[4] or '' }}\t₹{{ expense[3] }}\n";
            {% endfor %}
            
            navigator.clipboard.writeText(text).then(function() {
                alert('Data copied to clipboard!');
            });
        }

        // Initialize Socket.IO for real-time updates
        const socket = io();

        // Connection status
        socket.on('connect', function() {
            console.log('Connected to real-time expense system');
            showNotification('Connected to real-time updates', 'success');
        });

        socket.on('disconnect', function() {
            showNotification('Connection lost', 'warning');
        });

        // Real-time responses
        socket.on('expense_deleted', function(data) {
            showNotification(data.message, 'success');
            // Remove the deleted row from table
            const row = document.querySelector(`button[data-expense-id="${data.expense_id}"]`).closest('tr');
            if (row) {
                row.style.transition = 'opacity 0.5s ease';
                row.style.opacity = '0';
                setTimeout(() => row.remove(), 500);
            }
            // Update total
            updateTotal();
        });

        socket.on('expense_added', function(data) {
            showNotification('New expense added by another user', 'info');
            // Refresh the page to show new data
            setTimeout(() => location.reload(), 2000);
        });

        socket.on('success', function(data) {
            showNotification(data.message, 'success');
        });

        socket.on('error', function(data) {
            showNotification(data.message, 'danger');
        });

        // Real-time delete function
        function deleteExpenseRealtime(expenseId) {
            if (!confirm('Are you sure you want to delete this expense?')) {
                return;
            }

            const button = document.querySelector(`button[data-expense-id="${expenseId}"]`);

            if (socket.connected) {
                // Disable button and show loading
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                button.disabled = true;

                // Emit delete event
                socket.emit('delete_expense_realtime', { expense_id: expenseId });
            } else {
                // Fallback to regular deletion
                window.location.href = `/delete_expense/${expenseId}`;
            }
        }

        function updateTotal() {
            // Recalculate total from visible rows
            const rows = document.querySelectorAll('tbody tr');
            let total = 0;

            rows.forEach(row => {
                const amountCell = row.querySelector('td:nth-child(4)');
                if (amountCell) {
                    const amount = parseFloat(amountCell.textContent.replace('₹', '').replace(',', ''));
                    if (!isNaN(amount)) {
                        total += amount;
                    }
                }
            });

            // Update footer total
            const totalCell = document.querySelector('tfoot th:nth-child(4)');
            if (totalCell) {
                totalCell.textContent = '₹' + total.toFixed(2);
            }
        }

        function showNotification(message, type) {
            // Remove existing notifications
            const existing = document.querySelectorAll('.notification-alert');
            existing.forEach(el => el.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed notification-alert`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Auto-submit form when date changes
        document.getElementById('date_from').addEventListener('change', function() {
            if (this.value && document.getElementById('date_to').value) {
                document.querySelector('form').submit();
            }
        });

        document.getElementById('date_to').addEventListener('change', function() {
            if (this.value && document.getElementById('date_from').value) {
                document.querySelector('form').submit();
            }
        });

        // Real-time table updates
        function addRowAnimation(row) {
            row.style.backgroundColor = '#d4edda';
            row.style.transition = 'background-color 3s ease';
            setTimeout(() => {
                row.style.backgroundColor = '';
            }, 3000);
        }
    </script>
</body>
</html>
