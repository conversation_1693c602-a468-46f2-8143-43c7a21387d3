{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Crop yield Prediction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Import necessary libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### load the dataset"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["data = pd.read_excel('crop_csv_file.xlsx')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>State_Name</th>\n", "      <th>District_Name</th>\n", "      <th>Crop_Year</th>\n", "      <th>Season</th>\n", "      <th>Crop</th>\n", "      <th>Temperature</th>\n", "      <th>humidity</th>\n", "      <th>soil moisture</th>\n", "      <th>area</th>\n", "      <th>Production</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Arecanut</td>\n", "      <td>36</td>\n", "      <td>35</td>\n", "      <td>45</td>\n", "      <td>1254.0</td>\n", "      <td>2000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Other Kharif pulses</td>\n", "      <td>37</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Rice</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>50</td>\n", "      <td>102.0</td>\n", "      <td>321.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td>Whole Year</td>\n", "      <td>Banana</td>\n", "      <td>37</td>\n", "      <td>42</td>\n", "      <td>55</td>\n", "      <td>176.0</td>\n", "      <td>641.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td>Whole Year</td>\n", "      <td>Cashewnut</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>720.0</td>\n", "      <td>165.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    State_Name District_Name  Crop_Year       Season  \\\n", "0  Andaman and Nicobar Islands      NICOBARS       2000  Kharif        \n", "1  Andaman and Nicobar Islands      NICOBARS       2000  Kharif        \n", "2  Andaman and Nicobar Islands      NICOBARS       2000  Kharif        \n", "3  Andaman and Nicobar Islands      NICOBARS       2000  Whole Year    \n", "4  Andaman and Nicobar Islands      NICOBARS       2000  Whole Year    \n", "\n", "                  Crop  Temperature  humidity  soil moisture    area  \\\n", "0             Arecanut           36        35             45  1254.0   \n", "1  Other Kharif pulses           37        40             46     2.0   \n", "2                 Rice           36        41             50   102.0   \n", "3               Banana           37        42             55   176.0   \n", "4            Cashewnut           36        40             54   720.0   \n", "\n", "   Production  \n", "0      2000.0  \n", "1         1.0  \n", "2       321.0  \n", "3       641.0  \n", "4       165.0  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["data.head()\n", "data = data[:500]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>State_Name</th>\n", "      <th>District_Name</th>\n", "      <th>Crop_Year</th>\n", "      <th>Season</th>\n", "      <th>Crop</th>\n", "      <th>Temperature</th>\n", "      <th>humidity</th>\n", "      <th>soil moisture</th>\n", "      <th>area</th>\n", "      <th>Production</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Arecanut</td>\n", "      <td>36</td>\n", "      <td>35</td>\n", "      <td>45</td>\n", "      <td>1254.0</td>\n", "      <td>2000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Other Kharif pulses</td>\n", "      <td>37</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Rice</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>50</td>\n", "      <td>102.0</td>\n", "      <td>321.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td>Whole Year</td>\n", "      <td>Banana</td>\n", "      <td>37</td>\n", "      <td>42</td>\n", "      <td>55</td>\n", "      <td>176.0</td>\n", "      <td>641.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td>Whole Year</td>\n", "      <td>Cashewnut</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>720.0</td>\n", "      <td>165.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>495</th>\n", "      <td>Andhra Pradesh</td>\n", "      <td>ANANTAPUR</td>\n", "      <td>2004</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Castor seed</td>\n", "      <td>36</td>\n", "      <td>35</td>\n", "      <td>45</td>\n", "      <td>1578.0</td>\n", "      <td>923.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>496</th>\n", "      <td>Andhra Pradesh</td>\n", "      <td>ANANTAPUR</td>\n", "      <td>2004</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON>(lint)</td>\n", "      <td>37</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>8933.0</td>\n", "      <td>7041.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>Andhra Pradesh</td>\n", "      <td>ANANTAPUR</td>\n", "      <td>2004</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Dry chillies</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>50</td>\n", "      <td>3402.0</td>\n", "      <td>11288.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>498</th>\n", "      <td>Andhra Pradesh</td>\n", "      <td>ANANTAPUR</td>\n", "      <td>2004</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Groundnut</td>\n", "      <td>37</td>\n", "      <td>42</td>\n", "      <td>55</td>\n", "      <td>857823.0</td>\n", "      <td>684543.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>499</th>\n", "      <td>Andhra Pradesh</td>\n", "      <td>ANANTAPUR</td>\n", "      <td>2004</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Horse-gram</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>421.0</td>\n", "      <td>121.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>500 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                      State_Name District_Name  Crop_Year       Season  \\\n", "0    Andaman and Nicobar Islands      NICOBARS       2000  Kharif        \n", "1    Andaman and Nicobar Islands      NICOBARS       2000  Kharif        \n", "2    Andaman and Nicobar Islands      NICOBARS       2000  Kharif        \n", "3    Andaman and Nicobar Islands      NICOBARS       2000  Whole Year    \n", "4    Andaman and Nicobar Islands      NICOBARS       2000  Whole Year    \n", "..                           ...           ...        ...          ...   \n", "495               Andhra Pradesh     ANANTAPUR       2004  Kharif        \n", "496               Andhra Pradesh     ANANTAPUR       2004  Kharif        \n", "497               Andhra Pradesh     ANANTAPUR       2004  Kharif        \n", "498               Andhra Pradesh     ANANTAPUR       2004  Kharif        \n", "499               Andhra Pradesh     ANANTAPUR       2004  Kharif        \n", "\n", "                    Crop  Temperature  humidity  soil moisture      area  \\\n", "0               Arecanut           36        35             45    1254.0   \n", "1    Other Kharif pulses           37        40             46       2.0   \n", "2                   Rice           36        41             50     102.0   \n", "3                 Banana           37        42             55     176.0   \n", "4              Cashewnut           36        40             54     720.0   \n", "..                   ...          ...       ...            ...       ...   \n", "495          Castor seed           36        35             45    1578.0   \n", "496         Cotton(lint)           37        40             46    8933.0   \n", "497         Dry chillies           36        41             50    3402.0   \n", "498            Groundnut           37        42             55  857823.0   \n", "499           Horse-gram           36        40             54     421.0   \n", "\n", "     Production  \n", "0        2000.0  \n", "1           1.0  \n", "2         321.0  \n", "3         641.0  \n", "4         165.0  \n", "..          ...  \n", "495       923.0  \n", "496      7041.0  \n", "497     11288.0  \n", "498    684543.0  \n", "499       121.0  \n", "\n", "[500 rows x 10 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Exploration"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 500 entries, 0 to 499\n", "Data columns (total 10 columns):\n", " #   Column         Non-Null Count  Dtype  \n", "---  ------         --------------  -----  \n", " 0   State_Name     500 non-null    object \n", " 1   District_Name  500 non-null    object \n", " 2   Crop_Year      500 non-null    int64  \n", " 3   Season         500 non-null    object \n", " 4   Crop           500 non-null    object \n", " 5   Temperature    500 non-null    int64  \n", " 6   humidity       500 non-null    int64  \n", " 7   soil moisture  500 non-null    int64  \n", " 8    area          500 non-null    float64\n", " 9   Production     498 non-null    float64\n", "dtypes: float64(2), int64(4), object(4)\n", "memory usage: 39.2+ KB\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 498 entries, 0 to 499\n", "Data columns (total 10 columns):\n", " #   Column         Non-Null Count  Dtype  \n", "---  ------         --------------  -----  \n", " 0   State_Name     498 non-null    object \n", " 1   District_Name  498 non-null    object \n", " 2   Crop_Year      498 non-null    int64  \n", " 3   Season         498 non-null    object \n", " 4   Crop           498 non-null    object \n", " 5   Temperature    498 non-null    int64  \n", " 6   humidity       498 non-null    int64  \n", " 7   soil moisture  498 non-null    int64  \n", " 8    area          498 non-null    float64\n", " 9   Production     498 non-null    float64\n", "dtypes: float64(2), int64(4), object(4)\n", "memory usage: 42.8+ KB\n"]}], "source": ["#handling missing data\n", "data = data.dropna()\n", "data.info()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Crop_Year</th>\n", "      <th>Temperature</th>\n", "      <th>humidity</th>\n", "      <th>soil moisture</th>\n", "      <th>area</th>\n", "      <th>Production</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>498.000000</td>\n", "      <td>498.000000</td>\n", "      <td>498.000000</td>\n", "      <td>498.000000</td>\n", "      <td>498.000000</td>\n", "      <td>4.980000e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2002.405622</td>\n", "      <td>34.459839</td>\n", "      <td>44.714859</td>\n", "      <td>53.076305</td>\n", "      <td>16368.695582</td>\n", "      <td>1.484588e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.434370</td>\n", "      <td>3.496390</td>\n", "      <td>6.654677</td>\n", "      <td>5.248187</td>\n", "      <td>93281.097184</td>\n", "      <td>8.502200e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1997.000000</td>\n", "      <td>25.000000</td>\n", "      <td>35.000000</td>\n", "      <td>45.000000</td>\n", "      <td>0.200000</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2000.000000</td>\n", "      <td>34.000000</td>\n", "      <td>40.000000</td>\n", "      <td>50.000000</td>\n", "      <td>85.500000</td>\n", "      <td>1.000000e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2002.000000</td>\n", "      <td>36.000000</td>\n", "      <td>42.000000</td>\n", "      <td>54.000000</td>\n", "      <td>652.725000</td>\n", "      <td>8.264100e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2004.000000</td>\n", "      <td>36.000000</td>\n", "      <td>50.000000</td>\n", "      <td>55.000000</td>\n", "      <td>3391.000000</td>\n", "      <td>1.023835e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>2010.000000</td>\n", "      <td>37.000000</td>\n", "      <td>55.000000</td>\n", "      <td>62.000000</td>\n", "      <td>857823.000000</td>\n", "      <td>7.130000e+07</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Crop_Year  Temperature    humidity  soil moisture           area  \\\n", "count   498.000000   498.000000  498.000000     498.000000     498.000000   \n", "mean   2002.405622    34.459839   44.714859      53.076305   16368.695582   \n", "std       3.434370     3.496390    6.654677       5.248187   93281.097184   \n", "min    1997.000000    25.000000   35.000000      45.000000       0.200000   \n", "25%    2000.000000    34.000000   40.000000      50.000000      85.500000   \n", "50%    2002.000000    36.000000   42.000000      54.000000     652.725000   \n", "75%    2004.000000    36.000000   50.000000      55.000000    3391.000000   \n", "max    2010.000000    37.000000   55.000000      62.000000  857823.000000   \n", "\n", "         Production  \n", "count  4.980000e+02  \n", "mean   1.484588e+06  \n", "std    8.502200e+06  \n", "min    0.000000e+00  \n", "25%    1.000000e+02  \n", "50%    8.264100e+02  \n", "75%    1.023835e+04  \n", "max    7.130000e+07  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Categorical data handling"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import LabelEncoder\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "le = LabelEncoder()\n", "\n", "State_Name = le.fit_transform(data.State_Name)\n", "District_Name = le.fit_transform(data.District_Name)\n", "#Crop_Year = le.fit_transform(data.Crop_Year)\n", "crop = le.fit_transform(data.Crop)\n", "Season = le.fit_transform(data.Season)\n", "data['State_Name'] = State_Name\n", "data['District_Name'] = District_Name\n", "data['Crop'] = crop\n", "data['Season']  = Season"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>State_Name</th>\n", "      <th>District_Name</th>\n", "      <th>Crop_Year</th>\n", "      <th>Season</th>\n", "      <th>Crop</th>\n", "      <th>Temperature</th>\n", "      <th>humidity</th>\n", "      <th>soil moisture</th>\n", "      <th>area</th>\n", "      <th>Production</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>36</td>\n", "      <td>35</td>\n", "      <td>45</td>\n", "      <td>1254.0</td>\n", "      <td>2000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "      <td>32</td>\n", "      <td>37</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "      <td>38</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>50</td>\n", "      <td>102.0</td>\n", "      <td>321.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2000</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>37</td>\n", "      <td>42</td>\n", "      <td>55</td>\n", "      <td>176.0</td>\n", "      <td>641.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2000</td>\n", "      <td>3</td>\n", "      <td>8</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>720.0</td>\n", "      <td>165.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   State_Name  District_Name  Crop_Year  Season  Crop  Temperature  humidity  \\\n", "0           0              1       2000       1     0           36        35   \n", "1           0              1       2000       1    32           37        40   \n", "2           0              1       2000       1    38           36        41   \n", "3           0              1       2000       3     3           37        42   \n", "4           0              1       2000       3     8           36        40   \n", "\n", "   soil moisture    area  Production  \n", "0             45  1254.0      2000.0  \n", "1             46     2.0         1.0  \n", "2             50   102.0       321.0  \n", "3             55   176.0       641.0  \n", "4             54   720.0       165.0  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Splitting the dataset for train and test"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "#from sklearn.cross_validation import train_test_split"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["X = data.iloc[:,:-1]\n", "y = data.iloc[:,-1]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["X_train,X_test,Y_train,Y_test = train_test_split(X,y,test_size=0.2,random_state=100)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MSE train: 728970984646.353, test: 26682633827305.480\n", "R^2 train: 0.991, test: 0.498\n"]}], "source": ["from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.metrics import roc_auc_score , classification_report, mean_squared_error, r2_score\n", "forest = RandomForestRegressor(n_estimators=1000, \n", "                               criterion='mse', \n", "                               random_state=1, \n", "                               n_jobs=-1)\n", "forest.fit(X_train, Y_train)\n", "y_train_pred = forest.predict(X_train)\n", "y_test_pred = forest.predict(X_test)\n", "\n", "print('MSE train: %.3f, test: %.3f' % (\n", "        mean_squared_error(Y_train, y_train_pred),\n", "        mean_squared_error(Y_test, y_test_pred)))\n", "print('R^2 train: %.3f, test: %.3f' % (\n", "        r2_score(Y_train, y_train_pred),\n", "        r2_score(Y_test, y_test_pred)))\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.49778082627763265\n"]}], "source": ["print(forest.score(X_test,Y_test))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([8.11403020e+02, 1.00810826e+04, 2.36741366e+03, 1.92515450e+04,\n", "       1.13247600e+02, 1.14762250e+02, 3.07825429e+03, 3.62676330e+03,\n", "       1.27188416e+05, 8.01591147e+05, 2.82478978e+03, 2.60175629e+05,\n", "       6.55914774e+03, 9.93564116e+06, 1.98508238e+03, 2.24197826e+06,\n", "       2.74462002e+03, 1.62484618e+03, 1.33776520e+04, 1.77714950e+02,\n", "       1.28973246e+05, 8.93879130e+02, 2.46759142e+04, 2.46065080e+02,\n", "       2.56263490e+04, 4.57280517e+04, 9.46904140e+02, 4.72281236e+03,\n", "       3.51523075e+07, 2.94407400e+01, 1.67409200e+03, 1.77140640e+02,\n", "       2.03993064e+03, 8.30137120e+02, 1.17031725e+04, 8.29772080e+02,\n", "       6.92223147e+03, 4.29473832e+03, 6.40813701e+03, 1.60681532e+06,\n", "       1.85955550e+03, 5.20850396e+03, 1.77522433e+04, 1.53728599e+04,\n", "       8.45081880e+03, 2.72898316e+03, 5.55906910e+02, 5.46273850e+04,\n", "       3.72148780e+07, 2.49019810e+03, 4.59277400e+01, 1.19240610e+03,\n", "       2.83849763e+05, 8.63127760e+02, 4.99875180e+02, 1.30259960e+02,\n", "       2.48710960e+04, 1.60768840e+02, 7.01834270e+02, 5.04121161e+03,\n", "       4.14965355e+03, 1.13427100e+01, 3.31971448e+05, 4.03291000e+00,\n", "       3.00652770e+02, 4.14561900e+01, 2.46554148e+06, 1.03958610e+02,\n", "       4.59974416e+04, 6.01994190e+02, 6.64630179e+04, 6.58230183e+03,\n", "       2.72052730e+02, 1.45926206e+05, 2.33137408e+04, 7.12064567e+04,\n", "       2.13424900e+02, 6.49068915e+07, 4.24627774e+06, 6.52535220e+02,\n", "       4.19939257e+04, 5.79060000e+01, 7.38927148e+03, 3.08110574e+07,\n", "       6.84045700e+01, 4.68684800e+01, 1.34218400e+02, 8.90032180e+02,\n", "       6.83410254e+05, 1.66134380e+02, 4.58738598e+03, 1.41862683e+03,\n", "       3.11827523e+06, 6.65176756e+03, 4.91790570e+02, 6.54114355e+04,\n", "       3.69176203e+03, 1.22482700e+01, 7.01190835e+03, 1.73476734e+03])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["forest.predict(X_test)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>State_Name</th>\n", "      <th>District_Name</th>\n", "      <th>Crop_Year</th>\n", "      <th>Season</th>\n", "      <th>Crop</th>\n", "      <th>Temperature</th>\n", "      <th>humidity</th>\n", "      <th>soil moisture</th>\n", "      <th>area</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2006</td>\n", "      <td>3</td>\n", "      <td>16</td>\n", "      <td>34</td>\n", "      <td>55</td>\n", "      <td>62</td>\n", "      <td>98.66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2002</td>\n", "      <td>3</td>\n", "      <td>44</td>\n", "      <td>37</td>\n", "      <td>42</td>\n", "      <td>55</td>\n", "      <td>455.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2003</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>35</td>\n", "      <td>50</td>\n", "      <td>59</td>\n", "      <td>497.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>461</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2003</td>\n", "      <td>2</td>\n", "      <td>19</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>50</td>\n", "      <td>15060.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>355</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2001</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>5.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>268</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1999</td>\n", "      <td>1</td>\n", "      <td>38</td>\n", "      <td>35</td>\n", "      <td>50</td>\n", "      <td>59</td>\n", "      <td>37991.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>307</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "      <td>42</td>\n", "      <td>37</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>1647.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2006</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>3.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>392</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2002</td>\n", "      <td>1</td>\n", "      <td>28</td>\n", "      <td>34</td>\n", "      <td>45</td>\n", "      <td>52</td>\n", "      <td>776.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>232</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1998</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>35</td>\n", "      <td>50</td>\n", "      <td>59</td>\n", "      <td>2600.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 9 columns</p>\n", "</div>"], "text/plain": ["     State_Name  District_Name  Crop_Year  Season  Crop  Temperature  \\\n", "105           0              2       2006       3    16           34   \n", "138           0              3       2002       3    44           37   \n", "142           0              3       2003       3     6           35   \n", "461           1              0       2003       2    19           36   \n", "355           1              0       2001       2     1           36   \n", "..          ...            ...        ...     ...   ...          ...   \n", "268           1              0       1999       1    38           35   \n", "307           1              0       2000       1    42           37   \n", "58            0              1       2006       3    15           36   \n", "392           1              0       2002       1    28           34   \n", "232           1              0       1998       1     2           35   \n", "\n", "     humidity  soil moisture      area  \n", "105        55             62     98.66  \n", "138        42             55    455.00  \n", "142        50             59    497.00  \n", "461        41             50  15060.00  \n", "355        40             54      5.00  \n", "..        ...            ...       ...  \n", "268        50             59  37991.00  \n", "307        40             46   1647.00  \n", "58         40             54      3.00  \n", "392        45             52    776.00  \n", "232        50             59   2600.00  \n", "\n", "[100 rows x 9 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["X_test"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([79537.7254])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["forest.predict([[1,5,5,3,40,37,40,46,1359.0]])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["enter state:4\n", "enter district:4\n", "enter year:4\n", "enter season:4\n", "enter crop:4\n", "enter Temperature4\n", "enter humidity4\n", "enter soilmoisture4\n", "enter area:134\n", "[10138.23862]\n", "crop yield Production: [10138.23862]\n"]}], "source": ["state = input('enter state:')\n", "district = input('enter district:')\n", "year = input('enter year:')\n", "season = input('enter season:')\n", "crop = input('enter crop:')\n", "Temperature = input('enter Temperature')\n", "humidity= input('enter humidity')\n", "soilmoisture= input('enter soilmoisture')\n", "area = input('enter area:13')\n", "\n", "out_1 = forest.predict([[float(state),\n", "       float(district),\n", "       float(year),\n", "       float(season),\n", "       float(crop),\n", "       float(Temperature),\n", "       float(humidity),\n", "       float(soilmoisture),\n", "       float(area)]])\n", "print(out_1)\n", "print('crop yield Production:',out_1)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fertilizer prediction"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["data_1 = pd.read_csv('ferlizer.csv')"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n</th>\n", "      <th>p</th>\n", "      <th>k</th>\n", "      <th>amt of n</th>\n", "      <th>amt of p</th>\n", "      <th>amt of k</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>64</td>\n", "      <td>50</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>93</td>\n", "      <td>16</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>63</td>\n", "      <td>20</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>87</td>\n", "      <td>37</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3235</th>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3236</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>93</td>\n", "      <td>16</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3237</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>63</td>\n", "      <td>20</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3238</th>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>87</td>\n", "      <td>37</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3239</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>65</td>\n", "      <td>19</td>\n", "      <td>32</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3240 rows × 6 columns</p>\n", "</div>"], "text/plain": ["      n  p  k  amt of n  amt of p  amt of k\n", "0     3  5  6        64        50        60\n", "1     1  4  2        40        46        30\n", "2     5  1  5        93        16        32\n", "3     3  1  3        63        20        39\n", "4     4  6  6        87        37        39\n", "...  .. .. ..       ...       ...       ...\n", "3235  1  4  2        40        46        30\n", "3236  5  1  5        93        16        32\n", "3237  3  1  3        63        20        39\n", "3238  4  6  6        87        37        39\n", "3239  2  1  1        65        19        32\n", "\n", "[3240 rows x 6 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["data_1"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["n = data_1.iloc[:,0:1]\n", "p = data_1.iloc[:,1:2]\n", "k = data_1.iloc[:,2:3]\n", "amt_n = data_1.iloc[:,3:4]\n", "amt_p = data_1.iloc[:,4:5]\n", "amt_k = data_1.iloc[:,5:6]"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["X_n_train , X_n_test , y_n_train , y_n_test = train_test_split(n,amt_n,test_size=0.2,random_state=100)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["X_p_train , X_p_test , y_p_train , y_p_test = train_test_split(p,amt_p,test_size=0.2,random_state=100)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["X_k_train , X_k_test , y_k_train , y_k_test = train_test_split(k,amt_k,test_size=0.2,random_state=100)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\neural_network\\_multilayer_perceptron.py:1599: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().\n", "  y = column_or_1d(y, warn=True)\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:493: FutureWarning: The feature names should match those that were passed during fit. Starting version 1.2, an error will be raised.\n", "Feature names unseen at fit time:\n", "- amt of n\n", "Feature names seen at fit time, yet now missing:\n", "- n\n", "\n", "  warnings.warn(message, FutureWarning)\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:493: FutureWarning: The feature names should match those that were passed during fit. Starting version 1.2, an error will be raised.\n", "Feature names unseen at fit time:\n", "- amt of n\n", "Feature names seen at fit time, yet now missing:\n", "- n\n", "\n", "  warnings.warn(message, FutureWarning)\n"]}, {"data": {"text/plain": ["1.0"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.neural_network import MLPRegressor\n", "clf_n = MLPRegressor(solver='lbfgs', alpha=1e-5,hidden_layer_sizes=(5, 2), random_state=1)\n", "clf_n.fit(X_n_train, y_n_train)\n", "y_n_pred = clf_n.predict(y_n_test)\n", "clf_n.score(y_n_test,y_n_pred)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\neural_network\\_multilayer_perceptron.py:1599: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().\n", "  y = column_or_1d(y, warn=True)\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:493: FutureWarning: The feature names should match those that were passed during fit. Starting version 1.2, an error will be raised.\n", "Feature names unseen at fit time:\n", "- amt of p\n", "Feature names seen at fit time, yet now missing:\n", "- n\n", "\n", "  warnings.warn(message, FutureWarning)\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:493: FutureWarning: The feature names should match those that were passed during fit. Starting version 1.2, an error will be raised.\n", "Feature names unseen at fit time:\n", "- amt of p\n", "Feature names seen at fit time, yet now missing:\n", "- p\n", "\n", "  warnings.warn(message, FutureWarning)\n"]}, {"data": {"text/plain": ["-1.3101496439657598"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["clf_p = MLPRegressor(solver='lbfgs', alpha=1e-5,hidden_layer_sizes=(5, 2), random_state=1)\n", "clf_p.fit(X_p_train, y_p_train)\n", "y_p_pred = clf_n.predict(y_p_test)\n", "clf_p.score(y_p_test,y_p_pred)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\neural_network\\_multilayer_perceptron.py:1599: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().\n", "  y = column_or_1d(y, warn=True)\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:493: FutureWarning: The feature names should match those that were passed during fit. Starting version 1.2, an error will be raised.\n", "Feature names unseen at fit time:\n", "- amt of k\n", "Feature names seen at fit time, yet now missing:\n", "- n\n", "\n", "  warnings.warn(message, FutureWarning)\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:493: FutureWarning: The feature names should match those that were passed during fit. Starting version 1.2, an error will be raised.\n", "Feature names unseen at fit time:\n", "- amt of k\n", "Feature names seen at fit time, yet now missing:\n", "- p\n", "\n", "  warnings.warn(message, FutureWarning)\n"]}, {"data": {"text/plain": ["-8.612062745927059"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["clf_k = MLPRegressor(solver='lbfgs', alpha=1e-5,hidden_layer_sizes=(5, 2), random_state=1)\n", "clf_k.fit(X_p_train, y_k_train)\n", "y_k_pred = clf_n.predict(y_k_test)\n", "clf_k.score(y_k_test,y_k_pred)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["enter nitrogen:45\n", "enter posporus:45\n", "enter pottasium:45\n", "Amount of nitrogen Fertizer: [604.32031581]\n", "Amount of posporus Fertizer: [268.33727992]\n", "Amount of nitrogen Fertizer: [134.79519645]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:450: UserWarning: X does not have valid feature names, but MLPRegressor was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:450: UserWarning: X does not have valid feature names, but MLPRegressor was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:450: UserWarning: X does not have valid feature names, but MLPRegressor was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["n_i = float(input('enter nitrogen:'))\n", "p_i = float(input('enter posporus:'))\n", "k_i = float(input('enter pottasium:'))\n", "p_n = clf_n.predict([[n_i]])\n", "p_p = clf_p.predict([[p_i]])\n", "p_k = clf_k.predict([[k_i]])\n", "print('Amount of nitrogen Fertizer:',p_n)\n", "print('Amount of posporus Fertizer:',p_p)\n", "print('Amount of nitrogen Fertizer:',p_k)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["state = {\"Andaman and Nicobar Islands\":0,}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# GUI"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:450: UserWarning: X does not have valid feature names, but RandomForestRegressor was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:450: UserWarning: X does not have valid feature names, but RandomForestRegressor was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:450: UserWarning: X does not have valid feature names, but RandomForestRegressor was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:450: UserWarning: X does not have valid feature names, but RandomForestRegressor was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Anaconda3\\envs\\romesh_proj\\lib\\site-packages\\sklearn\\base.py:450: UserWarning: X does not have valid feature names, but RandomForestRegressor was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["from tkinter import *\n", "from tkinter import ttk\n", "\n", "root = Tk()\n", "root.title('Crop Yield and fertilizer Prediction System')\n", "root.geometry('850x650')\n", "root.configure(background=\"purple2\")\n", "var = StringVar()\n", "label = Label( root, textvariable = var,font=('arial',20,'bold'),bd=20,background=\"purple2\")\n", "var.set('Crop Yield and fertilizer Prediction System')\n", "label.grid(row=0,columnspan=6)\n", "\n", "\n", "\n", "label_1 = ttk.Label(root, text ='state',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_1.grid(row=11,column=0)\n", "    \n", "Entry_1= Entry(root)\n", "Entry_1.grid(row=11,column=1)\n", "\n", "label_2 = ttk.Label(root, text ='district',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_2.grid(row=12,column=0)\n", "    \n", "Entry_2 = Entry(root)\n", "Entry_2.grid(row=12,column=1)\n", "    \n", "    \n", "label_3 = ttk.Label(root, text ='year',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_3.grid(row=13,column=0)\n", "    \n", "Entry_3 = Entry(root)\n", "Entry_3.grid(row=13,column=1)\n", "\n", "label_4 = ttk.Label(root, text ='season',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_4.grid(row=14,column=0)\n", "    \n", "Entry_4= Entry(root)\n", "Entry_4.grid(row=14,column=1)\n", "\n", "label_5 = ttk.Label(root, text ='crop',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_5.grid(row=15,column=0)\n", "    \n", "Entry_5 = Entry(root)\n", "Entry_5.grid(row=15,column=1)\n", "    \n", "    \n", "label_6 = ttk.Label(root, text ='Temperature',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_6.grid(row=16,column=0)\n", "    \n", "Entry_6 = Entry(root)\n", "Entry_6.grid(row=16,column=1)\n", "\n", "label_7 = ttk.Label(root, text ='humidity',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_7.grid(row=17,column=0)\n", "    \n", "Entry_7= Entry(root)\n", "Entry_7.grid(row=17,column=1)\n", "\n", "label_8 = ttk.Label(root, text ='soilmoisture',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_8.grid(row=18,column=0)\n", "\n", "Entry_8 = Entry(root)\n", "Entry_8.grid(row=18,column=1)\n", "    \n", "    \n", "label_9 = ttk.Label(root, text ='area',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_9.grid(row=19,column=0)\n", "    \n", "Entry_9 = Entry(root)\n", "Entry_9.grid(row=19,column=1)\n", "\n", "\n", "def predict():\n", "    state = Entry_1.get()\n", "    district = Entry_2.get()\n", "    year = Entry_3.get()\n", "    season = Entry_4.get()\n", "    crop = Entry_5.get()\n", "    Temperature = Entry_6.get()\n", "    humidity = Entry_7.get()\n", "    soilmoisture = Entry_8.get()\n", "    area = Entry_9.get()\n", "    out = forest.predict([[float(state),\n", "       float(district),\n", "       float(year),\n", "       float(season),\n", "       float(crop),\n", "       float(Temperature),\n", "       float(humidity),\n", "       float(soilmoisture),\n", "       float(area)]])\n", "    \n", "    output.delete(0,END)\n", "    output.insert(0,out[0])\n", "   \n", "        \n", "\n", "b1 = Button(root, text = 'predict',font=(\"Helvetica\", 16),background=\"Purple3\",command = predict)\n", "b1.grid(row=20,column=0)\n", "    \n", "\n", "output = Entry(root)\n", "output.grid(row=20,column=1)\n", "    \n", "root.mainloop()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Amount of nitrogen Fertizer required: [94.22616724]\n", "Amount of posporus Fertizer required: [364.29006435]\n", "Amount of nitrogen Fertizer required: [194.35954069]\n"]}], "source": ["# from tkinter import *\n", "from tkinter import ttk\n", "\n", "root = Tk()\n", "root.title('Crop Yield and fertilizer Prediction System')\n", "root.geometry('850x650')\n", "root.configure(background=\"purple2\")\n", "var = StringVar()\n", "label = Label( root, textvariable = var,font=('arial',20,'bold'),bd=20,background=\"purple2\")\n", "var.set('Crop Yield and fertilizer Prediction System')\n", "label.grid(row=0,columnspan=6)\n", "\n", "\n", "\n", "label_1 = ttk.Label(root, text ='nitrogen',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_1.grid(row=11,column=0)\n", "    \n", "Entry_1= Entry(root)\n", "Entry_1.grid(row=11,column=1)\n", "\n", "label_2 = ttk.Label(root, text ='posporus',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_2.grid(row=12,column=0)\n", "    \n", "Entry_2 = Entry(root)\n", "Entry_2.grid(row=12,column=1)\n", "    \n", "    \n", "label_3 = ttk.Label(root, text ='pottasium',font=(\"Helvetica\", 16),background=\"Purple3\")\n", "label_3.grid(row=13,column=0)\n", "    \n", "Entry_3 = Entry(root)\n", "Entry_3.grid(row=13,column=1)\n", "\n", "\n", "\n", "\n", "def predict():\n", "    n_i = Entry_1.get()\n", "    p_i = Entry_2.get()\n", "    k_i = Entry_3.get()\n", "\n", "    p_n = clf_n.predict([[float(n_i)]])\n", "    p_p = clf_p.predict([[float(p_i)]])\n", "    p_k = clf_k.predict([[float(k_i)]])\n", "    print('Amount of nitrogen Fertizer required:',p_n)\n", "    print('Amount of posporus Fertizer required:',p_p)\n", "    print('Amount of nitrogen Fertizer required:',p_k)\n", "    \n", "    s = \"\"\"Amount of nitrogen Fertizer required:{}\\nAmount of posporus Fertizer required:{}\\nAmount of nitrogen Fertizer required:{}\"\"\".format(p_n[0],p_p[0],p_k[0])\n", "    \n", "    output.delete('1.0',END)\n", "    output.insert('1.0',s)\n", "   \n", "        \n", "\n", "b1 = Button(root, text = 'predict',font=(\"Helvetica\", 16),background=\"Purple3\",command = predict)\n", "b1.grid(row=20,column=0)\n", "    \n", "\n", "output = Text(root)\n", "output.grid(row=20,column=1)\n", "    \n", "root.mainloop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 1}