{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Crop yield Prediction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Import necessary libraries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["-->NumPy stands for ‘Numerical Python’ or ‘Numeric Python’. It is an open source module of Python which provides fast mathematical computation on arrays and matrices.\n", "\n", "->Similar to NumPy, Pandas is one of the most widely used python libraries in data science. It provides high-performance, easy to use structures and data analysis tools. Unlike NumPy library which provides objects for multi-dimensional arrays, Pandas provides in-memory 2d table object called Dataframe. It is like a spreadsheet with column names and row labels."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### load the dataset"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'crop_csv_file.xlsx'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-2-6709d507fca4>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mdata\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mread_excel\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'crop_csv_file.xlsx'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\pandas\\util\\_decorators.py\u001b[0m in \u001b[0;36mwrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    297\u001b[0m                 )\n\u001b[0;32m    298\u001b[0m                 \u001b[0mwarnings\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mwarn\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mmsg\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mFutureWarning\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mstacklevel\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mstacklevel\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 299\u001b[1;33m             \u001b[1;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    300\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    301\u001b[0m         \u001b[1;32mreturn\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\pandas\\io\\excel\\_base.py\u001b[0m in \u001b[0;36mread_excel\u001b[1;34m(io, sheet_name, header, names, index_col, usecols, squeeze, dtype, engine, converters, true_values, false_values, skiprows, nrows, na_values, keep_default_na, na_filter, verbose, parse_dates, date_parser, thousands, comment, skipfooter, convert_float, mangle_dupe_cols, storage_options)\u001b[0m\n\u001b[0;32m    334\u001b[0m     \u001b[1;32mif\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[0misinstance\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mio\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mExcelFile\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    335\u001b[0m         \u001b[0mshould_close\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 336\u001b[1;33m         \u001b[0mio\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mExcelFile\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mio\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mstorage_options\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mstorage_options\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mengine\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mengine\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    337\u001b[0m     \u001b[1;32melif\u001b[0m \u001b[0mengine\u001b[0m \u001b[1;32mand\u001b[0m \u001b[0mengine\u001b[0m \u001b[1;33m!=\u001b[0m \u001b[0mio\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mengine\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    338\u001b[0m         raise ValueError(\n", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\pandas\\io\\excel\\_base.py\u001b[0m in \u001b[0;36m__init__\u001b[1;34m(self, path_or_buffer, engine, storage_options)\u001b[0m\n\u001b[0;32m   1069\u001b[0m                 \u001b[0mext\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;34m\"xls\"\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1070\u001b[0m             \u001b[1;32melse\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1071\u001b[1;33m                 ext = inspect_excel_format(\n\u001b[0m\u001b[0;32m   1072\u001b[0m                     \u001b[0mcontent\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mpath_or_buffer\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mstorage_options\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mstorage_options\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1073\u001b[0m                 )\n", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\pandas\\io\\excel\\_base.py\u001b[0m in \u001b[0;36minspect_excel_format\u001b[1;34m(path, content, storage_options)\u001b[0m\n\u001b[0;32m    947\u001b[0m     \u001b[1;32massert\u001b[0m \u001b[0mcontent_or_path\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    948\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 949\u001b[1;33m     with get_handle(\n\u001b[0m\u001b[0;32m    950\u001b[0m         \u001b[0mcontent_or_path\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m\"rb\"\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mstorage_options\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mstorage_options\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mis_text\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mFalse\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    951\u001b[0m     ) as handle:\n", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\pandas\\io\\common.py\u001b[0m in \u001b[0;36mget_handle\u001b[1;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[0;32m    649\u001b[0m         \u001b[1;32melse\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    650\u001b[0m             \u001b[1;31m# Binary mode\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 651\u001b[1;33m             \u001b[0mhandle\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mopen\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mhandle\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mioargs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mmode\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    652\u001b[0m         \u001b[0mhandles\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mhandle\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    653\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'crop_csv_file.xlsx'"]}], "source": ["data = pd.read_excel('crop_csv_file.xlsx')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["49999"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#length of the dataset\n", "len(data)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(49999, 10)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["#shape of the dataset\n", "data.shape"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>State_Name</th>\n", "      <th>District_Name</th>\n", "      <th>Crop_Year</th>\n", "      <th>Season</th>\n", "      <th>Crop</th>\n", "      <th>Temperature</th>\n", "      <th>humidity</th>\n", "      <th>soil moisture</th>\n", "      <th>area</th>\n", "      <th>Production</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Arecanut</td>\n", "      <td>36</td>\n", "      <td>35</td>\n", "      <td>45</td>\n", "      <td>1254.0</td>\n", "      <td>2000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Other Kharif pulses</td>\n", "      <td>37</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Rice</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>50</td>\n", "      <td>102.0</td>\n", "      <td>321.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td>Whole Year</td>\n", "      <td>Banana</td>\n", "      <td>37</td>\n", "      <td>42</td>\n", "      <td>55</td>\n", "      <td>176.0</td>\n", "      <td>641.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Andaman and Nicobar Islands</td>\n", "      <td>NICOBARS</td>\n", "      <td>2000</td>\n", "      <td>Whole Year</td>\n", "      <td>Cashewnut</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>720.0</td>\n", "      <td>165.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49994</th>\n", "      <td>Chhattisgarh</td>\n", "      <td>JANJGIR-CHAMPA</td>\n", "      <td>2006</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Small millets</td>\n", "      <td>36</td>\n", "      <td>35</td>\n", "      <td>45</td>\n", "      <td>27.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49995</th>\n", "      <td>Chhattisgarh</td>\n", "      <td>JANJGIR-CHAMPA</td>\n", "      <td>2006</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Soyabean</td>\n", "      <td>37</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>27.0</td>\n", "      <td>32.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49996</th>\n", "      <td>Chhattisgarh</td>\n", "      <td>JANJGIR-CHAMPA</td>\n", "      <td>2006</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Sunflower</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>50</td>\n", "      <td>450.0</td>\n", "      <td>213.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49997</th>\n", "      <td>Chhattisgarh</td>\n", "      <td>JANJGIR-CHAMPA</td>\n", "      <td>2006</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Urad</td>\n", "      <td>37</td>\n", "      <td>42</td>\n", "      <td>55</td>\n", "      <td>1778.0</td>\n", "      <td>309.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49998</th>\n", "      <td>Chhattisgarh</td>\n", "      <td>JANJGIR-CHAMPA</td>\n", "      <td>2006</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Gram</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>216.0</td>\n", "      <td>194.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>49999 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                        State_Name   District_Name  Crop_Year       Season  \\\n", "0      Andaman and Nicobar Islands        NICOBARS       2000  Kharif        \n", "1      Andaman and Nicobar Islands        NICOBARS       2000  Kharif        \n", "2      Andaman and Nicobar Islands        NICOBARS       2000  Kharif        \n", "3      Andaman and Nicobar Islands        NICOBARS       2000  Whole Year    \n", "4      Andaman and Nicobar Islands        NICOBARS       2000  Whole Year    \n", "...                            ...             ...        ...          ...   \n", "49994                 Chhattisgarh  JANJGIR-CHAMPA       2006  Kharif        \n", "49995                 Chhattisgarh  JANJGIR-CHAMPA       2006  Kharif        \n", "49996                 Chhattisgarh  JANJGIR-CHAMPA       2006  Kharif        \n", "49997                 Chhattisgarh  JANJGIR-CHAMPA       2006  Kharif        \n", "49998                 Chhattisgarh  JANJGIR-CHAMPA       2006  Rabi          \n", "\n", "                      Crop  Temperature  humidity  soil moisture    area  \\\n", "0                 Arecanut           36        35             45  1254.0   \n", "1      Other Kharif pulses           37        40             46     2.0   \n", "2                     Rice           36        41             50   102.0   \n", "3                   Banana           37        42             55   176.0   \n", "4                Cashewnut           36        40             54   720.0   \n", "...                    ...          ...       ...            ...     ...   \n", "49994        Small millets           36        35             45    27.0   \n", "49995             Soyabean           37        40             46    27.0   \n", "49996            Sunflower           36        41             50   450.0   \n", "49997                 <PERSON><PERSON>           37        42             55  1778.0   \n", "49998                 Gram           36        40             54   216.0   \n", "\n", "       Production  \n", "0          2000.0  \n", "1             1.0  \n", "2           321.0  \n", "3           641.0  \n", "4           165.0  \n", "...           ...  \n", "49994         7.0  \n", "49995        32.0  \n", "49996       213.0  \n", "49997       309.0  \n", "49998       194.0  \n", "\n", "[49999 rows x 10 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#print the data\n", "data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Visulization"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\csv\\lib\\site-packages\\seaborn\\_decorators.py:36: FutureWarning: Pass the following variables as keyword args: x, y. From version 0.12, the only valid positional argument will be `data`, and passing other arguments without an explicit keyword will result in an error or misinterpretation.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<AxesSubplot:xlabel='Crop_Year', ylabel='Production'>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#plot a graph for crop_yearand Production\n", "sns.lineplot(data[\"Crop_Year\"],data[\"Production\"])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\csv\\lib\\site-packages\\seaborn\\_decorators.py:36: FutureWarning: Pass the following variables as keyword args: x, y. From version 0.12, the only valid positional argument will be `data`, and passing other arguments without an explicit keyword will result in an error or misinterpretation.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<AxesSubplot:xlabel='Season', ylabel='Production'>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#plot for the seasonal production\n", "sns.barplot(data[\"Season\"],data[\"Production\"])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Production</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Season</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Autumn</th>\n", "      <td>3.187838e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>2.890649e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>2.150024e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Summer</th>\n", "      <td>3.285227e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Whole Year</th>\n", "      <td>1.986870e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Winter</th>\n", "      <td>1.333412e+08</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Production\n", "Season                   \n", "Autumn       3.187838e+07\n", "<PERSON><PERSON><PERSON>       2.890649e+08\n", "Rabi         2.150024e+08\n", "Summer       3.285227e+07\n", "Whole Year   1.986870e+10\n", "Winter       1.333412e+08"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#data grouped by season for Productions\n", "data.groupby(\"Season\",axis=0).agg({\"Production\":np.sum})"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["Rice                 3853\n", "Maize                3249\n", "<PERSON><PERSON>(Green Gram)    2084\n", "Dry chillies         1595\n", "Arhar/Tur            1578\n", "                     ... \n", "Grapes                 17\n", "Varagu                 13\n", "Peas  (vegetable)       3\n", "other fibres            2\n", "Guar seed               1\n", "Name: <PERSON><PERSON>, Length: 80, dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#unique crop value counts\n", "data[\"Crop\"].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Exploration"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Dataset comprises of 49999 observations and 10 characteristics. Out of which one is dependent variable and rest 9 are independent variables — physico-chemical characteristics. It is also a good practice to know the columns and their corresponding data types,along with finding whether they contain null values or not."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 49999 entries, 0 to 49998\n", "Data columns (total 10 columns):\n", " #   Column         Non-Null Count  Dtype  \n", "---  ------         --------------  -----  \n", " 0   State_Name     49999 non-null  object \n", " 1   District_Name  49999 non-null  object \n", " 2   Crop_Year      49999 non-null  int64  \n", " 3   Season         49999 non-null  object \n", " 4   Crop           49999 non-null  object \n", " 5   Temperature    49999 non-null  int64  \n", " 6   humidity       49999 non-null  int64  \n", " 7   soil moisture  49999 non-null  int64  \n", " 8    area          49999 non-null  float64\n", " 9   Production     49784 non-null  float64\n", "dtypes: float64(2), int64(4), object(4)\n", "memory usage: 3.8+ MB\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 49784 entries, 0 to 49998\n", "Data columns (total 10 columns):\n", " #   Column         Non-Null Count  Dtype  \n", "---  ------         --------------  -----  \n", " 0   State_Name     49784 non-null  object \n", " 1   District_Name  49784 non-null  object \n", " 2   Crop_Year      49784 non-null  int64  \n", " 3   Season         49784 non-null  object \n", " 4   Crop           49784 non-null  object \n", " 5   Temperature    49784 non-null  int64  \n", " 6   humidity       49784 non-null  int64  \n", " 7   soil moisture  49784 non-null  int64  \n", " 8    area          49784 non-null  float64\n", " 9   Production     49784 non-null  float64\n", "dtypes: float64(2), int64(4), object(4)\n", "memory usage: 4.2+ MB\n"]}], "source": ["#handling missing data\n", "data = data.dropna()\n", "data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Data has only float and integer values. No variable column has null/missing values. The describe() function in pandas is very handy in getting various summary statistics.This function returns the count, mean, standard deviation, minimum and maximum values and the quantiles of the data."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Crop_Year</th>\n", "      <th>Temperature</th>\n", "      <th>humidity</th>\n", "      <th>soil moisture</th>\n", "      <th>area</th>\n", "      <th>Production</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>49784.000000</td>\n", "      <td>49784.000000</td>\n", "      <td>49784.000000</td>\n", "      <td>49784.000000</td>\n", "      <td>49784.000000</td>\n", "      <td>4.978400e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2005.872087</td>\n", "      <td>34.445746</td>\n", "      <td>44.775269</td>\n", "      <td>53.109312</td>\n", "      <td>7393.028993</td>\n", "      <td>4.132019e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>5.108602</td>\n", "      <td>3.498277</td>\n", "      <td>6.662586</td>\n", "      <td>5.259326</td>\n", "      <td>27983.253218</td>\n", "      <td>1.232609e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1997.000000</td>\n", "      <td>25.000000</td>\n", "      <td>35.000000</td>\n", "      <td>45.000000</td>\n", "      <td>0.200000</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2002.000000</td>\n", "      <td>34.000000</td>\n", "      <td>40.000000</td>\n", "      <td>50.000000</td>\n", "      <td>78.000000</td>\n", "      <td>8.700000e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2006.000000</td>\n", "      <td>36.000000</td>\n", "      <td>42.000000</td>\n", "      <td>54.000000</td>\n", "      <td>441.000000</td>\n", "      <td>6.384400e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2010.000000</td>\n", "      <td>36.000000</td>\n", "      <td>50.000000</td>\n", "      <td>55.000000</td>\n", "      <td>2567.000000</td>\n", "      <td>5.615000e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>2014.000000</td>\n", "      <td>37.000000</td>\n", "      <td>55.000000</td>\n", "      <td>62.000000</td>\n", "      <td>877029.000000</td>\n", "      <td>7.801620e+08</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Crop_Year   Temperature      humidity  soil moisture           area  \\\n", "count  49784.000000  49784.000000  49784.000000   49784.000000   49784.000000   \n", "mean    2005.872087     34.445746     44.775269      53.109312    7393.028993   \n", "std        5.108602      3.498277      6.662586       5.259326   27983.253218   \n", "min     1997.000000     25.000000     35.000000      45.000000       0.200000   \n", "25%     2002.000000     34.000000     40.000000      50.000000      78.000000   \n", "50%     2006.000000     36.000000     42.000000      54.000000     441.000000   \n", "75%     2010.000000     36.000000     50.000000      55.000000    2567.000000   \n", "max     2014.000000     37.000000     55.000000      62.000000  877029.000000   \n", "\n", "         Production  \n", "count  4.978400e+04  \n", "mean   4.132019e+05  \n", "std    1.232609e+07  \n", "min    0.000000e+00  \n", "25%    8.700000e+01  \n", "50%    6.384400e+02  \n", "75%    5.615000e+03  \n", "max    7.801620e+08  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Categorical data handling"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21856\\1024635225.py:9: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data['State_Name'] = State_Name\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21856\\1024635225.py:10: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data['District_Name'] = District_Name\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21856\\1024635225.py:11: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data['Crop'] = crop\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21856\\1024635225.py:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data['Season']  = Season\n"]}], "source": ["from sklearn.preprocessing import LabelEncoder\n", "le = LabelEncoder()\n", "\n", "State_Name = le.fit_transform(data.State_Name)\n", "District_Name = le.fit_transform(data.District_Name)\n", "#Crop_Year = le.fit_transform(data.Crop_Year)\n", "crop = le.fit_transform(data.Crop)\n", "Season = le.fit_transform(data.Season)\n", "data['State_Name'] = State_Name\n", "data['District_Name'] = District_Name\n", "data['Crop'] = crop\n", "data['Season']  = Season"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>State_Name</th>\n", "      <th>District_Name</th>\n", "      <th>Crop_Year</th>\n", "      <th>Season</th>\n", "      <th>Crop</th>\n", "      <th>Temperature</th>\n", "      <th>humidity</th>\n", "      <th>soil moisture</th>\n", "      <th>area</th>\n", "      <th>Production</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>78</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>36</td>\n", "      <td>35</td>\n", "      <td>45</td>\n", "      <td>1254.0</td>\n", "      <td>2000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>78</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "      <td>46</td>\n", "      <td>37</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>78</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "      <td>59</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>50</td>\n", "      <td>102.0</td>\n", "      <td>321.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>78</td>\n", "      <td>2000</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>37</td>\n", "      <td>42</td>\n", "      <td>55</td>\n", "      <td>176.0</td>\n", "      <td>641.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>78</td>\n", "      <td>2000</td>\n", "      <td>4</td>\n", "      <td>12</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>720.0</td>\n", "      <td>165.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49994</th>\n", "      <td>6</td>\n", "      <td>46</td>\n", "      <td>2006</td>\n", "      <td>1</td>\n", "      <td>65</td>\n", "      <td>36</td>\n", "      <td>35</td>\n", "      <td>45</td>\n", "      <td>27.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49995</th>\n", "      <td>6</td>\n", "      <td>46</td>\n", "      <td>2006</td>\n", "      <td>1</td>\n", "      <td>66</td>\n", "      <td>37</td>\n", "      <td>40</td>\n", "      <td>46</td>\n", "      <td>27.0</td>\n", "      <td>32.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49996</th>\n", "      <td>6</td>\n", "      <td>46</td>\n", "      <td>2006</td>\n", "      <td>1</td>\n", "      <td>68</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>50</td>\n", "      <td>450.0</td>\n", "      <td>213.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49997</th>\n", "      <td>6</td>\n", "      <td>46</td>\n", "      <td>2006</td>\n", "      <td>1</td>\n", "      <td>74</td>\n", "      <td>37</td>\n", "      <td>42</td>\n", "      <td>55</td>\n", "      <td>1778.0</td>\n", "      <td>309.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49998</th>\n", "      <td>6</td>\n", "      <td>46</td>\n", "      <td>2006</td>\n", "      <td>2</td>\n", "      <td>24</td>\n", "      <td>36</td>\n", "      <td>40</td>\n", "      <td>54</td>\n", "      <td>216.0</td>\n", "      <td>194.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>49784 rows × 10 columns</p>\n", "</div>"], "text/plain": ["       State_Name  District_Name  Crop_Year  Season  Crop  Temperature  \\\n", "0               0             78       2000       1     0           36   \n", "1               0             78       2000       1    46           37   \n", "2               0             78       2000       1    59           36   \n", "3               0             78       2000       4     3           37   \n", "4               0             78       2000       4    12           36   \n", "...           ...            ...        ...     ...   ...          ...   \n", "49994           6             46       2006       1    65           36   \n", "49995           6             46       2006       1    66           37   \n", "49996           6             46       2006       1    68           36   \n", "49997           6             46       2006       1    74           37   \n", "49998           6             46       2006       2    24           36   \n", "\n", "       humidity  soil moisture    area  Production  \n", "0            35             45  1254.0      2000.0  \n", "1            40             46     2.0         1.0  \n", "2            41             50   102.0       321.0  \n", "3            42             55   176.0       641.0  \n", "4            40             54   720.0       165.0  \n", "...         ...            ...     ...         ...  \n", "49994        35             45    27.0         7.0  \n", "49995        40             46    27.0        32.0  \n", "49996        41             50   450.0       213.0  \n", "49997        42             55  1778.0       309.0  \n", "49998        40             54   216.0       194.0  \n", "\n", "[49784 rows x 10 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 864x720 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["corr = data.corr()\n", "\n", "plt.figure(figsize=(12,10))\n", "sns.heatmap(corr, annot=True, cmap='rainbow')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 2, 3, 4, 5, 6])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["data['State_Name'].unique()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 78,  79,  96,   0,  24,  35,  43,  49,  59,  60,  83,  97,  98,\n", "       107, 108, 109,   1,  22,  31,  36,  37,  61,  64,  65,  66,  67,\n", "        76,  80, 100, 102, 104, 105, 110, 111,   5,  10,  18,  20,  23,\n", "        27,  29,  30,  32,  33,  40,  41,  44,  48,  51,  52,  53,  54,\n", "        58,  62,  70,  73,  75,  93,  95, 101, 103,   2,   3,   4,   9,\n", "        12,  14,  15,  19,  26,  39,  42,  45,  47,  50,  55,  56,  57,\n", "        63,  68,  69,  71,  72,  74,  77,  81,  82,  84,  85,  86,  87,\n", "        88,  89,  90,  91,  92,  94,  99, 106,  21,   6,   7,   8,  11,\n", "        13,  16,  17,  25,  28,  34,  38,  46])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["data['District_Name'].unique()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 4, 0, 2, 3, 5])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["data['Season'].unique()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0, 46, 59,  3, 12, 15, 21, 67, 69, 70,  7, 20, 79, 73, 35, 39, 74,\n", "        1, 26, 68,  2, 13, 17, 28, 29, 32, 57, 71, 24, 76, 37, 64, 34, 60,\n", "       42, 78, 61, 65, 16, 55, 44, 66,  5,  6, 10, 14, 19, 25, 36, 43, 77,\n", "       45, 47, 49, 53, 72, 38, 18, 33, 54, 63, 11, 58, 50, 40,  9, 75, 22,\n", "       23, 41, 56, 30, 51,  8, 48, 52,  4, 62, 31, 27])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["data['Crop'].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Splitting the dataset for train and test"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "#from sklearn.cross_validation import train_test_split"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["X = data.iloc[:,:-1]\n", "y = data.iloc[:,-1]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["X_train,X_test,Y_train,Y_test = train_test_split(X,y,test_size=0.2,random_state=100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Apply algorithm to fit model"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\csv\\lib\\site-packages\\sklearn\\ensemble\\_forest.py:396: FutureWarning: Criterion 'mse' was deprecated in v1.0 and will be removed in version 1.2. Use `criterion='squared_error'` which is equivalent.\n", "  warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["MSE train: 1469428470792.599, test: 4557059304917.332\n", "R^2 train: 0.990, test: 0.970\n"]}], "source": ["from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.metrics import roc_auc_score , classification_report, mean_squared_error, r2_score\n", "forest = RandomForestRegressor(n_estimators=1000, \n", "                               criterion='mse', \n", "                               random_state=1, \n", "                               n_jobs=-1)\n", "forest.fit(X_train, Y_train)\n", "y_train_pred = forest.predict(X_train)\n", "y_test_pred = forest.predict(X_test)\n", "\n", "print('MSE train: %.3f, test: %.3f' % (\n", "        mean_squared_error(Y_train, y_train_pred),\n", "        mean_squared_error(Y_test, y_test_pred)))\n", "print('R^2 train: %.3f, test: %.3f' % (\n", "        r2_score(Y_train, y_train_pred),\n", "        r2_score(Y_test, y_test_pred)))\n", "\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9702724954406419\n"]}], "source": ["print(forest.score(X_test,Y_test))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["score_rf=forest.score(X_test,Y_test)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["score_rf=forest.score(X_train,Y_train)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9903064206313974"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["score_rf"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 304.83275, 1475.626  ,  125.21396, ...,  931.128  ,   86.255  ,\n", "         14.372  ])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["forest.predict(X_test)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import pickle\n", "with open('rf.pkl', 'wb') as file:\n", "   pickle.dump(forest, file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model2 "]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["DecisionTreeRegressor(random_state=0)"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["# import the regressor\n", "from sklearn.tree import DecisionTreeRegressor \n", "  \n", "# create a regressor object\n", "regressor = DecisionTreeRegressor(random_state = 0) \n", "  \n", "# fit the regressor with X and Y data\n", "regressor.fit(X_train, Y_train)"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["Y_pred_regressor = regressor.predict(X_test)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["score_dt=regressor.score(X_train, Y_train)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MSE train: 0.000, test: 8905677857384.344\n", "R^2 train: 1.000, test: 0.942\n"]}], "source": ["from sklearn.metrics import roc_auc_score , classification_report, mean_squared_error, r2_score\n", "y_train_pred = regressor.predict(X_train)\n", "y_test_pred = regressor.predict(X_test)\n", "\n", "print('MSE train: %.3f, test: %.3f' % (\n", "        mean_squared_error(Y_train, y_train_pred),\n", "        mean_squared_error(Y_test, y_test_pred)))\n", "print('R^2 train: %.3f, test: %.3f' % (\n", "        r2_score(Y_train, y_train_pred),\n", "        r2_score(Y_test, y_test_pred)))"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["import pickle\n", "with open('decision.pkl', 'wb') as file:\n", "   pickle.dump(regressor, file)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the accuracy score achieved usingSVMis :-0.0011517278279586929 %\n", "the accuracy score achieved usinglinear regressoris :0.007087406566488652 %\n", "the accuracy score achieved usingrandom forestis :0.9702724954406419 %\n", "the accuracy score achieved usingDecisiontreeis :1.0 %\n"]}], "source": ["scores = [score_rf,score_dt]\n", "algorithems = ['random forest','Decisiontree']\n", "\n", "for i in range(len(algorithems)):\n", "    print('the accuracy score achieved using' +algorithems[i]+ 'is :'+str(scores[i])+\" %\")"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\csv\\lib\\site-packages\\seaborn\\_decorators.py:36: FutureWarning: Pass the following variables as keyword args: x, y. From version 0.12, the only valid positional argument will be `data`, and passing other arguments without an explicit keyword will result in an error or misinterpretation.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<AxesSubplot:xlabel='algo', ylabel='score'>"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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****************************************************/+U1GjhyZ4cOHN4UmVNLy5cvz0ksvpWvXrunfv3+OPPLIJMkbb7yRli1b5o477lhp+zlz5mT99ddvWv7CF76Q+++/P9OmTcuvf/3r/PSnP83999/ftP6dHnv+dcy907H7Tv613bs9fj755JP5yle+kh//+MdJkrfeeitLly79wD8LVuXDWGi2tm3b5rLLLmv6LWVRFHnxxRebzloMHDgwDz74YNMnc/67li1bZrfddsvZZ5+dvn37fqSzw5rStWvXPPjgg5k/f36Kosjo0aPzs5/9LM8++2w222yzHHHEEdl+++3zyCOPND1RbK5u3bpl/Pjxqa+vz9KlS3PIIYfk6aefTvfu3TNlypQsX748S5YsydSpU1NVVdXs619yySWZNGlSBg4cmLPPPjvPP/98nn/++QwZMiQ777xzTjvttGy++eZ56aWX0q1bt6YnBgsWLMjDDz+80nubYHU8+uijOeqoo9KnT5/Mnj07c+bMeddfXiTJ1772tUyePDnLly/PokWLMm3atCTJLrvskl/96ldZuHBhkmT8+PHp2rXrh57v7ff7WbNm5Q9/+EN22GGHlbbZaKON8oUvfKEp9B599NEceuihWbFiRfbZZ5906NAhxxxzTPbbb7/MmDEjrVq1WilCYU1rbGzM1Vdfne233z6DBg3KpEmTsnTp0qxYsSLHH398Hnjggey000555JFHmi4/+eST86c//alpH7fcckuuvvrq9OnTJ+ecc04WLFiQxYsXN63v1q1b7r333ixbtiwrVqzIhAkT0q1bt/ecq2XLlu/4+Pduj5/bb799/vjHP+all15KkvzoRz/KJZdcsoZ+Sus2Z/Rotm7dumX48OE59thjU19fnyTZfffdm07z/8d//Ec6dOiQxsbGfP7zn3/HffTp0yeTJk3Knnvu+ZHNDWvSVlttleHDh+fwww9PY2Njtt566xx99NFZsWJFbrvttvTt2zetW7dOly5d8pe//OUD7Xvw4MH529/+loEDB2bFihXZf//9m57E/uEPf8jAgQPzqU99KjU1Ne94hvDdrv+f//mfOfnkk3PnnXemZcuWOeecc7LNNttkhx12SL9+/bL++utn6623To8ePbLLLrtk9OjR6d+/fxoaGnLsscdm2223zZ///Oc18vNj3XTMMcfk1FNPzcYbb5yOHTtmu+22e8+XNu6999559tln069fv3zmM5/J5ptvnuSfx98xxxyTww47LPX19dl2221z7rnnfuj5zjzzzJx99tlNf8LhvPPOS01NzSrbXXrppRk9enSuu+66rLfeerniiiuy3nrrZcSIETnyyCPTtm3bbLzxxrn44ovz6U9/Op/73Ody2GGHVeyTPVn31NbWZr/99kuSpsegyy67LO3bt8/MmTNz4IEHpqGhIbvvvnsGDhyYqqqqDBkyJIMHD05jY2N69eqVXXfdNXfffXeSZMCAATnppJPSv3//tGrVKsOHD1/pE9F79uyZGTNmZNCgQVmxYkV23333DBkyJK+99tq7zvj1r389Rx99dK677rqVLn+3x882bdrkggsuyHe/+900NjamU6dOufTSSyvw01v3VBXvdp4VgI+F//3f/83LL7+cgQMHpr6+PgcddFAuuOCCbLXVVmt7NADgY0roAXzMLVy4MCeffHLmzp2boigyYMCAHHXUUWt7LADgY0zoAQAAlIwPYwEAACgZoQcAAFAyQg8AAKBkhB4ANMPpp5+e66+/fm2PAQDNIvQAAABKxh9MB4C3aWxszAUXXJCnn346S5cuTVEUOe+881ba5je/+U3GjBmTFi1aZOutt85jjz2WcePGZdNNN80111yTe++9Ny1btswXv/jFnHXWWamurl5L3w0A6ypn9ADgbZ5++unU1tbm9ttvz3333ZeBAwdm7NixTetff/31nHrqqbn00kszadKkdO3aNXPmzEmSTJgwIdOmTcsdd9yRe+65J1tssUVOP/30tfWtALAOc0YPAN5mxx13zKc+9an8/Oc/z6xZs/LEE09kgw02SPv27ZMkv//977P55ptnq622SpIMHDiw6YzfI488kv333z/t2rVLkgwdOjQ//vGPs3z58rRu3XqtfD8ArJuc0QOAt5k6dWqOOeaYJMlee+2Vgw8+eKX1LVu2TFEUK13WosU/H07//fLGxsasWLGigtMCwDsTegDwNo8++mh69uyZQw45JJ07d85DDz2UhoaGpvVf+cpX8vLLL2fmzJlJkgceeCBvvPFGqqqqsttuu2XixIl58803kyQ333xzdt55Z2fzAPjIeekmALzN4MGDc8opp6R///5p2bJldtppp0yZMiWbbrppkqR9+/a5/PLLc9ppp6VFixbZbrvt0qpVq6y//vo54IADMnv27Hzzm99MY2NjNttss4wZM2Ytf0cArIuqin9/nQkA8K6WLFmSH/3oRznhhBOy/vrr57nnnssxxxyTadOmpaqqam2PBwBJnNEDgA9kww03zHrrrZcDDjggrVq1SqtWrXLllVeKPAA+VpzRAwAAKBkfxgIAAFAyQg8AAKBkhB4AAEDJCD0AAICSEXoAAAAl8/8B2CsdqdevHBoAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1080x576 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "sns.set(rc={'figure.figsize':(15,8)})\n", "plt.xlabel('algo')\n", "plt.ylabel('score')\n", "sns.barplot(algorithems,scores)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["state = input('enter State:--->')\n", "district = input('enter District:--->')\n", "year = input('enter Year:--->')\n", "season = input('enter Season:--->')\n", "crop = input('enter Crop:--->')\n", "Temperature = input('enter Temperature:--->')\n", "humidity= input('enter Humidity:--->')\n", "soilmoisture= input('enter Soilmoisture:---->')\n", "area = input('enter area(in Hectares):--->')\n", "\n", "out_1 = forest.predict([[float(state),\n", "       float(district),\n", "       float(year),\n", "       float(season),\n", "       float(crop),\n", "       float(Temperature),\n", "       float(humidity),\n", "       float(soilmoisture),\n", "       float(area)]])\n", "print(out_1)\n", "print('crop yield Production(in Tones):',out_1)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# GUI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tkinter import *\n", "from tkinter import ttk\n", "\n", "root = Tk()\n", "root.title('Crop Yield Prediction')\n", "root.geometry('850x650')\n", "root.configure(background=\"green\")\n", "var = StringVar()\n", "label = Label( root, textvariable = var,font=('arial',20,'bold'),bd=20,background=\"green\")\n", "var.set('Crop Yield Prediction')\n", "label.grid(row=0,columnspan=6)\n", "\n", "\n", "\n", "label_1 = ttk.Label(root, text ='State',font=(\"Helvetica\", 16),background=\"green\")\n", "label_1.grid(row=11,column=0)\n", "    \n", "Entry_1= Entry(root)\n", "Entry_1.grid(row=11,column=1)\n", "\n", "label_2 = ttk.Label(root, text ='District',font=(\"Helvetica\", 16),background=\"green\")\n", "label_2.grid(row=12,column=0)\n", "    \n", "Entry_2 = Entry(root)\n", "Entry_2.grid(row=12,column=1)\n", "    \n", "    \n", "label_3 = ttk.Label(root, text ='Year',font=(\"Helvetica\", 16),background=\"green\")\n", "label_3.grid(row=13,column=0)\n", "    \n", "Entry_3 = Entry(root)\n", "Entry_3.grid(row=13,column=1)\n", "\n", "label_4 = ttk.Label(root, text ='Season',font=(\"Helvetica\", 16),background=\"green\")\n", "label_4.grid(row=14,column=0)\n", "    \n", "Entry_4= Entry(root)\n", "Entry_4.grid(row=14,column=1)\n", "\n", "label_5 = ttk.Label(root, text ='Crop',font=(\"Helvetica\", 16),background=\"green\")\n", "label_5.grid(row=15,column=0)\n", "    \n", "Entry_5 = Entry(root)\n", "Entry_5.grid(row=15,column=1)\n", "    \n", "    \n", "label_6 = ttk.Label(root, text ='Temperature',font=(\"Helvetica\", 16),background=\"green\")\n", "label_6.grid(row=16,column=0)\n", "    \n", "Entry_6 = Entry(root)\n", "Entry_6.grid(row=16,column=1)\n", "\n", "label_7 = ttk.Label(root, text ='Humidity',font=(\"Helvetica\", 16),background=\"green\")\n", "label_7.grid(row=17,column=0)\n", "    \n", "Entry_7= Entry(root)\n", "Entry_7.grid(row=17,column=1)\n", "\n", "label_8 = ttk.Label(root, text ='Soilmoisture',font=(\"Helvetica\", 16),background=\"green\")\n", "label_8.grid(row=18,column=0)\n", "\n", "Entry_8 = Entry(root)\n", "Entry_8.grid(row=18,column=1)\n", "    \n", "    \n", "label_9 = ttk.Label(root, text ='Area(in Hectares)',font=(\"Helvetica\", 16),background=\"green\")\n", "label_9.grid(row=19,column=0)\n", "    \n", "Entry_9 = Entry(root)\n", "Entry_9.grid(row=19,column=1)\n", "\n", "\n", "def predict():\n", "    state = Entry_1.get()\n", "    district = Entry_2.get()\n", "    year = Entry_3.get()\n", "    season = Entry_4.get()\n", "    crop = Entry_5.get()\n", "    Temperature = Entry_6.get()\n", "    humidity = Entry_7.get()\n", "    soilmoisture = Entry_8.get()\n", "    area = Entry_9.get()\n", "    out = forest.predict([[float(state),\n", "       float(district),\n", "       float(year),\n", "       float(season),\n", "       float(crop),\n", "       float(Temperature),\n", "       float(humidity),\n", "       float(soilmoisture),\n", "       float(area)]])\n", "    \n", "    output.delete(0,END)\n", "    output.insert(0,out[0])\n", "   \n", "   \n", "\n", "b1 = Button(root, text = 'Production(in Tones)',font=(\"Helvetica\", 16),background=\"gray\",command = predict)\n", "b1.grid(row=20,column=0)\n", "    \n", "\n", "\n", "output = Entry(root)\n", "output.grid(row=20,column=1)\n", "    \n", "root.mainloop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 1}