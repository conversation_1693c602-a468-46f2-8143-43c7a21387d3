# Importing essential libraries and modules

from flask import Flask, render_template, request, redirect, session, url_for, Response, jsonify
from flask_socketio import SocketIO, emit, join_room, leave_room
from markupsafe import Markup
import numpy as np
import pandas as pd
import pickle
import io
import requests
import config
import sqlite3
import random
import warnings
import threading
import time
import os
from datetime import datetime
from bs4 import BeautifulSoup
import base64

# Get the directory of the current script
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
print("🚀 Starting AgroPro Flask Application...")

# ML and Image Processing imports
try:
    import torch
    from torchvision import transforms
    TORCH_AVAILABLE = True
except ImportError:
    print("Warning: PyTorch not available, using fallback for disease detection")
    TORCH_AVAILABLE = False

from PIL import Image

# Import custom modules
from disease import disease_dic
from fertilizer import fertilizer_dic

# Market API import with error handling
try:
    from market_api import market_api
    MARKET_API_AVAILABLE = True
    print("✅ Market API imported successfully")
except ImportError as e:
    print(f"Warning: Market API not available: {e}")
    MARKET_API_AVAILABLE = False
    market_api = None

try:
    from model import ResNet9
    MODEL_CLASS_AVAILABLE = True
except ImportError:
    print("Warning: ResNet9 model class not available, using fallback")
    MODEL_CLASS_AVAILABLE = False

from warnings import filterwarnings
filterwarnings('ignore')
# Load ML model with error handling
try:
    yield_model_path = os.path.join(SCRIPT_DIR, 'models', 'yield_rf.pkl')
    forest = pickle.load(open(yield_model_path, 'rb'))  # yield
    print("✅ Yield model loaded successfully")
except:
    print("Warning: Could not load yield model, using fallback")
    forest = None

try:
    price_model_path = os.path.join(SCRIPT_DIR, 'models', 'forest.pkl')
    cp = pickle.load(open(price_model_path, 'rb'))  # price
    print("✅ Price model loaded successfully")
except:
    print("Warning: Could not load price model, using fallback")
    cp = None

try:
    classifier_model_path = os.path.join(SCRIPT_DIR, 'models', 'classifier.pkl')
    model = pickle.load(open(classifier_model_path,'rb'))
    print("✅ Classifier model loaded successfully")
except:
    print("Warning: Could not load classifier model, using fallback")
    model = None

try:
    fertilizer_model_path = os.path.join(SCRIPT_DIR, 'models', 'fertilizer.pkl')
    ferti = pickle.load(open(fertilizer_model_path,'rb'))
    print("✅ Fertilizer model loaded successfully")
except:
    print("Warning: Could not load fertilizer model, using fallback")
    ferti = None

# Loading crop recommendation model
try:
    import warnings
    warnings.filterwarnings('ignore', category=UserWarning)
    model_path = os.path.join(SCRIPT_DIR, 'models', 'RandomForest.pkl')
    cr = pickle.load(open(model_path, 'rb'))
    print("✅ Crop recommendation model loaded successfully")
except Exception as e:
    print(f"⚠️ Warning: Could not load crop recommendation model: {e}")
    cr = None

# Crop information with images and descriptions
def get_crop_info(crop_name):
    """Get comprehensive information about a crop including image URL"""
    crop_info = {
        'rice': {
            'image': '/static/images/Rice.jpg',
            'emoji': '🌾',
            'description': 'Rice is one of the most important staple crops worldwide, requiring abundant water and warm climate.',
            'season': 'Kharif (Monsoon)',
            'duration': '3-6 months',
            'ideal_temp': '20-37°C',
            'water_req': 'High',
            'soil_type': 'Clay, Loamy'
        },
        'wheat': {
            'image': '/static/images/Wheat.webp',
            'emoji': '🌾',
            'description': 'Wheat is a major cereal grain, widely cultivated for its seed which is used to make flour.',
            'season': 'Rabi (Winter)',
            'duration': '4-6 months',
            'ideal_temp': '10-25°C',
            'water_req': 'Moderate',
            'soil_type': 'Loamy, Clay Loam'
        },
        'maize': {
            'image': '/static/images/Maize.jpg',
            'emoji': '🌽',
            'description': 'Maize (corn) is a versatile crop used for food, feed, and industrial purposes.',
            'season': 'Kharif & Rabi',
            'duration': '3-5 months',
            'ideal_temp': '21-27°C',
            'water_req': 'Moderate',
            'soil_type': 'Well-drained Loamy'
        },
        'cotton': {
            'image': '/static/images/Cotton.jpg',
            'emoji': '🤍',
            'description': 'Cotton is a major cash crop, primarily grown for its fiber used in textile industry.',
            'season': 'Kharif',
            'duration': '5-6 months',
            'ideal_temp': '21-30°C',
            'water_req': 'High',
            'soil_type': 'Black Cotton Soil'
        },
        'sugarcane': {
            'image': '/static/images/crops/sugarcane.svg',
            'emoji': '🎋',
            'description': 'Sugarcane is a tall tropical plant used primarily for sugar production.',
            'season': 'Year-round',
            'duration': '10-18 months',
            'ideal_temp': '20-26°C',
            'water_req': 'Very High',
            'soil_type': 'Rich Loamy'
        },
        'jute': {
            'image': '/static/images/Jute.jpg',
            'emoji': '🌿',
            'description': 'Jute is a fiber crop grown in warm humid climate, used for making sacks and ropes.',
            'season': 'Kharif',
            'duration': '4-6 months',
            'ideal_temp': '24-37°C',
            'water_req': 'High',
            'soil_type': 'Alluvial'
        },
        'coconut': {
            'image': '/static/images/crops/default.svg',
            'emoji': '🥥',
            'description': 'Coconut is a tropical crop providing oil, fiber, and food products.',
            'season': 'Year-round',
            'duration': 'Perennial',
            'ideal_temp': '27-30°C',
            'water_req': 'High',
            'soil_type': 'Sandy Loam'
        },
        'apple': {
            'image': '/static/images/Apple.jpg',
            'emoji': '🍎',
            'description': 'Apple is a temperate fruit crop requiring cool climate for optimal growth.',
            'season': 'Perennial',
            'duration': 'Perennial',
            'ideal_temp': '15-25°C',
            'water_req': 'Moderate',
            'soil_type': 'Well-drained Loamy'
        },
        'banana': {
            'image': '/static/images/Banana.jpg',
            'emoji': '🍌',
            'description': 'Banana is a tropical fruit crop that grows year-round in suitable climates.',
            'season': 'Year-round',
            'duration': '9-12 months',
            'ideal_temp': '26-30°C',
            'water_req': 'High',
            'soil_type': 'Rich Loamy'
        },
        'grapes': {
            'image': '/static/images/Grapes.jpg',
            'emoji': '🍇',
            'description': 'Grapes are grown for fresh consumption, wine production, and raisins.',
            'season': 'Year-round',
            'duration': 'Perennial',
            'ideal_temp': '15-25°C',
            'water_req': 'Moderate',
            'soil_type': 'Well-drained Sandy Loam'
        },
        'mango': {
            'image': '/static/images/Mango.jpg',
            'emoji': '🥭',
            'description': 'Mango is the king of fruits, grown in tropical and subtropical regions.',
            'season': 'Perennial',
            'duration': 'Perennial',
            'ideal_temp': '24-27°C',
            'water_req': 'Moderate',
            'soil_type': 'Well-drained Loamy'
        },
        'orange': {
            'image': "/static/images/Orange'.jfif",
            'emoji': '🍊',
            'description': 'Orange is a citrus fruit rich in vitamin C, grown in subtropical regions.',
            'season': 'Perennial',
            'duration': 'Perennial',
            'ideal_temp': '15-30°C',
            'water_req': 'Moderate',
            'soil_type': 'Well-drained Sandy Loam'
        },
        'papaya': {
            'image': '/static/images/Papaya.jpg',
            'emoji': '🧡',
            'description': 'Papaya is a tropical fruit crop that grows quickly in warm climates.',
            'season': 'Year-round',
            'duration': '6-12 months',
            'ideal_temp': '25-30°C',
            'water_req': 'Moderate',
            'soil_type': 'Well-drained Loamy'
        },
        'pomegranate': {
            'image': '/static/images/Pomegranate.jpg',
            'emoji': '🍎',
            'description': 'Pomegranate is a drought-tolerant fruit crop with high antioxidant content.',
            'season': 'Perennial',
            'duration': 'Perennial',
            'ideal_temp': '15-35°C',
            'water_req': 'Low to Moderate',
            'soil_type': 'Well-drained Sandy Loam'
        },
        'coffee': {
            'image': '/static/images/Coffee.jpg',
            'emoji': '☕',
            'description': 'Coffee is a cash crop grown in tropical highlands for its beans.',
            'season': 'Perennial',
            'duration': 'Perennial',
            'ideal_temp': '15-24°C',
            'water_req': 'High',
            'soil_type': 'Well-drained Loamy'
        },
        'blackgram': {
            'image': '/static/images/Blackgram.jpg',
            'emoji': '🫘',
            'description': 'Blackgram is a protein-rich pulse crop, widely used in Indian cuisine.',
            'season': 'Kharif & Rabi',
            'duration': '2-3 months',
            'ideal_temp': '25-35°C',
            'water_req': 'Low to Moderate',
            'soil_type': 'Well-drained Loamy'
        },
        'chickpea': {
            'image': '/static/images/Chickpea.jpg',
            'emoji': '🫘',
            'description': 'Chickpea is a protein-rich legume crop, widely cultivated in dry regions.',
            'season': 'Rabi (Winter)',
            'duration': '3-4 months',
            'ideal_temp': '20-30°C',
            'water_req': 'Low',
            'soil_type': 'Well-drained Clay Loam'
        },
        'kidneybeans': {
            'image': '/static/images/Kidneybeans.jpg',
            'emoji': '🫘',
            'description': 'Kidney beans are protein-rich legumes used in various cuisines worldwide.',
            'season': 'Kharif & Rabi',
            'duration': '3-4 months',
            'ideal_temp': '15-25°C',
            'water_req': 'Moderate',
            'soil_type': 'Well-drained Loamy'
        },
        'lentil': {
            'image': '/static/images/Lentil.jpeg',
            'emoji': '🫘',
            'description': 'Lentil is a highly nutritious pulse crop, rich in protein and fiber.',
            'season': 'Rabi (Winter)',
            'duration': '3-4 months',
            'ideal_temp': '18-30°C',
            'water_req': 'Low',
            'soil_type': 'Well-drained Clay Loam'
        },
        'mothbeans': {
            'image': '/static/images/Mothbeans.jpg',
            'emoji': '🫘',
            'description': 'Moth beans are drought-tolerant legumes grown in arid regions.',
            'season': 'Kharif',
            'duration': '2-3 months',
            'ideal_temp': '25-35°C',
            'water_req': 'Very Low',
            'soil_type': 'Sandy Loam'
        },
        'mungbean': {
            'image': '/static/images/Mungbean.jpg',
            'emoji': '🫘',
            'description': 'Mung beans are quick-growing legumes used for food and green manure.',
            'season': 'Kharif & Summer',
            'duration': '2-3 months',
            'ideal_temp': '25-35°C',
            'water_req': 'Low to Moderate',
            'soil_type': 'Well-drained Loamy'
        },
        'pigeonpeas': {
            'image': '/static/images/Pigeonpeas.jpg',
            'emoji': '🫘',
            'description': 'Pigeon peas are drought-tolerant legumes, important for soil fertility.',
            'season': 'Kharif',
            'duration': '4-6 months',
            'ideal_temp': '20-35°C',
            'water_req': 'Low',
            'soil_type': 'Well-drained Red Soil'
        },
        'muskmelon': {
            'image': '/static/images/Muskmelon.jpg',
            'emoji': '🍈',
            'description': 'Muskmelon is a sweet fruit crop grown in warm, dry climates.',
            'season': 'Summer',
            'duration': '3-4 months',
            'ideal_temp': '25-35°C',
            'water_req': 'Moderate',
            'soil_type': 'Well-drained Sandy Loam'
        },
        'watermelon': {
            'image': '/static/images/Watermelon.jpg',
            'emoji': '🍉',
            'description': 'Watermelon is a refreshing fruit crop requiring warm climate and good drainage.',
            'season': 'Summer',
            'duration': '3-4 months',
            'ideal_temp': '25-35°C',
            'water_req': 'Moderate',
            'soil_type': 'Well-drained Sandy Loam'
        }
    }
    
    crop_lower = crop_name.lower().strip()
    return crop_info.get(crop_lower, {
        'image': '/static/images/crops/default.svg',
        'emoji': '🌱',
        'description': f'{crop_name.title()} is recommended based on your soil and climate conditions.',
        'season': 'Variable',
        'duration': 'Variable',
        'ideal_temp': 'Variable',
        'water_req': 'Moderate',
        'soil_type': 'Well-drained'
    })

disease_classes = ['Apple___Apple_scab',
                   'Apple___Black_rot',
                   'Apple___Cedar_apple_rust',
                   'Apple___healthy',
                   'Blueberry___healthy',
                   'Cherry_(including_sour)___Powdery_mildew',
                   'Cherry_(including_sour)___healthy',
                   'Corn_(maize)___Cercospora_leaf_spot Gray_leaf_spot',
                   'Corn_(maize)___Common_rust_',
                   'Corn_(maize)___Northern_Leaf_Blight',
                   'Corn_(maize)___healthy',
                   'Grape___Black_rot',
                   'Grape___Esca_(Black_Measles)',
                   'Grape___Leaf_blight_(Isariopsis_Leaf_Spot)',
                   'Grape___healthy',
                   'Orange___Haunglongbing_(Citrus_greening)',
                   'Peach___Bacterial_spot',
                   'Peach___healthy',
                   'Pepper,_bell___Bacterial_spot',
                   'Pepper,_bell___healthy',
                   'Potato___Early_blight',
                   'Potato___Late_blight',
                   'Potato___healthy',
                   'Raspberry___healthy',
                   'Soybean___healthy',
                   'Squash___Powdery_mildew',
                   'Strawberry___Leaf_scorch',
                   'Strawberry___healthy',
                   'Tomato___Bacterial_spot',
                   'Tomato___Early_blight',
                   'Tomato___Late_blight',
                   'Tomato___Leaf_Mold',
                   'Tomato___Septoria_leaf_spot',
                   'Tomato___Spider_mites Two-spotted_spider_mite',
                   'Tomato___Target_Spot',
                   'Tomato___Tomato_Yellow_Leaf_Curl_Virus',
                   'Tomato___Tomato_mosaic_virus',
                   'Tomato___healthy']

try:
    if TORCH_AVAILABLE and MODEL_CLASS_AVAILABLE:
        disease_model_path = os.path.join(SCRIPT_DIR, 'models', 'plant_disease_model.pth')
        disease_model = ResNet9(3, len(disease_classes))
        disease_model.load_state_dict(torch.load(
            disease_model_path, map_location=torch.device('cpu')))
        disease_model.eval()
        print("✅ Disease model loaded successfully")
    else:
        raise ImportError("PyTorch or ResNet9 model class not available")
except Exception as e:
    print(f"Warning: Could not load disease model: {e}, using fallback")
    disease_model = None


def predict_image(img, model=disease_model):
    """
    Transforms image to tensor and predicts disease label
    :params: image
    :return: prediction (string)
    """
    if model is not None and TORCH_AVAILABLE:
        try:
            transform = transforms.Compose([
                transforms.Resize(256),
                transforms.ToTensor(),
            ])
            image = Image.open(io.BytesIO(img))
            img_t = transform(image)
            img_u = torch.unsqueeze(img_t, 0)

            # Get predictions from model
            yb = model(img_u)
            # Pick index with highest probability
            _, preds = torch.max(yb, dim=1)
            prediction = disease_classes[preds[0].item()]
            # Retrieve the class label
            return prediction
        except Exception as e:
            print(f"Error in disease prediction: {e}")
            # Fallback prediction
            return random.choice(disease_classes)
    else:
        # Fallback when model is not available
        return random.choice(disease_classes)

def get_city_alternatives(city_name):
    """Get alternative names for cities"""
    city_alternatives = {
        'bangalore': ['bengaluru', 'bangalore', 'bengalore'],
        'bengaluru': ['bengaluru', 'bangalore', 'bengalore'],
        'mumbai': ['mumbai', 'bombay'],
        'bombay': ['mumbai', 'bombay'],
        'kolkata': ['kolkata', 'calcutta'],
        'calcutta': ['kolkata', 'calcutta'],
        'chennai': ['chennai', 'madras'],
        'madras': ['chennai', 'madras'],
        'thiruvananthapuram': ['thiruvananthapuram', 'trivandrum'],
        'trivandrum': ['thiruvananthapuram', 'trivandrum'],
        'kochi': ['kochi', 'cochin'],
        'cochin': ['kochi', 'cochin'],
        'pune': ['pune', 'poona'],
        'poona': ['pune', 'poona'],
        'gurgaon': ['gurgaon', 'gurugram'],
        'gurugram': ['gurgaon', 'gurugram'],
        'new delhi': ['new delhi', 'delhi', 'new delhi, in'],
        'delhi': ['delhi', 'new delhi', 'delhi, in']
    }

    city_lower = city_name.lower().strip()
    return city_alternatives.get(city_lower, [city_name])

def weather_fetch(city_name):
    """
    Fetch comprehensive weather data for agricultural use with multiple fallbacks
    :params: city_name
    :return: weather_data dictionary with all parameters
    """
    api_key = config.weather_api_key
    base_url = "http://api.openweathermap.org/data/2.5/weather?"

    # Get alternative city names to try
    city_alternatives = get_city_alternatives(city_name)

    for city_variant in city_alternatives:
        try:
            # Try with country code for Indian cities
            city_queries = [
                city_variant,
                f"{city_variant},IN",
                f"{city_variant},India"
            ]

            for query in city_queries:
                complete_url = base_url + "appid=" + api_key + "&q=" + query
                print(f"Trying weather API with query: {query}")

                response = requests.get(complete_url, timeout=15)
                x = response.json()

                print(f"API Response code: {x.get('cod', 'unknown')}")

                if x.get("cod") == 200:
                    # Main weather data
                    main_data = x["main"]
                    weather_data = x["weather"][0]
                    wind_data = x.get("wind", {})

                    # Calculate comprehensive weather parameters
                    current_temperature = round((main_data["temp"] - 273.15), 1)
                    feels_like = round((main_data["feels_like"] - 273.15), 1)
                    current_humidity = main_data["humidity"]
                    pressure = main_data["pressure"]

                    # Wind data
                    wind_speed = round(wind_data.get("speed", 0) * 3.6, 1)  # Convert m/s to km/h
                    wind_direction = wind_data.get("deg", 0)

                    # Visibility and other data
                    visibility = round(x.get("visibility", 10000) / 1000, 1)  # Convert to km
                    cloudiness = x.get("clouds", {}).get("all", 0)

                    # Weather description
                    weather_description = weather_data["description"].title()
                    weather_main = weather_data["main"]

                    return {
                        'temperature': f"{current_temperature}°C",
                        'feels_like': f"{feels_like}°C",
                        'humidity': current_humidity,
                        'pressure': pressure,
                        'wind_speed': f"{wind_speed} km/h",
                        'wind_direction': wind_direction,
                        'visibility': visibility,
                        'cloudiness': cloudiness,
                        'description': weather_description,
                        'main': weather_main,
                        'city': x["name"],
                        'country': x["sys"]["country"]
                    }
                elif x.get("cod") == "404":
                    print(f"City not found with query: {query}")
                    continue
                else:
                    print(f"API error: {x.get('message', 'Unknown error')}")
                    continue

        except requests.exceptions.RequestException as e:
            print(f"Weather API request failed for {city_variant}: {e}")
            continue
        except Exception as e:
            print(f"Weather data processing failed for {city_variant}: {e}")
            continue

    print(f"All weather API attempts failed for city: {city_name}")
    return None




# ===============================================================================================
# ------------------------------------ FLASK APP -------------------------------------------------


app = Flask(__name__)
app.secret_key = 'your_secret_key_here'

# Initialize SocketIO for real-time features
socketio = SocketIO(app, cors_allowed_origins="*")

# render home page

@app.route('/')
def index():
    return redirect(url_for('userlog'))

@app.route('/userlog', methods=['GET', 'POST'])
def userlog():
    if request.method == 'POST':
        try:
            # Get form data with validation
            name = request.form.get('name', '').strip()
            password = request.form.get('password', '').strip()
            
            # Basic validation
            if not name or not password:
                return render_template('login.html', 
                                     msg='Please fill in all fields', 
                                     error_type='warning')
            
            # Database connection with proper error handling
            connection = sqlite3.connect('user_data.db')
            cursor = connection.cursor()

            # Use parameterized query to prevent SQL injection
            query = "SELECT name, email, mobile FROM user WHERE name = ? AND password = ?"
            cursor.execute(query, (name, password))
            
            result = cursor.fetchone()
            connection.close()

            if result is None:
                return render_template('login.html', 
                                     msg='Invalid username or password. Please try again.', 
                                     error_type='danger')
            else:
                # Store user info in session
                session['username'] = result[0]
                session['email'] = result[1]
                session['mobile'] = result[2]
                session['logged_in'] = True
                
                # Redirect to dashboard after successful login
                return redirect(url_for('home'))
                
        except sqlite3.Error as e:
            return render_template('login.html', 
                                 msg='Database error occurred. Please try again later.', 
                                 error_type='danger')
        except Exception as e:
            return render_template('login.html', 
                                 msg='An unexpected error occurred. Please try again.', 
                                 error_type='danger')

    # Handle GET request - check for registration success message
    msg = request.args.get('msg')
    error_type = request.args.get('error_type', 'info')
    
    return render_template('login.html', msg=msg, error_type=error_type)




@app.route('/userreg', methods=['GET', 'POST'])
def userreg():
    if request.method == 'POST':
        try:
            # Get and validate form data
            name = request.form.get('name', '').strip()
            password = request.form.get('password', '').strip()
            mobile = request.form.get('phone', '').strip()
            email = request.form.get('email', '').strip()
            
            # Validation
            if not all([name, password, mobile, email]):
                return render_template('register.html', 
                                     msg='Please fill in all fields', 
                                     error_type='warning')
            
            if len(password) < 6:
                return render_template('register.html', 
                                     msg='Password must be at least 6 characters long', 
                                     error_type='warning')
            
            # Email validation (basic)
            if '@' not in email or '.' not in email:
                return render_template('register.html', 
                                     msg='Please enter a valid email address', 
                                     error_type='warning')
            
            # Phone validation (basic)
            if len(mobile) < 10:
                return render_template('register.html', 
                                     msg='Please enter a valid 10-digit mobile number', 
                                     error_type='warning')

            connection = sqlite3.connect('user_data.db')
            cursor = connection.cursor()

            # Check if user already exists (using existing table structure)
            cursor.execute("SELECT name FROM user WHERE name = ? OR email = ?", (name, email))
            existing_user = cursor.fetchone()
            
            if existing_user:
                connection.close()
                return render_template('register.html', 
                                     msg='Username or email already exists. Please choose different ones.', 
                                     error_type='warning')

            # Insert new user using parameterized query
            cursor.execute("INSERT INTO user (name, password, mobile, email) VALUES (?, ?, ?, ?)", 
                          (name, password, mobile, email))
            connection.commit()
            connection.close()

            # Redirect to login page with success message
            return redirect(url_for('userlog', msg='Successfully registered! Please sign in with your credentials.', error_type='success'))
                                 
        except sqlite3.IntegrityError:
            return render_template('register.html', 
                                 msg='Username or email already exists. Please choose different ones.', 
                                 error_type='warning')
        except sqlite3.Error as e:
            return render_template('register.html', 
                                 msg='Database error occurred. Please try again later.', 
                                 error_type='danger')
        except Exception as e:
            return render_template('register.html', 
                                 msg='An unexpected error occurred. Please try again.', 
                                 error_type='danger')
    
    return render_template('register.html')







@ app.route('/index.html')
def home():
    title = 'Crop harvest'
    return render_template('index.html', title=title)

# render crop recommendation form page

@ app.route('/stats', methods=['GET', 'POST'])
def stats():
    if request.method == 'POST':
        File = request.form['season']
        from check import check_stat
        check_stat(File)

        return render_template('price.html')

    return render_template('price.html')

def get_fallback_weather_data(city):
    """Generate realistic fallback weather data when APIs fail"""
    import random
    from datetime import datetime

    # Realistic weather data based on Indian cities
    fallback_data = {
        'bangalore': {'temp': 24, 'humidity': 65, 'desc': 'Pleasant Weather'},
        'bengaluru': {'temp': 24, 'humidity': 65, 'desc': 'Pleasant Weather'},
        'mumbai': {'temp': 28, 'humidity': 75, 'desc': 'Humid'},
        'delhi': {'temp': 26, 'humidity': 60, 'desc': 'Clear Sky'},
        'chennai': {'temp': 30, 'humidity': 80, 'desc': 'Hot and Humid'},
        'kolkata': {'temp': 27, 'humidity': 70, 'desc': 'Partly Cloudy'},
        'hyderabad': {'temp': 25, 'humidity': 55, 'desc': 'Pleasant'},
        'pune': {'temp': 23, 'humidity': 60, 'desc': 'Cool and Pleasant'},
        'ahmedabad': {'temp': 29, 'humidity': 50, 'desc': 'Warm'},
        'jaipur': {'temp': 27, 'humidity': 45, 'desc': 'Dry and Warm'}
    }

    city_lower = city.lower()
    if city_lower in fallback_data:
        data = fallback_data[city_lower]
        temp_variation = random.randint(-3, 3)
        humidity_variation = random.randint(-10, 10)

        return {
            'temperature': f"{data['temp'] + temp_variation}°C",
            'humidity': max(30, min(90, data['humidity'] + humidity_variation)),
            'wind_speed': f"{random.randint(5, 15)} km/h",
            'visibility': f"{random.randint(8, 12)} km",
            'pressure': random.randint(1010, 1020),
            'description': data['desc'],
            'city': city.title(),
            'time': datetime.now().strftime("%I:%M %p")
        }
    else:
        # Generic fallback for unknown cities
        return {
            'temperature': f"{random.randint(20, 32)}°C",
            'humidity': random.randint(40, 80),
            'wind_speed': f"{random.randint(5, 20)} km/h",
            'visibility': f"{random.randint(8, 15)} km",
            'pressure': random.randint(1005, 1025),
            'description': random.choice(['Clear Sky', 'Partly Cloudy', 'Pleasant Weather', 'Sunny']),
            'city': city.title(),
            'time': datetime.now().strftime("%I:%M %p")
        }

@ app.route('/weather', methods=['GET', 'POST'])
def weather():
    if request.method == 'POST':
        try:
            # Handle both dropdown and manual city input
            city = None

            # Check for city from dropdown or manual input
            if 'city' in request.form and request.form['city']:
                city = request.form['city']
            elif 'city_select' in request.form and request.form['city_select']:
                city = request.form['city_select']
            elif 'city_manual' in request.form and request.form['city_manual']:
                city = request.form['city_manual']

            if not city:
                return render_template('weather.html', msg="Please select or enter a city name.")

            # Clean city name (remove extra info for international cities)
            if ',' in city:
                city = city.split(',')[0].strip()

            print(f"Fetching weather for city: {city}")

            # Try OpenWeatherMap API first
            weather_data = weather_fetch(city)

            if weather_data:
                print(f"Successfully fetched API data for {city}")
                return render_template('weather.html',
                                     city=weather_data['city'],
                                     temp=weather_data['temperature'],
                                     sky=weather_data['description'],
                                     humidity=weather_data['humidity'],
                                     wind_speed=weather_data['wind_speed'],
                                     visibility=weather_data['visibility'],
                                     pressure=weather_data['pressure'],
                                     time="Real-time data")
            else:
                print(f"API failed, trying Google scraping for {city}")
                # Fallback to Google scraping
                try:
                    url = "https://www.google.com/search?q="+"weather "+city
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                    html = requests.get(url, headers=headers, timeout=10).content

                    # getting raw data
                    soup = BeautifulSoup(html, 'html.parser')
                    temp1 = soup.find('div', attrs={'class': 'BNeawe iBp4i AP7Wnd'})
                    str_data = soup.find('div', attrs={'class': 'BNeawe tAd8D AP7Wnd'})

                    if temp1 and str_data:
                        temp1 = temp1.text
                        str_data = str_data.text

                        # formatting data
                        data = str_data.split('\n')
                        Time = data[0] if len(data) > 0 else "Now"
                        sky = data[1] if len(data) > 1 else "Clear"

                        print(f"Successfully scraped Google data for {city}")
                        return render_template('weather.html',
                                             city=city,
                                             temp=temp1,
                                             time=Time,
                                             sky=sky,
                                             humidity="N/A",
                                             wind_speed="N/A",
                                             visibility="N/A")
                    else:
                        raise Exception("Could not parse Google weather data")

                except Exception as scraping_error:
                    print(f"Google scraping failed: {scraping_error}")
                    # Final fallback - use realistic dummy data
                    fallback_data = get_fallback_weather_data(city)
                    print(f"Using fallback data for {city}")

                    return render_template('weather.html',
                                         city=fallback_data['city'],
                                         temp=fallback_data['temperature'],
                                         sky=fallback_data['description'],
                                         humidity=fallback_data['humidity'],
                                         wind_speed=fallback_data['wind_speed'],
                                         visibility=fallback_data['visibility'],
                                         pressure=fallback_data['pressure'],
                                         time=fallback_data['time'],
                                         fallback_notice=True)

        except Exception as e:
            print(f"Weather error: {e}")
            # Even if everything fails, provide fallback data
            fallback_data = get_fallback_weather_data(city if 'city' in locals() else 'Delhi')
            return render_template('weather.html',
                                 city=fallback_data['city'],
                                 temp=fallback_data['temperature'],
                                 sky=fallback_data['description'],
                                 humidity=fallback_data['humidity'],
                                 wind_speed=fallback_data['wind_speed'],
                                 visibility=fallback_data['visibility'],
                                 pressure=fallback_data['pressure'],
                                 time=fallback_data['time'],
                                 fallback_notice=True)

    return render_template('weather.html')
    
    
@app.route('/disease_prediction', methods=['GET', 'POST'])
def disease_prediction():
    title = 'Harvestify - Disease Detection'

    if request.method == 'POST':
        if 'file' not in request.files:
            return redirect(request.url)
        file = request.files.get('file')
        if not file:
            return render_template('disease.html', title=title)
        try:
            img = file.read()
            prediction = predict_image(img)
            prediction = Markup(str(disease_dic[prediction]))
            return render_template('disease-result.html', prediction=prediction, title=title)
        except Exception as e:
            print(f"Error during prediction: {e}")
            return render_template('disease.html', title=title, error="An error occurred during prediction.")
    
    return render_template('disease.html', title=title)

@app.route('/disease_prediction_real', methods=['GET', 'POST'])
def disease_prediction_real():
    title = 'Harvestify - Disease Detection'

    if request.method == 'POST':
        if 'file' not in request.files:
            return redirect(request.url)
        file = request.files.get('file')
        if not file:
            return render_template('disease.html', title=title)
        try:
            img = file.read()
            prediction = predict_image(img)
            prediction = Markup(str(disease_dic[prediction]))
            return render_template('disease-result.html', prediction=prediction, title=title)
        except:
            pass
    return render_template('disease.html', title=title)


@ app.route('/crop-recommend')
def crop_recommend():
    # import requests
    # import pandas as pd
    # data=requests.get("https://api.thingspeak.com/channels/2476593/feeds.json?api_key=4JYCPM1VM9XSCPK5&results=2")
    # hum=data.json()['feeds'][-1]['field1']
    # moi=data.json()['feeds'][-1]['field2']
    # temp=data.json()['feeds'][-1]['field3']
    # n=data.json()['feeds'][-1]['field4']
    # p=data.json()['feeds'][-1]['field5']
    # k=data.json()['feeds'][-1]['field6']
    title = 'Crop Recommendation'
    # , n=n, p=p, k=k, temp=temp)
    return render_template('crop-form-proper.html', title=title) #,n=n,k=k,p=p,temp=temp,hum=hum,moi=moi)

# render fertilizer recommendation form page


@ app.route('/yeild')
def yeild():
    # import requests
    # import pandas as pd
    # data=requests.get("https://api.thingspeak.com/channels/2476593/feeds.json?api_key=4JYCPM1VM9XSCPK5&results=2")
    # hum=data.json()['feeds'][-1]['field1']
    # moi=data.json()['feeds'][-1]['field2']
    # temp=data.json()['feeds'][-1]['field3']
    # n=data.json()['feeds'][-1]['field4']
    # p=data.json()['feeds'][-1]['field5']
    # k=data.json()['feeds'][-1]['field6']
    title = 'crop yeild prediction'

    # , temp=temp, hum=hum)
    return render_template('crop_yeild.html', title=title ) #,n=n,k=k,p=p,temp=temp,hum=hum,moi=moi)

# render disease prediction input page


# ===============================================================================================

# RENDER PREDICTION PAGES

# render crop recommendation result page


@ app.route('/crop_predict', methods=['POST'])
def crop_predict():
    title = 'Crop Recommended'

    if request.method == 'POST':
        try:
            # Get and validate input values
            N = float(request.form['nitrogen'])
            P = float(request.form['phosphorous'])
            K = float(request.form['pottasium'])
            ph = float(request.form['ph'])
            rainfall = float(request.form['rainfall'])
            hum = float(request.form['hum'])
            temp = float(request.form['temp'])

            print(f"🌾 Crop prediction request: N={N}, P={P}, K={K}, temp={temp}, hum={hum}, ph={ph}, rainfall={rainfall}")

            # Input validation
            if not (0 <= N <= 140):
                raise ValueError("Nitrogen should be between 0-140 kg/ha")
            if not (5 <= P <= 145):
                raise ValueError("Phosphorous should be between 5-145 kg/ha")
            if not (5 <= K <= 205):
                raise ValueError("Potassium should be between 5-205 kg/ha")
            if not (4.0 <= ph <= 10.0):
                raise ValueError("pH should be between 4.0-10.0")
            if not (20 <= rainfall <= 300):
                raise ValueError("Rainfall should be between 20-300 mm")
            if not (14 <= hum <= 100):
                raise ValueError("Humidity should be between 14-100%")
            if not (8 <= temp <= 44):
                raise ValueError("Temperature should be between 8-44°C")

            if cr is not None:
                try:
                    # Prepare data in correct order: N, P, K, temperature, humidity, ph, rainfall
                    data = np.array([[N, P, K, temp, hum, ph, rainfall]])
                    
                    # Get prediction with warnings suppressed
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        my_prediction = cr.predict(data)
                        prediction_proba = cr.predict_proba(data)
                    
                    final_prediction = my_prediction[0]
                    confidence = max(prediction_proba[0]) * 100
                    
                    # Get alternative suggestions if confidence is low
                    alternatives = []
                    if confidence < 50:
                        # Get top 3 predictions with their probabilities
                        class_names = cr.classes_
                        proba_with_names = list(zip(class_names, prediction_proba[0]))
                        # Sort by probability descending
                        sorted_crops = sorted(proba_with_names, key=lambda x: x[1], reverse=True)
                        # Get top 3 alternatives (excluding the main prediction)
                        alternatives = [(crop, round(prob*100, 1)) for crop, prob in sorted_crops[1:4] if prob > 0.1]
                    
                    print(f"✅ Model prediction: {final_prediction} (confidence: {confidence:.1f}%)")
                    if alternatives:
                        alt_str = ", ".join([f"{crop} ({conf}%)" for crop, conf in alternatives])
                        print(f"🔄 Alternatives: {alt_str}")
                    
                    # Get crop information including image
                    crop_info = get_crop_info(final_prediction)
                    
                    # Model prediction successful - return result immediately
                    return render_template('crop-result-realistic.html', 
                                         prediction=final_prediction, 
                                         title=title,
                                         confidence=round(confidence, 1),
                                         alternatives=alternatives,
                                         nitrogen=N, phosphorous=P, potassium=K,
                                         temperature=temp, humidity=hum, 
                                         ph_level=ph, rainfall=rainfall,
                                         crop_info=crop_info)
                except Exception as model_error:
                    print(f"⚠️ Model prediction failed: {model_error}")
                    # Simple fallback
                    fallback_crops = ['rice', 'wheat', 'cotton', 'maize', 'sugarcane']
                    final_prediction = random.choice(fallback_crops)
                    confidence = 65.0
                    print(f"🔄 Using simple fallback prediction: {final_prediction} (confidence: {confidence:.1f}%)")
                    
                    # Get crop information including image
                    crop_info = get_crop_info(final_prediction)
                    
                    return render_template('crop-result-realistic.html', 
                                         prediction=final_prediction, 
                                         title=title,
                                         confidence=round(confidence, 1),
                                         alternatives=[],
                                         nitrogen=N, phosphorous=P, potassium=K,
                                         temperature=temp, humidity=hum, 
                                         ph_level=ph, rainfall=rainfall,
                                         crop_info=crop_info)
            else:
                # Simple fallback recommendation when model not available
                fallback_crops = ['rice', 'wheat', 'cotton', 'maize', 'sugarcane']
                final_prediction = random.choice(fallback_crops)
                confidence = 60.0
                print(f"🔄 Model not available, using simple fallback: {final_prediction} (confidence: {confidence:.1f}%)")

            # Get crop information including image
            crop_info = get_crop_info(final_prediction)

            return render_template('crop-result-realistic.html', 
                                 prediction=final_prediction, 
                                 title=title,
                                 confidence=round(confidence, 1),
                                 alternatives=[],
                                 nitrogen=N, phosphorous=P, potassium=K,
                                 temperature=temp, humidity=hum, 
                                 ph_level=ph, rainfall=rainfall,
                                 crop_info=crop_info)
        
        except ValueError as e:
            error_msg = str(e)
            print(f"❌ Validation error: {error_msg}")
            return render_template('crop-form-proper.html', title='Crop Recommendation', error=error_msg)
        except Exception as e:
            error_msg = f"An error occurred during prediction: {str(e)}"
            print(f"❌ Prediction error: {error_msg}")
            return render_template('crop-form-proper.html', title='Crop Recommendation', error=error_msg)
    
    return render_template('crop-form-proper.html', title='Crop Recommendation')
    
    return render_template('crop.html', title='Crop Recommendation')
# render fertilizer recommendation result page

@app.route('/fer_predict',methods=['POST'])
def fer_predict():
    temp = request.form.get('temp')
    humi = request.form.get('humid')
    mois = request.form.get('mois')
    soil = request.form.get('crop')
    crop = request.form.get('crop')
    nitro = request.form.get('pota')
    pota = request.form.get('pota')
    phosp = request.form.get('phos')
    input = [float(temp),float(humi),float(mois),float(soil),float(crop),float(nitro),float(pota),float(phosp)]

    if model is not None and ferti is not None:
        res = ferti.classes_[model.predict([input])]
        result = res[0]
    else:
        # Fallback fertilizer recommendation
        fertilizers = ['Urea', 'DAP', 'NPK', 'Potash', 'Organic Compost']
        result = random.choice(fertilizers)

    return render_template('fer_predict.html',res = result)
@ app.route('/yeild-predict', methods=['POST'])
def yeild_predict():
    title = 'yeild predicted'

    if request.method == 'POST':
        state = request.form['stt']
        district = request.form['city']
        year = 2024 #request.form['year']
        season = request.form['season']
        crop = request.form['crop']
        Temperature = request.form['Temperature']
        humidity = request.form['humidity']
        soilmoisture = request.form['soilmoisture']
        area = request.form['area']

        if forest is not None:
            out_1 = forest.predict([[float(state),
                                     float(district),
                                     float(year),
                                     float(season),
                                     float(crop),
                                     float(Temperature),
                                     float(humidity),
                                     float(soilmoisture),
                                     float(area)]])
            yield_value = out_1[0]
        else:
            # Fallback price prediction
            yield_value = float(area) * random.uniform(2.0, 5.0)  # Simple fallback

        if yield_value > 1000:
            ans=f" {yield_value:.2f} Kg"
        elif yield_value > 100:
            ans=f" {yield_value:.2f} Quintal"
        else:
            ans=f" {yield_value:.2f} Tons"
        


        return render_template('yeild_prediction.html', prediction=ans, title=title)

    return render_template('try_again.html', title=title)


# render disease prediction result page

@ app.route('/price_predict', methods=['POST'])
def price_predict():
    title = 'price Suggestion'
    if request.method == 'POST':
        state = int(request.form['stt'])
        district = int(request.form['city'])
        year = int(request.form['year'])
        season = int(request.form['season'])
        crop = int(request.form['crop'])

        if cp is not None:
            p_result = cp.predict([[float(state),
                                    float(district),
                                    float(year),
                                    float(season),
                                    float(crop)]])
        else:
            # Fallback price prediction
            p_result = [random.uniform(1000, 5000)]  # Random price between 1000-5000

        return render_template('price_prediction.html', title=title, p_result=p_result)
    return render_template('try_again.html',title=title)
                           

@app.route('/crop_price', methods=['GET', 'POST'])
def crop_price():
    # return "this is crop prediction page"
    title = 'crop price'
    return render_template('crop_price.html', title=title)

@app.route('/crop_fer', methods=['GET', 'POST'])
def crop_fer():
    # import requests
    # import pandas as pd
    # data=requests.get("https://api.thingspeak.com/channels/2476593/feeds.json?api_key=4JYCPM1VM9XSCPK5&results=2")
    # hum=data.json()['feeds'][-1]['field1']
    # moi=data.json()['feeds'][-1]['field2']
    # temp=data.json()['feeds'][-1]['field3']
    # n=data.json()['feeds'][-1]['field4']
    # p=data.json()['feeds'][-1]['field5']
    # k=data.json()['feeds'][-1]['field6']
    # return "this is crop prediction page"
    title = 'crop Fertilizer'
    return render_template('fer.html', title=title) #,n=n,k=k,p=p,temp=temp,hum=hum,moi=moi)


# @ app.route('/price_predict', methods=['POST'])
# def price_predict():
#     title = 'price Suggestion'
#     if request.method == 'POST':
#         state = int(request.form['stt'])
#         district = int(request.form['city'])
#         year = int(request.form['year'])
#         season = int(request.form['season'])
#         crop = int(request.form['crop'])

#         p_result = cp.predict([[float(state),
#                                 float(district),
#                                 float(year),
#                                 float(season),
#                                 float(crop)]])

#         return render_template('price_prediction.html', title=title, p_result=p_result)
#     return render_template('try_again.html', title=title)


# ===============================================================================================

# ===============================================================================================
# ENHANCED FEATURES ROUTES
# ===============================================================================================

# Enhanced Irrigation Management with ML
try:
    from enhanced_irrigation_api import enhanced_irrigation_api
    IRRIGATION_ML_AVAILABLE = True
except ImportError:
    IRRIGATION_ML_AVAILABLE = False
    print("⚠️ Enhanced irrigation API not available, using basic recommendations")

@app.route('/irrigation', methods=['GET', 'POST'])
@app.route('/irrigation_management', methods=['GET', 'POST'])
def irrigation_management():
    if request.method == 'POST':
        crop_type = request.form.get('crop_type')
        soil_type = request.form.get('soil_type')
        location = request.form.get('location')
        planting_date = request.form.get('planting_date')
        field_size = float(request.form.get('field_size', 1.0))

        # Get enhanced irrigation recommendation
        if IRRIGATION_ML_AVAILABLE:
            try:
                recommendation = enhanced_irrigation_api.get_comprehensive_irrigation_recommendation(
                    crop_type=crop_type,
                    soil_type=soil_type,
                    location=location,
                    planting_date=planting_date,
                    field_size=field_size,
                    user_id=session.get('user_id')
                )
                recommendation['ml_powered'] = True
            except Exception as e:
                print(f"❌ ML recommendation failed: {e}")
                recommendation = get_basic_irrigation_recommendation(crop_type, soil_type, location)
                recommendation['ml_powered'] = False
        else:
            recommendation = get_basic_irrigation_recommendation(crop_type, soil_type, location)
            recommendation['ml_powered'] = False

        return render_template('irrigation_result.html',
                             recommendation=recommendation,
                             crop_type=crop_type,
                             soil_type=soil_type,
                             location=location,
                             planting_date=planting_date,
                             field_size=field_size)

    return render_template('irrigation.html')

@app.route('/irrigation_predict', methods=['POST'])
def irrigation_predict():
    """API endpoint for irrigation prediction"""
    try:
        data = request.get_json() if request.is_json else request.form

        crop_type = data.get('crop_type')
        soil_type = data.get('soil_type')
        location = data.get('location')
        planting_date = data.get('planting_date')
        field_size = float(data.get('field_size', 1.0))

        if IRRIGATION_ML_AVAILABLE:
            recommendation = enhanced_irrigation_api.get_comprehensive_irrigation_recommendation(
                crop_type=crop_type,
                soil_type=soil_type,
                location=location,
                planting_date=planting_date,
                field_size=field_size,
                user_id=session.get('user_id')
            )
        else:
            recommendation = get_basic_irrigation_recommendation(crop_type, soil_type, location)

        if request.is_json:
            return jsonify(recommendation)
        else:
            return render_template('irrigation_result.html',
                                 recommendation=recommendation,
                                 crop_type=crop_type,
                                 soil_type=soil_type,
                                 location=location)

    except Exception as e:
        error_msg = f"Error generating irrigation recommendation: {str(e)}"
        if request.is_json:
            return jsonify({'error': error_msg}), 500
        else:
            return render_template('irrigation.html', error=error_msg)

@app.route('/irrigation_dashboard')
def irrigation_dashboard():
    """Irrigation management dashboard"""
    try:
        # Get dashboard data
        dashboard_data = get_irrigation_dashboard_data()

        # Get weather data
        weather_data = get_current_weather_data()

        # Generate trend data for charts
        trend_data = generate_trend_chart_data()

        return render_template('irrigation_dashboard.html',
                             dashboard_data=dashboard_data,
                             weather_data=weather_data,
                             trend_labels=trend_data['labels'],
                             trend_data_a=trend_data['data_a'],
                             trend_data_b=trend_data['data_b'])

    except Exception as e:
        print(f"❌ Dashboard error: {e}")
        return render_template('irrigation_dashboard.html',
                             dashboard_data={'summary': {}, 'current_readings': [], 'active_alerts': [], 'recommendations': {}},
                             weather_data={},
                             trend_labels=[],
                             trend_data_a=[],
                             trend_data_b=[])

@app.route('/api/dashboard_data')
def api_dashboard_data():
    """API endpoint for dashboard data"""
    try:
        dashboard_data = get_irrigation_dashboard_data()
        return jsonify(dashboard_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/resolve_alert', methods=['POST'])
def api_resolve_alert():
    """API endpoint to resolve alerts"""
    try:
        data = request.get_json()
        alert_id = data.get('alert_id')

        if IRRIGATION_ML_AVAILABLE:
            from soil_moisture_monitor import soil_monitor
            soil_monitor.resolve_alert(alert_id)

        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

def get_irrigation_dashboard_data():
    """Get data for irrigation dashboard"""
    try:
        if IRRIGATION_ML_AVAILABLE:
            from soil_moisture_monitor import soil_monitor
            return soil_monitor.get_dashboard_data()
        else:
            # Return sample data
            return {
                'summary': {'total_sensors': 0, 'readings_today': 0, 'active_alerts_count': 0},
                'current_readings': [],
                'active_alerts': [],
                'recommendations': {},
                'avg_moisture': 0
            }
    except Exception as e:
        print(f"❌ Error getting dashboard data: {e}")
        return {
            'summary': {'total_sensors': 0, 'readings_today': 0, 'active_alerts_count': 0},
            'current_readings': [],
            'active_alerts': [],
            'recommendations': {},
            'avg_moisture': 0
        }

def get_current_weather_data():
    """Get current weather data for dashboard"""
    try:
        if IRRIGATION_ML_AVAILABLE:
            from weather_irrigation_integration import weather_integrator
            weather = weather_integrator.get_fallback_weather_data("Delhi")
            return weather
        else:
            return {
                'temperature': 25.0,
                'humidity': 65.0,
                'wind_speed': 10.0,
                'rainfall': 0.0
            }
    except Exception as e:
        print(f"❌ Error getting weather data: {e}")
        return {
            'temperature': 25.0,
            'humidity': 65.0,
            'wind_speed': 10.0,
            'rainfall': 0.0
        }

def generate_trend_chart_data():
    """Generate sample trend data for charts"""
    import random
    from datetime import datetime, timedelta

    # Generate 7 days of sample data
    labels = []
    data_a = []
    data_b = []

    for i in range(7):
        date = datetime.now() - timedelta(days=6-i)
        labels.append(date.strftime('%m/%d'))

        # Generate sample moisture data with some variation
        data_a.append(random.uniform(30, 50))
        data_b.append(random.uniform(25, 45))

    return {
        'labels': labels,
        'data_a': data_a,
        'data_b': data_b
    }

def get_basic_irrigation_recommendation(crop_type, soil_type, location):
    """Basic irrigation recommendation (fallback when ML is not available)"""
    recommendations = {
        'irrigation_needed': True,
        'irrigation_probability': 0.7,
        'irrigation_amount_mm': 25,
        'schedule': 'Water every 2-3 days',
        'amount_per_session': 25,
        'daily_water_mm': 8.3,
        'best_time': 'Early morning (6-8 AM) or evening (6-8 PM)',
        'method': 'Drip irrigation recommended for water efficiency',
        'duration_minutes': 375,
        'confidence': 'medium',
        'explanations': ['Based on general crop and soil requirements'],
        'warnings': [],
        'tips': ['Monitor soil moisture regularly', 'Adjust based on weather conditions'],
        'ml_powered': False
    }

    # Customize based on crop type
    if crop_type == 'rice':
        recommendations.update({
            'irrigation_amount_mm': 50,
            'amount_per_session': 50,
            'daily_water_mm': 50,
            'schedule': 'Keep field flooded during growing season',
            'method': 'Flood irrigation'
        })
    elif crop_type == 'wheat':
        recommendations.update({
            'irrigation_amount_mm': 20,
            'amount_per_session': 20,
            'daily_water_mm': 2.9,
            'schedule': 'Water every 7-10 days'
        })
    elif crop_type == 'cotton':
        recommendations.update({
            'irrigation_amount_mm': 30,
            'amount_per_session': 30,
            'daily_water_mm': 6,
            'schedule': 'Water every 5-7 days'
        })

    return recommendations

# Expense Management System
@app.route('/expenses')
def expenses_dashboard():
    """Expense management dashboard"""
    try:
        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        # Get recent expenses
        cursor.execute('''
            SELECT * FROM expenses
            ORDER BY date DESC
            LIMIT 10
        ''')
        recent_expenses = cursor.fetchall()

        # Get expense summary by category
        cursor.execute('''
            SELECT category, SUM(amount) as total, COUNT(*) as count
            FROM expenses
            WHERE date >= date('now', '-30 days')
            GROUP BY category
            ORDER BY total DESC
        ''')
        category_summary = cursor.fetchall()

        # Get monthly totals
        cursor.execute('''
            SELECT strftime('%Y-%m', date) as month, SUM(amount) as total
            FROM expenses
            WHERE date >= date('now', '-12 months')
            GROUP BY month
            ORDER BY month DESC
        ''')
        monthly_totals = cursor.fetchall()

        # Calculate total expenses
        cursor.execute('SELECT SUM(amount) FROM expenses WHERE date >= date("now", "-30 days")')
        monthly_total = cursor.fetchone()[0] or 0

        cursor.execute('SELECT SUM(amount) FROM expenses')
        total_expenses = cursor.fetchone()[0] or 0

        conn.close()

        return render_template('expenses_dashboard.html',
                             recent_expenses=recent_expenses,
                             category_summary=category_summary,
                             monthly_totals=monthly_totals,
                             monthly_total=monthly_total,
                             total_expenses=total_expenses)

    except Exception as e:
        print(f"Error in expenses dashboard: {e}")
        return render_template('expenses_dashboard.html',
                             recent_expenses=[],
                             category_summary=[],
                             monthly_totals=[],
                             monthly_total=0,
                             total_expenses=0)

@app.route('/add_expense', methods=['GET', 'POST'])
def add_expense():
    """Add new expense"""
    if request.method == 'POST':
        try:
            date = request.form['date']
            category = request.form['category']
            amount = float(request.form['amount'])
            description = request.form.get('description', '')

            conn = sqlite3.connect('agriculture.db')
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO expenses (date, category, amount, description, user_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (date, category, amount, description, session.get('user_id')))

            conn.commit()
            conn.close()

            return redirect(url_for('expenses_dashboard'))

        except Exception as e:
            return render_template('add_expense.html', error=f"Error adding expense: {e}")

    return render_template('add_expense.html')

@app.route('/view_expenses')
def view_expenses():
    """View all expenses with filtering"""
    try:
        # Get filter parameters
        category_filter = request.args.get('category', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        # Build query with filters
        query = 'SELECT * FROM expenses WHERE 1=1'
        params = []

        if category_filter:
            query += ' AND category = ?'
            params.append(category_filter)

        if date_from:
            query += ' AND date >= ?'
            params.append(date_from)

        if date_to:
            query += ' AND date <= ?'
            params.append(date_to)

        query += ' ORDER BY date DESC'

        cursor.execute(query, params)
        expenses = cursor.fetchall()

        # Get all categories for filter dropdown
        cursor.execute('SELECT DISTINCT category FROM expenses ORDER BY category')
        categories = [row[0] for row in cursor.fetchall()]

        conn.close()

        return render_template('view_expenses.html',
                             expenses=expenses,
                             categories=categories,
                             selected_category=category_filter,
                             date_from=date_from,
                             date_to=date_to)

    except Exception as e:
        print(f"Error viewing expenses: {e}")
        return render_template('view_expenses.html',
                             expenses=[],
                             categories=[],
                             selected_category='',
                             date_from='',
                             date_to='')

@app.route('/delete_expense/<int:expense_id>')
def delete_expense(expense_id):
    """Delete an expense"""
    try:
        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        cursor.execute('DELETE FROM expenses WHERE id = ?', (expense_id,))
        conn.commit()
        conn.close()

        return redirect(url_for('view_expenses'))

    except Exception as e:
        print(f"Error deleting expense: {e}")
        return redirect(url_for('view_expenses'))

@app.route('/expense_analytics')
def expense_analytics():
    """Expense analytics and charts"""
    try:
        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        # Monthly expense trends
        cursor.execute('''
            SELECT strftime('%Y-%m', date) as month, SUM(amount) as total
            FROM expenses
            WHERE date >= date('now', '-12 months')
            GROUP BY month
            ORDER BY month
        ''')
        monthly_data = cursor.fetchall()

        # Category breakdown
        cursor.execute('''
            SELECT category, SUM(amount) as total
            FROM expenses
            WHERE date >= date('now', '-30 days')
            GROUP BY category
            ORDER BY total DESC
        ''')
        category_data = cursor.fetchall()

        # Daily expenses for current month
        cursor.execute('''
            SELECT date, SUM(amount) as total
            FROM expenses
            WHERE strftime('%Y-%m', date) = strftime('%Y-%m', 'now')
            GROUP BY date
            ORDER BY date
        ''')
        daily_data = cursor.fetchall()

        conn.close()

        return render_template('expense_analytics.html',
                             monthly_data=monthly_data,
                             category_data=category_data,
                             daily_data=daily_data)

    except Exception as e:
        print(f"Error in expense analytics: {e}")
        return render_template('expense_analytics.html',
                             monthly_data=[],
                             category_data=[],
                             daily_data=[])

# Real-time Expense Management with SocketIO
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print('Client connected')
    join_room('expense_updates')
    emit('status', {'msg': 'Connected to real-time expense updates'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print('Client disconnected')
    leave_room('expense_updates')

@socketio.on('get_live_data')
def handle_get_live_data():
    """Send live expense data to client"""
    try:
        dashboard_data = get_expense_dashboard_data()
        emit('live_data_update', dashboard_data)
    except Exception as e:
        emit('error', {'message': f'Error fetching live data: {str(e)}'})

@socketio.on('add_expense_realtime')
def handle_add_expense_realtime(data):
    """Handle real-time expense addition"""
    try:
        # Validate data
        required_fields = ['date', 'category', 'amount']
        for field in required_fields:
            if field not in data or not data[field]:
                emit('error', {'message': f'Missing required field: {field}'})
                return

        # Add expense to database
        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO expenses (date, category, amount, description, user_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (data['date'], data['category'], float(data['amount']),
              data.get('description', ''), session.get('user_id', 1)))

        expense_id = cursor.lastrowid
        conn.commit()
        conn.close()

        # Get updated dashboard data
        dashboard_data = get_expense_dashboard_data()

        # Broadcast to all connected clients
        socketio.emit('expense_added', {
            'expense_id': expense_id,
            'expense': data,
            'dashboard_data': dashboard_data,
            'message': f'New expense added: ₹{data["amount"]} for {data["category"]}'
        }, room='expense_updates')

        emit('success', {'message': 'Expense added successfully'})

    except Exception as e:
        emit('error', {'message': f'Error adding expense: {str(e)}'})

@socketio.on('delete_expense_realtime')
def handle_delete_expense_realtime(data):
    """Handle real-time expense deletion"""
    try:
        expense_id = data.get('expense_id')
        if not expense_id:
            emit('error', {'message': 'Expense ID is required'})
            return

        # Delete from database
        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        # Get expense details before deletion
        cursor.execute('SELECT category, amount FROM expenses WHERE id = ?', (expense_id,))
        expense_details = cursor.fetchone()

        if not expense_details:
            emit('error', {'message': 'Expense not found'})
            return

        cursor.execute('DELETE FROM expenses WHERE id = ?', (expense_id,))
        conn.commit()
        conn.close()

        # Get updated dashboard data
        dashboard_data = get_expense_dashboard_data()

        # Broadcast to all connected clients
        socketio.emit('expense_deleted', {
            'expense_id': expense_id,
            'dashboard_data': dashboard_data,
            'message': f'Deleted expense: ₹{expense_details[1]} from {expense_details[0]}'
        }, room='expense_updates')

        emit('success', {'message': 'Expense deleted successfully'})

    except Exception as e:
        emit('error', {'message': f'Error deleting expense: {str(e)}'})

def get_expense_dashboard_data():
    """Get real-time dashboard data"""
    try:
        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        # Get recent expenses
        cursor.execute('''
            SELECT id, date, category, amount, description, created_at
            FROM expenses
            ORDER BY created_at DESC
            LIMIT 10
        ''')
        recent_expenses = [dict(zip([col[0] for col in cursor.description], row))
                          for row in cursor.fetchall()]

        # Get category summary
        cursor.execute('''
            SELECT category, SUM(amount) as total, COUNT(*) as count
            FROM expenses
            WHERE date >= date('now', '-30 days')
            GROUP BY category
            ORDER BY total DESC
        ''')
        category_summary = [dict(zip([col[0] for col in cursor.description], row))
                           for row in cursor.fetchall()]

        # Get totals
        cursor.execute('SELECT SUM(amount) FROM expenses WHERE date >= date("now", "-30 days")')
        monthly_total = cursor.fetchone()[0] or 0

        cursor.execute('SELECT SUM(amount) FROM expenses')
        total_expenses = cursor.fetchone()[0] or 0

        cursor.execute('SELECT COUNT(*) FROM expenses')
        total_records = cursor.fetchone()[0] or 0

        conn.close()

        return {
            'recent_expenses': recent_expenses,
            'category_summary': category_summary,
            'monthly_total': monthly_total,
            'total_expenses': total_expenses,
            'total_records': total_records,
            'timestamp': datetime.now().isoformat()
        }

    except Exception as e:
        print(f"Error getting dashboard data: {e}")
        return {
            'recent_expenses': [],
            'category_summary': [],
            'monthly_total': 0,
            'total_expenses': 0,
            'total_records': 0,
            'timestamp': datetime.now().isoformat()
        }

@app.route('/api/chart_data')
def api_chart_data():
    """API endpoint for real-time chart data"""
    try:
        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        # Monthly data for chart
        cursor.execute('''
            SELECT strftime('%Y-%m', date) as month, SUM(amount) as total
            FROM expenses
            WHERE date >= date('now', '-12 months')
            GROUP BY month
            ORDER BY month
        ''')
        monthly_data = cursor.fetchall()

        conn.close()

        return jsonify({
            'monthly_data': monthly_data,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/export_expenses')
def api_export_expenses():
    """API endpoint for exporting expenses as CSV"""
    try:
        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        cursor.execute('''
            SELECT date, category, description, amount
            FROM expenses
            ORDER BY date DESC
        ''')
        expenses = cursor.fetchall()
        conn.close()

        # Create CSV content
        csv_content = "Date,Category,Description,Amount\n"
        for expense in expenses:
            description = (expense[2] or '').replace('"', '""')  # Escape quotes
            csv_content += f'"{expense[0]}","{expense[1]}","{description}","{expense[3]}"\n'

        # Return as downloadable file
        response = Response(
            csv_content,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename=expenses_{datetime.now().strftime("%Y%m%d")}.csv'}
        )
        return response

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/expense_stats')
def api_expense_stats():
    """API endpoint for real-time expense statistics"""
    try:
        conn = sqlite3.connect('agriculture.db')
        cursor = conn.cursor()

        # Today's expenses
        cursor.execute('''
            SELECT COUNT(*), COALESCE(SUM(amount), 0)
            FROM expenses
            WHERE date = date('now')
        ''')
        today_count, today_total = cursor.fetchone()

        # This week's expenses
        cursor.execute('''
            SELECT COUNT(*), COALESCE(SUM(amount), 0)
            FROM expenses
            WHERE date >= date('now', '-7 days')
        ''')
        week_count, week_total = cursor.fetchone()

        # Average daily expense
        cursor.execute('''
            SELECT AVG(daily_total) FROM (
                SELECT SUM(amount) as daily_total
                FROM expenses
                WHERE date >= date('now', '-30 days')
                GROUP BY date
            )
        ''')
        avg_daily = cursor.fetchone()[0] or 0

        conn.close()

        return jsonify({
            'today': {'count': today_count, 'total': today_total},
            'week': {'count': week_count, 'total': week_total},
            'avg_daily': avg_daily,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Pest Management
@app.route('/pest_management', methods=['GET', 'POST'])
def pest_management():
    if request.method == 'POST':
        if 'file' not in request.files:
            return render_template('pest_management.html', error='No file uploaded')

        file = request.files['file']
        if file.filename == '':
            return render_template('pest_management.html', error='No file selected')

        # Process the uploaded image for pest detection
        pest_result = detect_pest_from_image(file)

        return render_template('pest_management.html',
                             pest_result=pest_result,
                             show_result=True)

    return render_template('pest_management.html')

def detect_pest_from_image(file):
    """Detect pest from uploaded image using ML model"""
    try:
        # Initialize the advanced pest detector
        from pest_detector import PestWeedDetector
        detector = PestWeedDetector()
        
        # Get detection result
        result = detector.detect_pest_weed(file)
        
        # Format result for template compatibility
        pest_result = {
            'name': result['display_name'],
            'confidence': result['confidence'],
            'severity': result['severity'].title(),
            'description': result['description'],
            'treatments': result.get('treatments', {}),
            'prevention': result.get('prevention', []),
            'class_name': result['class_name']
        }
        
        # Get primary treatment recommendation
        treatments = result.get('treatments', {})
        if 'organic' in treatments:
            primary_treatment = treatments['organic'][0] if treatments['organic'] else 'General pest management'
        elif 'chemical' in treatments:
            primary_treatment = treatments['chemical'][0] if treatments['chemical'] else 'General pest management'
        else:
            primary_treatment = 'Consult agricultural expert for treatment'
        
        pest_result['treatment'] = primary_treatment
        
        return pest_result
        
    except Exception as e:
        print(f"Error in ML pest detection: {e}")
        # Fallback to simple detection
        return get_fallback_pest_detection()

def get_fallback_pest_detection():
    """Fallback pest detection when ML model fails"""
    import random
    
    pests = [
        {
            'name': 'Aphids',
            'confidence': 0.75,
            'treatment': 'Use neem oil spray or insecticidal soap',
            'severity': 'Medium',
            'description': 'Small, soft-bodied insects that feed on plant sap',
            'class_name': 'pests_aphids'
        },
        {
            'name': 'Whiteflies',
            'confidence': 0.70,
            'treatment': 'Yellow sticky traps and neem oil application',
            'severity': 'High',
            'description': 'Small white flying insects that damage leaves',
            'class_name': 'pests_whiteflies'
        },
        {
            'name': 'Spider Mites',
            'confidence': 0.80,
            'treatment': 'Increase humidity and use miticide spray',
            'severity': 'Medium',
            'description': 'Tiny mites that cause stippling on leaves',
            'class_name': 'pests_spider_mites'
        }
    ]
    
    return random.choice(pests)

# Farm Expenses Management  
@app.route('/expenses', methods=['GET', 'POST'])
@app.route('/farm_expenses', methods=['GET', 'POST'])
def farm_expenses():
    if request.method == 'POST':
        category = request.form.get('category')
        description = request.form.get('description')
        amount = float(request.form.get('amount'))
        expense_date = request.form.get('expense_date')

        # Save to database (simplified)
        connection = sqlite3.connect('user_data.db')
        cursor = connection.cursor()
        cursor.execute(
            """
            INSERT INTO farm_expenses (user_id, category, description, amount, expense_date)
            VALUES (?, ?, ?, ?, ?)
            """,
            (1, category, description, amount, expense_date))  # Using user_id = 1 for demo
        connection.commit()
        connection.close()

        return render_template('expenses.html', success='Expense added successfully!')

    # Get existing expenses
    connection = sqlite3.connect('user_data.db')
    cursor = connection.cursor()
    cursor.execute("SELECT * FROM farm_expenses ORDER BY expense_date DESC LIMIT 10")
    expenses = cursor.fetchall()
    connection.close()

    return render_template('expenses.html', expenses=expenses)

# Market Linkage
@app.route('/market_linkage')
def market_linkage():
    # Sample market data
    markets = [
        {
            'name': 'Delhi Mandi',
            'location': 'Delhi',
            'crops': ['Wheat', 'Rice', 'Potato'],
            'contact': '+91-9876543210',
            'distance': '25 km'
        },
        {
            'name': 'Punjab Agricultural Market',
            'location': 'Punjab',
            'crops': ['Cotton', 'Sugarcane', 'Corn'],
            'contact': '+91-9876543211',
            'distance': '45 km'
        }
    ]

    return render_template('market_linkage.html', markets=markets)

# Farm Calendar
@app.route('/farm_calendar', methods=['GET', 'POST'])
def farm_calendar():
    if request.method == 'POST':
        activity_type = request.form.get('activity_type')
        crop = request.form.get('crop')
        description = request.form.get('description')
        activity_date = request.form.get('activity_date')

        # Save activity to database
        connection = sqlite3.connect('user_data.db')
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO farm_activities (user_id, activity_type, crop, description, activity_date)
            VALUES (?, ?, ?, ?, ?)
        """, (1, activity_type, crop, description, activity_date))
        connection.commit()
        connection.close()

        return render_template('farm_calendar.html', success='Activity scheduled successfully!')

    # Get upcoming activities
    connection = sqlite3.connect('user_data.db')
    cursor = connection.cursor()
    cursor.execute("""
        SELECT * FROM farm_activities
        WHERE activity_date >= date('now')
        ORDER BY activity_date ASC LIMIT 10
    """)
    activities = cursor.fetchall()
    connection.close()

    return render_template('farm_calendar.html', activities=activities)

# Analytics and Yield Prediction
@app.route('/analytics')
def yield_analytics():
    # Sample analytics data
    analytics_data = {
        'total_yield': 1250,
        'total_area': 25,
        'avg_yield_per_hectare': 50,
        'profit_margin': 35.5,
        'crops': ['Wheat', 'Rice', 'Cotton', 'Corn', 'Sugarcane'],
        'current_year': [1250, 980, 750, 650, 450],
        'last_year': [1100, 920, 680, 600, 420],
        'price_trends': [2500, 3200, 4800, 2800, 1800],  # Price per quintal
        'top_crops': ['Wheat', 'Rice', 'Cotton'],
        'monthly_data': [
            {'month': 'Jan', 'yield': 120, 'profit': 15000},
            {'month': 'Feb', 'yield': 135, 'profit': 18000},
            {'month': 'Mar', 'yield': 150, 'profit': 22000},
            {'month': 'Apr', 'yield': 145, 'profit': 20000},
            {'month': 'May', 'yield': 160, 'profit': 25000},
            {'month': 'Jun', 'yield': 155, 'profit': 23000}
        ]
    }

    return render_template('analytics.html', data=analytics_data)

@app.route('/yield')
def yield_page():
    return render_template('crop_yeild.html', title='Yield Prediction')

# Satellite Data and Precision Agriculture
@app.route('/satellite_data', methods=['GET', 'POST'])
def satellite_data():
    if request.method == 'POST':
        latitude = float(request.form.get('latitude'))
        longitude = float(request.form.get('longitude'))

        # Generate satellite data
        satellite_info = generate_satellite_data(latitude, longitude)

        return render_template('satellite_data.html',
                             satellite_data=satellite_info,
                             show_data=True)

    return render_template('satellite_data.html')

def generate_satellite_data(lat, lon):
    """Generate satellite data for given coordinates"""
    import random

    return {
        'location': {'lat': lat, 'lon': lon},
        'ndvi': round(random.uniform(0.3, 0.9), 3),
        'soil_moisture': round(random.uniform(20, 80), 1),
        'temperature': round(random.uniform(15, 35), 1),
        'vegetation_health': random.choice(['Excellent', 'Good', 'Fair', 'Poor']),
        'last_updated': '2024-01-15',
        'recommendations': [
            'Monitor irrigation levels',
            'Consider fertilizer application',
            'Check for pest activity'
        ]
    }

# Enhanced Satellite Health Viewer
@app.route('/satellite_health')
def satellite_health_viewer():
    return render_template('satellite_health.html')

@app.route('/get_satellite_data', methods=['POST'])
def get_satellite_data():
    latitude = float(request.form.get('latitude'))
    longitude = float(request.form.get('longitude'))
    start_date = request.form.get('start_date')
    end_date = request.form.get('end_date')
    index_type = request.form.get('index_type')

    # Generate enhanced satellite data
    enhanced_data = generate_enhanced_satellite_data(latitude, longitude, start_date, end_date, index_type)

    return enhanced_data

def generate_enhanced_satellite_data(lat, lon, start_date, end_date, index_type):
    """Generate enhanced satellite data with multiple indices"""
    import random
    from datetime import datetime, timedelta

    # Generate time series data
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')

    time_series = []
    current_date = start
    while current_date <= end:
        time_series.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'ndvi': round(random.uniform(0.2, 0.9), 3),
            'evi': round(random.uniform(0.1, 0.8), 3),
            'savi': round(random.uniform(0.1, 0.7), 3),
            'ndwi': round(random.uniform(-0.5, 0.5), 3),
            'moisture': round(random.uniform(15, 85), 1),
            'temperature': round(random.uniform(10, 40), 1)
        })
        current_date += timedelta(days=7)  # Weekly data

    current_indices = {
        'ndvi': round(random.uniform(0.4, 0.8), 3),
        'evi': round(random.uniform(0.3, 0.7), 3),
        'savi': round(random.uniform(0.2, 0.6), 3),
        'ndwi': round(random.uniform(-0.3, 0.3), 3)
    }

    return {
        'location': {'lat': lat, 'lon': lon},
        'current_indices': current_indices,
        'current_moisture': round(random.uniform(25, 75), 1),
        'health_status': random.choice(['Excellent', 'Good', 'Fair', 'Needs Attention']),
        'time_series': time_series,
        'index_type': index_type
    }

# Live Market Prices
@app.route('/market_prices')
def live_market_prices():
    """Enhanced market prices page with real-time data"""
    try:
        if MARKET_API_AVAILABLE and market_api:
            # Get initial market data
            market_data = market_api.get_latest_prices()
            
            # Add additional fields for template compatibility
            for item in market_data:
                item['price'] = item['current_price']
                item['unit'] = 'per quintal'
                item['state'] = item.get('state', 'Multiple States')
                item['last_updated'] = item.get('last_updated', 'Just now')
            
            return render_template('market_prices.html', 
                                 market_data=market_data, 
                                 prices=market_data,
                                 real_time=True)
        else:
            raise Exception("Market API not available")
    except Exception as e:
        print(f"Error loading market prices: {e}")
        # Fallback to sample data
        fallback_data = [
            {'crop': 'Wheat', 'current_price': 2150, 'change': 2.5, 'change_percent': 2.5, 'state': 'Delhi', 'market': 'Delhi Mandi', 'last_updated': 'Just now'},
            {'crop': 'Rice', 'current_price': 3200, 'change': -1.2, 'change_percent': -1.2, 'state': 'Punjab', 'market': 'Punjab Mandi', 'last_updated': 'Just now'},
            {'crop': 'Cotton', 'current_price': 5800, 'change': 5.8, 'change_percent': 5.8, 'state': 'Gujarat', 'market': 'Gujarat Mandi', 'last_updated': 'Just now'}
        ]
        
        for item in fallback_data:
            item['price'] = item['current_price']
            item['unit'] = 'per quintal'
        
        return render_template('market_prices.html', 
                             market_data=fallback_data, 
                             prices=fallback_data,
                             real_time=False,
                             fallback_notice=True)

# Automatic price update scheduler (background task)
import threading
import time

def background_price_updater():
    """Background task to update prices every hour"""
    while True:
        try:
            if MARKET_API_AVAILABLE and market_api:
                print("Updating market prices...")
                market_api.update_market_prices()
                print("Market prices updated successfully")
            else:
                print("Market API not available, skipping price update")
        except Exception as e:
            print(f"Background price update failed: {e}")
        
        # Wait for 1 hour (3600 seconds)
        time.sleep(3600)

# Start background price updater
price_updater_thread = threading.Thread(target=background_price_updater, daemon=True)
price_updater_thread.start()

@app.route('/create_price_alert', methods=['POST'])
def create_price_alert():
    """Create a price alert for a commodity"""
    return redirect(url_for('live_market_prices'))

# ===============================================================================================
# REAL-TIME MARKET PRICE API INTEGRATION
# ===============================================================================================

@app.route('/api/market_prices')
def api_market_prices():
    """API endpoint for real-time market prices"""
    try:
        if not MARKET_API_AVAILABLE or not market_api:
            return {
                'status': 'error',
                'message': 'Market API not available',
                'data': []
            }, 503
            
        crop_filter = request.args.get('crop', 'all')
        state_filter = request.args.get('state', 'all')
        
        # Get latest prices from API
        prices = market_api.get_latest_prices(crop_filter, state_filter)
        
        return {
            'status': 'success',
            'data': prices,
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_records': len(prices)
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': str(e),
            'data': []
        }, 500

@app.route('/api/update_market_prices', methods=['POST'])
def api_update_market_prices():
    """API endpoint to manually update market prices"""
    try:
        if not MARKET_API_AVAILABLE or not market_api:
            return {
                'status': 'error',
                'message': 'Market API not available'
            }, 503
            
        # Update market prices from external APIs
        updated_prices = market_api.update_market_prices()
        
        return {
            'status': 'success',
            'message': 'Market prices updated successfully',
            'records_updated': len(updated_prices),
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': str(e)
        }, 500

@app.route('/api/price_history/<crop_name>')
def api_price_history(crop_name):
    """API endpoint for price history of specific crop"""
    try:
        if not MARKET_API_AVAILABLE or not market_api:
            return {
                'status': 'error',
                'message': 'Market API not available',
                'data': []
            }, 503
            
        # Get price history for the last 30 days
        history_data = market_api.get_price_history(crop_name, days=30)
        
        return {
            'status': 'success',
            'crop': crop_name,
            'data': history_data
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': str(e),
            'data': []
        }, 500

# Enhanced Flask routes for market prices

@app.route('/api/market_statistics')
def api_market_statistics():
    """API endpoint for market statistics"""
    try:
        if not MARKET_API_AVAILABLE or not market_api:
            return {'status': 'error', 'message': 'Market API not available'}, 503
        
        stats = market_api.get_market_statistics()
        return {
            'status': 'success',
            'data': stats,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    except Exception as e:
        return {'status': 'error', 'message': str(e)}, 500

@app.route('/api/export_market_data')
def api_export_market_data():
    """API endpoint for exporting market data"""
    try:
        if not MARKET_API_AVAILABLE or not market_api:
            return {'status': 'error', 'message': 'Market API not available'}, 503
        
        format_type = request.args.get('format', 'csv')
        crop_filter = request.args.get('crop')
        days = request.args.get('days', 30, type=int)
        
        data = market_api.export_market_data(format_type, crop_filter, days)
        
        if format_type.lower() == 'csv':
            return Response(
                data,
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename=market_data_{datetime.now().strftime("%Y%m%d")}.csv'}
            )
        else:
            return {
                'status': 'success',
                'data': data,
                'format': format_type,
                'record_count': len(data) if isinstance(data, list) else 'N/A'
            }
    except Exception as e:
        return {'status': 'error', 'message': str(e)}, 500

@app.route('/api/price_alerts', methods=['GET', 'POST'])
def api_price_alerts():
    """API endpoint for price alerts"""
    try:
        if not MARKET_API_AVAILABLE or not market_api:
            return {'status': 'error', 'message': 'Market API not available'}, 503
        
        if request.method == 'GET':
            user_id = request.args.get('user_id')
            alerts = market_api.get_price_alerts(user_id)
            return {
                'status': 'success',
                'data': alerts,
                'count': len(alerts)
            }
        
        elif request.method == 'POST':
            data = request.get_json()
            alert_id = market_api.create_price_alert(
                crop=data.get('crop'),
                target_price=data.get('target_price'),
                alert_type=data.get('alert_type', 'above'),
                user_id=data.get('user_id')
            )
            return {
                'status': 'success',
                'alert_id': alert_id,
                'message': 'Price alert created successfully'
            }
    except Exception as e:
        return {'status': 'error', 'message': str(e)}, 500

# Add session configuration
app.secret_key = 'your-secret-key-here'

# Initialize database
def init_db():
    connection = sqlite3.connect('user_data.db')
    cursor = connection.cursor()

    # Users table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS user(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        password TEXT,
        mobile TEXT,
        email TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""")

    # Irrigation schedules table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS irrigation_schedules(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        crop_type TEXT,
        soil_type TEXT,
        location TEXT,
        schedule_date DATE,
        water_amount REAL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Farm expenses table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS farm_expenses(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        category TEXT,
        description TEXT,
        amount REAL,
        expense_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Farm activities table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS farm_activities(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        activity_type TEXT,
        crop TEXT,
        description TEXT,
        activity_date DATE,
        status TEXT DEFAULT 'planned',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Pest detection history table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS pest_detections(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        image_path TEXT,
        pest_type TEXT,
        confidence REAL,
        treatment_recommendation TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Market data table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS market_data(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        location TEXT,
        market_name TEXT,
        crop TEXT,
        price REAL,
        unit TEXT,
        date_recorded DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""")

    # Government schemes table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS government_schemes(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        scheme_name TEXT,
        description TEXT,
        eligibility TEXT,
        benefits TEXT,
        application_link TEXT,
        status TEXT DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""")

    # Community forum table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS forum_posts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        title TEXT,
        content TEXT,
        category TEXT,
        replies_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Forum replies table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS forum_replies(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        post_id INTEGER,
        user_id INTEGER,
        content TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (post_id) REFERENCES forum_posts (id),
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Notifications table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS notifications(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        title TEXT,
        message TEXT,
        type TEXT,
        read_status INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    connection.commit()
    connection.close()

# Initialize database on startup
init_db()

def init_agriculture_db():
    """Initialize agriculture database with expenses table"""
    connection = sqlite3.connect('agriculture.db')
    cursor = connection.cursor()

    # Expenses table for agriculture management
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS expenses(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        date DATE NOT NULL,
        category TEXT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""")

    connection.commit()
    connection.close()

# Initialize agriculture database
init_agriculture_db()

# Additional Routes for Testing (avoiding conflicts)
@app.route('/signup')
def signup():
    return render_template('signup.html')

@app.route('/login')
def login():
    return render_template('login.html')

@app.route('/register')
def register():
    return render_template('register.html')

@app.route('/test')
def test():
    return "<h1 style='color:red; text-align:center; margin:50px;'>FLASK TEST WORKING!</h1><p style='text-align:center;'>This means Flask is responding correctly.</p>"

@app.route('/test_page')
def test_page():
    return render_template('test_page.html')

# Intelligent crop recommendation fallback function
def get_intelligent_crop_recommendation(N, P, K, temp, hum, ph, rainfall):
    """
    Intelligent fallback for crop recommendation based on agricultural science
    """
    recommendations = []
    
    # Rice conditions
    if temp >= 20 and temp <= 35 and hum >= 70 and rainfall >= 150 and ph >= 5.5 and ph <= 7:
        recommendations.append(('rice', 85))
    
    # Wheat conditions  
    if temp >= 15 and temp <= 25 and hum >= 40 and hum <= 70 and rainfall >= 50 and rainfall <= 200 and ph >= 6 and ph <= 7.5:
        recommendations.append(('wheat', 80))
    
    # Cotton conditions
    if temp >= 21 and temp <= 30 and hum >= 50 and hum <= 80 and rainfall >= 60 and rainfall <= 120 and ph >= 5.8 and ph <= 8:
        recommendations.append(('cotton', 78))
    
    # Maize/Corn conditions
    if temp >= 21 and temp <= 30 and hum >= 60 and hum <= 80 and rainfall >= 50 and rainfall <= 200 and ph >= 6 and ph <= 7.5:
        recommendations.append(('maize', 82))
    
    # Tomato conditions
    if temp >= 20 and temp <= 30 and hum >= 60 and hum <= 80 and rainfall >= 60 and rainfall <= 150 and ph >= 6 and ph <= 7:
        recommendations.append(('tomato', 75))
    
    # Potato conditions
    if temp >= 15 and temp <= 25 and hum >= 70 and hum <= 90 and rainfall >= 80 and rainfall <= 180 and ph >= 5.5 and ph <= 7:
        recommendations.append(('potato', 77))
    
    # Apple conditions
    if temp >= 15 and temp <= 25 and hum >= 60 and hum <= 80 and rainfall >= 100 and rainfall <= 200 and ph >= 6 and ph <= 7.5:
        recommendations.append(('apple', 73))
    
    # Banana conditions
    if temp >= 26 and temp <= 35 and hum >= 75 and hum <= 85 and rainfall >= 100 and rainfall <= 150:
        recommendations.append(('banana', 70))
    
    # Additional nutrient-based recommendations
    if N >= 80 and P >= 40 and K >= 40:  # High nutrient soil
        if temp >= 25 and rainfall >= 150:
            recommendations.append(('sugarcane', 76))
    
    if recommendations:
        # Sort by confidence and return the best match
        recommendations.sort(key=lambda x: x[1], reverse=True)
        return recommendations[0][0], recommendations[0][1]
    else:
        # Default recommendation based on climate
        if temp >= 25 and rainfall >= 150:
            return 'rice', 65
        elif temp <= 25 and rainfall <= 150:
            return 'wheat', 65
        else:
            return 'maize', 60

# Model validation and safety check function
def validate_crop_model():
    """
    Validate if the crop recommendation model is working properly
    """
    try:
        if cr is not None:
            # Test with sample data
            test_data = np.array([[90, 42, 43, 20.9, 82.0, 6.5, 202.9]])
            prediction = cr.predict(test_data)
            
            # Check if prediction is valid
            valid_crops = ['apple', 'banana', 'blackgram', 'chickpea', 'coconut', 'coffee', 'cotton',
                          'grapes', 'jute', 'kidneybeans', 'lentil', 'maize', 'mango', 'mothbeans',
                          'mungbean', 'muskmelon', 'orange', 'papaya', 'pigeonpeas', 'pomegranate',
                          'rice', 'watermelon']
            
            if prediction[0] in valid_crops:
                return True, "Model working correctly"
            else:
                return False, f"Model returned invalid prediction: {prediction[0]}"
        else:
            return False, "Model not loaded"
    except Exception as e:
        return False, f"Model validation error: {str(e)}"

# Enhanced logging for crop recommendations
def log_crop_recommendation(input_data, prediction, confidence, is_fallback=False):
    """
    Log crop recommendation for monitoring and improvement
    """
    try:
        log_entry = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'input_data': input_data,
            'prediction': prediction,
            'confidence': confidence,
            'is_fallback': is_fallback,
            'model_status': 'fallback' if is_fallback else 'ml_model'
        }
        
        # You can save this to a database or log file for analysis
        print(f"Crop Recommendation Log: {log_entry}")
        
    except Exception as e:
        print(f"Logging error: {e}")

# Model status endpoint for debugging
@app.route('/model_status')
def model_status():
    """Check the status of all models"""
    from flask import jsonify
    
    status = {
        'crop_recommendation': {
            'loaded': cr is not None,
            'model_type': str(type(cr)) if cr is not None else None,
            'classes': list(cr.classes_) if cr is not None and hasattr(cr, 'classes_') else None
        },
        'fertilizer': {
            'loaded': ferti is not None,
            'model_type': str(type(ferti)) if ferti is not None else None
        },
        'disease': {
            'loaded': disease_model is not None,
            'model_type': str(type(disease_model)) if disease_model is not None else None
        },
        'yield_prediction': {
            'loaded': forest is not None,
            'model_type': str(type(forest)) if forest is not None else None
        },
        'price_prediction': {
            'loaded': cp is not None,
            'model_type': str(type(cp)) if cp is not None else None
        }
    }
    
    # Test crop model if available
    if cr is not None:
        is_valid, message = validate_crop_model()
        status['crop_recommendation']['validation'] = {
            'is_valid': is_valid,
            'message': message
        }
    
    return jsonify(status)

# Missing route handlers for templates
@app.route('/sustainable_tips')
def sustainable_tips():
    """Sustainable farming tips page"""
    return render_template('sustainable_tips.html')

@app.route('/government_schemes')
def government_schemes():
    """Government schemes page"""
    return render_template('government_schemes.html')

@app.route('/community_forum')
def community_forum():
    """Community forum page"""
    return render_template('community_forum.html', title="Community Forum")

@app.route('/notifications')
def notifications():
    """Notifications page"""
    return render_template('notifications.html', title="Notifications")

@app.route('/logout')
def logout():
    """User logout"""
    session.clear()
    return redirect(url_for('userlog'))

@app.route('/add_post', methods=['POST'])
def add_post():
    """Add a new forum post"""
    # For now, just redirect back to community forum
    # In a real implementation, this would save the post to database
    return redirect(url_for('community_forum'))

# Run the Flask app with SocketIO
if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)

